# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json
import logging

_logger = logging.getLogger(__name__)

class DashboardBlock(models.Model):
    _name = 'dashboard.block'
    _description = 'Dashboard Block'
    
    name = fields.Char(string='Name', required=True)
    dashboard_id = fields.Many2one('dashboard.dashboard', string='Dashboard', required=True, ondelete='cascade')
    type = fields.Selection([
        ('kpi', 'KPI'),
        ('chart', 'Chart'),
    ], string='Type', required=True, default='kpi')
    
    # Position and size
    width = fields.Char(string='Width', default='300px')
    height = fields.Char(string='Height', default='200px')
    data_x = fields.Float(string='X Position', default=0)
    data_y = fields.Float(string='Y Position', default=0)
    translate_x = fields.Char(string='Translate X', default='0px', compute='_compute_translate', store=True)
    translate_y = fields.Char(string='Translate Y', default='0px', compute='_compute_translate', store=True)
    
    # KPI fields
    model_name = fields.Char(string='Model Name')
    domain = fields.Char(string='Domain')
    operation = fields.Selection([
        ('count', 'Count'),
        ('sum', 'Sum'),
        ('avg', 'Average'),
        ('min', 'Minimum'),
        ('max', 'Maximum'),
    ], string='Operation', default='count')
    measured_field_id = fields.Many2one('ir.model.fields', string='Measured Field')
    
    # Chart fields
    chart_type = fields.Selection([
        ('line', 'Line'),
        ('bar', 'Bar'),
        ('pie', 'Pie'),
        ('doughnut', 'Doughnut'),
        ('radar', 'Radar'),
        ('polarArea', 'Polar Area'),
    ], string='Chart Type', default='line')
    chart_data_model = fields.Char(string='Data Model')
    chart_data_domain = fields.Char(string='Data Domain')
    chart_data_groupby = fields.Char(string='Group By')
    chart_data_measure = fields.Many2one('ir.model.fields', string='Measure')
    chart_data_limit = fields.Integer(string='Limit', default=10)
    
    # Style fields
    background_color = fields.Char(string='Background Color', default='#4e73df')
    text_color = fields.Char(string='Text Color', default='#ffffff')
    
    @api.depends('data_x', 'data_y')
    def _compute_translate(self):
        for block in self:
            block.translate_x = f"{block.data_x}px"
            block.translate_y = f"{block.data_y}px"
    
    @api.model
    def get_dashboard_blocks(self, dashboard_id):
        blocks = self.search([('dashboard_id', '=', dashboard_id)])
        result = []
        
        for block in blocks:
            block_data = {
                'id': block.id,
                'name': block.name,
                'type': block.type,
                'width': block.width,
                'height': block.height,
                'data_x': block.data_x,
                'data_y': block.data_y,
                'translate_x': block.translate_x,
                'translate_y': block.translate_y,
                'model_name': block.model_name,
                'domain': json.loads(block.domain) if block.domain else [],
                'operation': block.operation,
                'measured_field_id': block.measured_field_id.id if block.measured_field_id else False,
                'chart_type': block.chart_type,
                'background_color': block.background_color,
                'text_color': block.text_color,
            }
            
            # Get KPI value
            if block.type == 'kpi' and block.model_name:
                try:
                    result_data = self.env[block.model_name].get_query(
                        json.loads(block.domain) if block.domain else [],
                        block.operation,
                        block.measured_field_id.id if block.measured_field_id else False
                    )
                    block_data['value'] = result_data[0]['value'] if result_data else 0
                except Exception as e:
                    _logger.error(f"Error getting KPI value: {e}")
                    block_data['value'] = 0
            
            result.append(block_data)
        
        return result
    
    @api.model
    def get_query(self, domain, operation, field_id=False):
        """
        Execute a query based on the parameters
        """
        if not field_id and operation != 'count':
            raise ValidationError(_("A measured field is required for this operation"))
        
        field = self.env['ir.model.fields'].browse(field_id) if field_id else False
        model = self.env[self.model_name]
        
        if operation == 'count':
            value = model.search_count(domain)
            return [{'value': value}]
        
        if operation == 'sum':
            result = model.read_group(domain, [field.name], [])[0]
            return [{'value': result.get(field.name, 0)}]
        
        if operation == 'avg':
            result = model.read_group(domain, [field.name], [])[0]
            count = result.get('__count', 1)
            total = result.get(field.name, 0)
            return [{'value': total / count if count else 0}]
        
        if operation == 'min':
            records = model.search(domain, limit=1, order=f"{field.name} ASC")
            return [{'value': records[field.name] if records else 0}]
        
        if operation == 'max':
            records = model.search(domain, limit=1, order=f"{field.name} DESC")
            return [{'value': records[field.name] if records else 0}]
        
        return [{'value': 0}]

