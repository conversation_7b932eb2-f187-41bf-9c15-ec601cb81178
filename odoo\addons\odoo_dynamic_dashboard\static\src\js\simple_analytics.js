/** @odoo-module **/
// TEMPORARY FILE - NOT USED ANYMORE
// The original analytics_dashboard.js is restored and working

/*
import { Component, useState, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { registry } from "@web/core/registry";

class SimpleAnalyticsDashboard extends Component {
    static template = "SimpleAnalyticsDashboard";

    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");

        this.state = useState({
            loading: true,
            error: null,
            dashboardType: this.props.action?.params?.dashboard_type || 'overview',
            title: this.getDashboardTitle(),
            widgets: [],
            charts: []
        });

        onMounted(() => {
            this.loadData();
        });
    }

    getDashboardTitle() {
        const titles = {
            'overview': '📈 Dashboard Overview',
            'sales': '💰 Sales Analytics',
            'production': '🏭 Production Analytics',
            'stock': '📦 Stock Analytics',
            'maintenance': '🔧 Maintenance Analytics',
            'inventory': '📋 Inventory Analytics',
            'workshop': '🔨 Workshop Analytics',
            'manufacturing': '⚙️ Manufacturing Analytics',
            'ai_assistant': '🤖 AI Assistant (Coming Soon)'
        };
        return titles[this.state.dashboardType] || '📊 Analytics Dashboard';
    }

    async loadData() {
        this.state.loading = true;
        this.state.error = null;

        try {
            // Simulate loading data
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Special handling for AI Assistant
            if (this.state.dashboardType === 'ai_assistant') {
                this.state.widgets = [
                    {
                        title: "AI Status",
                        value: "Coming Soon",
                        trend: "",
                        subtitle: "In development",
                        color: "info",
                        icon: "fa-robot"
                    }
                ];
                this.state.charts = [];
                this.state.loading = false;
                return;
            }

            // Mock data for testing
            this.state.widgets = [
                {
                    title: "Total Sales",
                    value: "€125,430",
                    trend: "+12%",
                    subtitle: "vs last month",
                    color: "primary",
                    icon: "fa-dollar-sign"
                },
                {
                    title: "Orders",
                    value: "1,234",
                    trend: "+8%",
                    subtitle: "this month",
                    color: "success",
                    icon: "fa-shopping-cart"
                },
                {
                    title: "Products",
                    value: "567",
                    trend: "+3%",
                    subtitle: "in stock",
                    color: "info",
                    icon: "fa-box"
                },
                {
                    title: "Customers",
                    value: "89",
                    trend: "+15%",
                    subtitle: "active",
                    color: "warning",
                    icon: "fa-users"
                }
            ];

            this.state.charts = [
                {
                    title: "Sales Trend",
                    type: "line",
                    size: "col-lg-6"
                },
                {
                    title: "Product Categories",
                    type: "doughnut",
                    size: "col-lg-6"
                }
            ];

            this.state.loading = false;

        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.state.error = error.message || 'Failed to load dashboard data';
            this.state.loading = false;
        }
    }

    async refreshData() {
        this.notification.add("Refreshing dashboard...", {
            type: "info"
        });
        await this.loadData();
        this.notification.add("Dashboard refreshed!", {
            type: "success"
        });
    }
}

// Register the component
// registry.category("actions").add("simple_analytics_dashboard", SimpleAnalyticsDashboard);
*/
