<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Dashboard Menu -->
    <menuitem id="menu_dashboard_root"
              name="Dashboards"
              web_icon="odoo_dynamic_dashboard,static/description/icon.png"
              sequence="100"/>

    <menuitem id="menu_dashboard_main"
              name="Dashboards"
              parent="menu_dashboard_root"
              action="action_dashboard"
              sequence="10"/>

    <menuitem id="menu_dashboard_view"
              name="View Dashboard"
              parent="menu_dashboard_root"
              action="action_dashboard_client"
              sequence="5"/>

    <menuitem id="menu_dashboard_block"
              name="Dashboard Blocks"
              parent="menu_dashboard_root"
              action="action_dashboard_block"
              sequence="20"/>

    <menuitem id="menu_dashboard_theme"
              name="Dashboard Themes"
              parent="menu_dashboard_root"
              action="action_dashboard_theme"
              sequence="30"/>

    <!-- Dashboard Menu Views -->
    <record id="view_dashboard_menu_form" model="ir.ui.view">
        <field name="name">dashboard.menu.form</field>
        <field name="model">dashboard.menu</field>
        <field name="arch" type="xml">
            <form string="Dashboard Menu">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Menu Name"/>
                        </h1>
                    </div>
                    <group>
                        <field name="menu_id"/>
                        <field name="client_action_id" readonly="1"/>
                        <field name="group_ids" widget="many2many_tags"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_dashboard_menu_tree" model="ir.ui.view">
        <field name="name">dashboard.menu.tree</field>
        <field name="model">dashboard.menu</field>
        <field name="arch" type="xml">
            <tree string="Dashboard Menus">
                <field name="name"/>
                <field name="menu_id"/>
            </tree>
        </field>
    </record>

    <record id="action_dashboard_menu" model="ir.actions.act_window">
        <field name="name">Dashboard Menus</field>
        <field name="res_model">dashboard.menu</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_dashboard_menu_config"
              name="Dashboard Menus"
              parent="menu_dashboard_root"
              action="action_dashboard_menu"
              sequence="40"/>
</odoo>

