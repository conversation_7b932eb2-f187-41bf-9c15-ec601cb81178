/* Analytics Dashboard Styles */

.analytics_dashboard_container {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background-color: #f8f9fc;
    max-height: calc(100vh - 100px);
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px;
    box-sizing: border-box;
}

.analytics_header h2 {
    color: #5a5c69;
    font-weight: 600;
    font-size: 1.75rem;
}

/* Natural scrollable content */
.analytics_dashboard_container .row {
    margin-bottom: 30px;
}

.analytics_dashboard_container .card {
    margin-bottom: 20px;
}

/* KPI Widget Cards - smaller size */
.widget_card {
    margin-bottom: 20px;
    min-height: auto !important;
    height: auto !important;
}

.widget_card .card-body {
    padding: 1rem !important;
    min-height: auto !important;
    height: auto !important;
}

.widget_card .card {
    min-height: auto !important;
    height: auto !important;
}

/* Chart Cards - normal size for charts */
.chart_card {
    margin-bottom: 25px;
}

/* Chart containers */
.chart-container {
    min-height: 300px;
    padding: 15px;
}

/* Ensure content flows naturally */
.analytics_dashboard_container {
    display: block;
}

.analytics_header h2 i {
    margin-right: 0.5rem;
    color: #858796;
}

/* KPI Cards */
.card.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.card.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.card.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.card.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.card.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.card.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-danger {
    color: #e74a3b !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Buttons */
.btn-outline-primary {
    color: #4e73df;
    border-color: #4e73df;
}

.btn-outline-primary:hover {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* Loading State */
.fa-spinner {
    color: #858796;
}

/* Empty State */
.text-muted {
    color: #858796 !important;
}

/* Chart Containers */
.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.card-header h6 {
    color: #5a5c69;
}

/* Responsive Design */
@media (max-width: 768px) {
    .analytics_header {
        text-align: center;
    }

    .analytics_controls {
        margin-top: 1rem;
    }

    .analytics_header .d-flex {
        flex-direction: column;
    }
}

/* Animation */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* AI Assistant Floating Button */
.ai_floating_button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 24px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai_floating_button:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
    color: white;
}

.ai_floating_button:active {
    transform: scale(0.95);
}

.ai_floating_button .fa-robot {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Update responsive design */
@media (max-width: 768px) {
    .analytics_header {
        text-align: center;
    }

    .analytics_controls {
        margin-top: 1rem;
        flex-direction: column;
        gap: 10px;
    }

    .analytics_controls .btn {
        width: 100%;
    }

    .analytics_header .d-flex {
        flex-direction: column;
    }

    .ai_floating_button {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* Dashboard Summary Styles */
.dashboard-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.insight-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f8f9fc;
    border-radius: 8px;
    border-left: 4px solid #4e73df;
}

.insight-item i {
    font-size: 1.2rem;
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.insight-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: #5a5c69;
}

.dashboard-header h1 {
    font-size: 1.75rem;
    font-weight: 400;
}

.dashboard-header p {
    font-size: 1rem;
    margin-bottom: 0;
}

/* Additional border colors for dashboard cards */
.border-left-secondary {
    border-left: 0.25rem solid #858796 !important;
}

.border-left-dark {
    border-left: 0.25rem solid #5a5c69 !important;
}

/* Quick actions buttons */
.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
    transform: translateY(-2px);
    transition: all 0.2s ease;
}

/* Chart containers - Fixed heights for consistency */
.chart_card .card,
.analytics_dashboard_container .card {
    height: 350px;
    margin-bottom: 1.5rem;
}

.chart_card .card-body,
.analytics_dashboard_container .card-body {
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
}

.chart_card canvas {
    flex: 1;
    max-height: 280px;
}

/* Widget cards - Consistent heights */
.widget_card .card,
.analytics_dashboard_container .col-xl-3 .card,
.analytics_dashboard_container .col-md-6 .card {
    height: 120px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.widget_card .card-body,
.analytics_dashboard_container .col-xl-3 .card-body,
.analytics_dashboard_container .col-md-6 .card-body {
    padding: 1.25rem;
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
}

/* Dashboard grid layout */
.analytics-dashboard-container .row,
.simple-analytics-dashboard .row {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
}

.analytics-dashboard-container .col-lg-3,
.analytics-dashboard-container .col-lg-6,
.analytics-dashboard-container .col-lg-12,
.simple-analytics-dashboard .col-xl-3,
.simple-analytics-dashboard .col-md-6,
.simple-analytics-dashboard .col-lg-6 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    margin-bottom: 1.5rem;
}

/* Ensure equal heights in rows */
.dashboard-row,
.simple-analytics-dashboard .row {
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
}

.dashboard-row > [class*="col-"],
.simple-analytics-dashboard .row > [class*="col-"] {
    display: flex;
    flex-direction: column;
}

.dashboard-row .card,
.simple-analytics-dashboard .card {
    flex: 1;
    height: 100%;
}

/* Simple Analytics Dashboard Specific Styles */
.simple-analytics-dashboard {
    background-color: #f8f9fc;
    min-height: 100vh;
}

.simple-analytics-dashboard .card {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s;
}

.simple-analytics-dashboard .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.simple-analytics-dashboard .border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.simple-analytics-dashboard .border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.simple-analytics-dashboard .border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.simple-analytics-dashboard .border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.simple-analytics-dashboard .text-primary {
    color: #4e73df !important;
}

.simple-analytics-dashboard .text-success {
    color: #1cc88a !important;
}

.simple-analytics-dashboard .text-info {
    color: #36b9cc !important;
}

.simple-analytics-dashboard .text-warning {
    color: #f6c23e !important;
}

.simple-analytics-dashboard .text-gray-800 {
    color: #5a5c69 !important;
}

.simple-analytics-dashboard .text-gray-300 {
    color: #dddfeb !important;
}

.simple-analytics-dashboard .text-gray-600 {
    color: #858796 !important;
}

/* Responsive adjustments for summary */
@media (max-width: 768px) {
    .dashboard-header h1 {
        font-size: 1.5rem;
    }

    .dashboard-header .col-md-4 {
        text-align: left !important;
        margin-top: 15px;
    }

    .insight-item {
        margin-bottom: 10px;
    }

    .dashboard-card {
        margin-bottom: 1rem;
    }
}