<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Analytics Dashboard Views -->

    <!-- List View -->
    <record id="view_analytics_dashboard_list" model="ir.ui.view">
        <field name="name">analytics.dashboard.list</field>
        <field name="model">analytics.dashboard</field>
        <field name="arch" type="xml">
            <list string="Analytics Dashboards">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="dashboard_type"/>
                <field name="active"/>
                <field name="auto_refresh"/>
                <field name="refresh_interval"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_analytics_dashboard_form" model="ir.ui.view">
        <field name="name">analytics.dashboard.form</field>
        <field name="model">analytics.dashboard</field>
        <field name="arch" type="xml">
            <form string="Analytics Dashboard">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Dashboard Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="dashboard_type"/>
                            <field name="sequence"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="auto_refresh"/>
                            <field name="refresh_interval" invisible="auto_refresh == False"/>
                        </group>
                    </group>
                    <group string="Description">
                        <field name="description" nolabel="1"/>
                    </group>
                    <group string="Access Control">
                        <field name="user_ids" widget="many2many_tags"/>
                        <field name="group_ids" widget="many2many_tags"/>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_analytics_dashboard_search" model="ir.ui.view">
        <field name="name">analytics.dashboard.search</field>
        <field name="model">analytics.dashboard</field>
        <field name="arch" type="xml">
            <search string="Analytics Dashboards">
                <field name="name"/>
                <field name="dashboard_type"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Auto Refresh" name="auto_refresh" domain="[('auto_refresh', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Dashboard Type" name="group_dashboard_type" context="{'group_by': 'dashboard_type'}"/>
                    <filter string="Active" name="group_active" context="{'group_by': 'active'}"/>
                </group>
            </search>
        </field>
    </record>
</odoo>
