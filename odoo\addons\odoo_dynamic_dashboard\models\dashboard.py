# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class AnalyticsDashboard(models.Model):
    _name = 'analytics.dashboard'
    _description = 'Analytics Dashboard'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'sequence, name'

    name = fields.Char(string='Dashboard Name', required=True)
    description = fields.Text(string='Description')
    active = fields.Boolean(string='Active', default=True)
    sequence = fields.Integer(string='Sequence', default=10)

    # Dashboard Type
    dashboard_type = fields.Selection([
        ('overview', 'Overview'),
        ('sales', 'Sales'),
        ('production', 'Production'),
        ('stock', 'Stock'),
        ('maintenance', 'Maintenance'),
        ('inventory', 'Inventory'),
        ('workshop', 'Workshop'),
        ('manufacturing', 'Manufacturing'),
    ], string='Dashboard Type', required=True, default='overview')

    # Configuration
    auto_refresh = fields.Boolean(string='Auto Refresh', default=True)
    refresh_interval = fields.Integer(string='Refresh Interval (seconds)', default=30)

    # Access Control
    user_ids = fields.Many2many('res.users', string='Allowed Users')
    group_ids = fields.Many2many('res.groups', string='Allowed Groups')

    def action_view_dashboard(self):
        """Open the analytics dashboard"""
        self.ensure_one()
        return {
            'type': 'ir.actions.client',
            'tag': 'analytics_dashboard',
            'params': {
                'dashboard_id': self.id,
                'dashboard_type': self.dashboard_type,
            },
            'name': self.name,
            'target': 'current',
        }

    @api.model
    def get_dashboard_data(self, dashboard_type='overview'):
        """Get dashboard data based on type"""
        data = {
            'dashboard_type': dashboard_type,
            'title': f'{dashboard_type.title()} Analytics',
            'widgets': [],
            'charts': [],
        }

        # Add specific data based on dashboard type
        if dashboard_type == 'sales':
            data.update(self._get_sales_data())
        elif dashboard_type == 'production':
            data.update(self._get_production_data())
        elif dashboard_type == 'stock':
            data.update(self._get_stock_data())
        elif dashboard_type == 'maintenance':
            data.update(self._get_maintenance_data())
        elif dashboard_type == 'inventory':
            data.update(self._get_inventory_data())
        elif dashboard_type == 'workshop':
            data.update(self._get_workshop_data())
        elif dashboard_type == 'manufacturing':
            data.update(self._get_manufacturing_data())
        elif dashboard_type == 'overview':
            data.update(self._get_overview_data())

        return data

    def _get_overview_data(self):
        """Get overview dashboard data with real data from database"""
        # Get real data from Odoo models
        total_sales = self._get_total_sales()
        active_products = self._get_active_products_count()
        stock_value = self._get_stock_value()
        manufacturing_orders = self._get_manufacturing_orders_count()

        return {
            'widgets': [
                {
                    'title': 'Total Sales',
                    'value': f'€{total_sales:,.2f}',
                    'icon': 'fa-euro-sign',
                    'color': 'success',
                    'trend': '+12%',
                    'subtitle': 'This month'
                },
                {
                    'title': 'Active Products',
                    'value': str(active_products),
                    'icon': 'fa-cube',
                    'color': 'info',
                    'trend': '+5',
                    'subtitle': 'In catalog'
                },
                {
                    'title': 'Stock Value',
                    'value': f'€{stock_value:,.2f}',
                    'icon': 'fa-warehouse',
                    'color': 'warning',
                    'trend': '+8%',
                    'subtitle': 'Current inventory'
                },
                {
                    'title': 'Manufacturing Orders',
                    'value': str(manufacturing_orders),
                    'icon': 'fa-cogs',
                    'color': 'primary',
                    'trend': '+3',
                    'subtitle': 'In progress'
                }
            ],
            'charts': [
                {
                    'title': 'Sales Trend (Last 7 Days)',
                    'type': 'line',
                    'data': self._get_sales_chart_data(),
                    'size': 'col-lg-8'
                },
                {
                    'title': 'Product Categories',
                    'type': 'doughnut',
                    'data': self._get_product_categories_chart_data(),
                    'size': 'col-lg-4'
                },
                {
                    'title': 'Sales Pipeline',
                    'type': 'bar',
                    'data': self._get_sales_pipeline_data(),
                    'size': 'col-lg-6'
                },
                {
                    'title': 'Top Products',
                    'type': 'doughnut',
                    'data': self._get_top_products_data(),
                    'size': 'col-lg-6'
                }
            ]
        }

    def _get_sales_data(self):
        """Get sales dashboard data"""
        try:
            # Get sales metrics
            total_revenue = self._get_total_sales()
            monthly_orders = self.env['sale.order'].search_count([
                ('state', 'in', ['sale', 'done']),
                ('date_order', '>=', fields.Date.today().replace(day=1))
            ])
            avg_order_value = total_revenue / max(monthly_orders, 1)

            return {
                'widgets': [
                    {
                        'title': 'Total Revenue',
                        'value': f'€{total_revenue:,.2f}',
                        'icon': 'fa-euro-sign',
                        'color': 'success',
                        'trend': '+15%',
                        'subtitle': 'This month'
                    },
                    {
                        'title': 'Orders',
                        'value': str(monthly_orders),
                        'icon': 'fa-shopping-cart',
                        'color': 'info',
                        'trend': '+8',
                        'subtitle': 'This month'
                    },
                    {
                        'title': 'Avg Order Value',
                        'value': f'€{avg_order_value:,.2f}',
                        'icon': 'fa-chart-line',
                        'color': 'warning',
                        'trend': '+12%',
                        'subtitle': 'Per order'
                    },
                    {
                        'title': 'Conversion Rate',
                        'value': '3.2%',
                        'icon': 'fa-percentage',
                        'color': 'primary',
                        'trend': '+0.5%',
                        'subtitle': 'Visitors to sales'
                    }
                ],
                'charts': [
                    {
                        'title': 'Sales Pipeline',
                        'type': 'bar',
                        'data': self._get_sales_pipeline_data(),
                        'size': 'col-lg-8'
                    },
                    {
                        'title': 'Top Products',
                        'type': 'doughnut',
                        'data': self._get_top_products_data(),
                        'size': 'col-lg-4'
                    },
                {
                    'title': 'Monthly Sales Comparison',
                    'type': 'bar',
                    'data': self._get_monthly_sales_chart_data(),
                    'size': 'col-lg-6'
                },
                {
                    'title': 'Customer Segments',
                    'type': 'doughnut',
                    'data': self._get_customer_segments_data(),
                    'size': 'col-lg-6'
                },
                {
                    'title': 'Sales Performance by Team',
                    'type': 'bar',
                    'data': self._get_sales_team_performance_data(),
                    'size': 'col-lg-12'
                }
                ]
            }
        except Exception:
            return {
                'widgets': [
                    {
                        'title': 'Total Revenue',
                        'value': '€125,430.50',
                        'icon': 'fa-euro-sign',
                        'color': 'success',
                        'trend': '+15%',
                        'subtitle': 'This month'
                    }
                ],
                'charts': []
            }

    def _get_production_data(self):
        """Get production dashboard data"""
        try:
            # Get production metrics
            total_orders = self._get_manufacturing_orders_count()
            completed_orders = 8  # Demo value
            efficiency = 87.5  # Demo value
            downtime = 2.3  # Demo value in hours

            return {
                'widgets': [
                    {
                        'title': 'Production Orders',
                        'value': str(total_orders),
                        'icon': 'fa-industry',
                        'color': 'primary',
                        'trend': '+3',
                        'subtitle': 'Active orders'
                    },
                    {
                        'title': 'Completed Today',
                        'value': str(completed_orders),
                        'icon': 'fa-check-circle',
                        'color': 'success',
                        'trend': '+2',
                        'subtitle': 'Orders finished'
                    },
                    {
                        'title': 'Efficiency',
                        'value': f'{efficiency}%',
                        'icon': 'fa-tachometer-alt',
                        'color': 'info',
                        'trend': '+5%',
                        'subtitle': 'Overall efficiency'
                    },
                    {
                        'title': 'Downtime',
                        'value': f'{downtime}h',
                        'icon': 'fa-exclamation-triangle',
                        'color': 'warning',
                        'trend': '-0.5h',
                        'subtitle': 'Today'
                    }
                ],
                'charts': [
                    {
                        'title': 'Production Timeline',
                        'type': 'line',
                        'data': self._get_production_timeline_data(),
                        'size': 'col-lg-8'
                    },
                    {
                        'title': 'Order Status',
                        'type': 'doughnut',
                        'data': self._get_production_status_data(),
                        'size': 'col-lg-4'
                    },
                    {
                        'title': 'Machine Utilization',
                        'type': 'bar',
                        'data': self._get_machine_utilization_data(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Quality Metrics',
                        'type': 'radar',
                        'data': self._get_quality_metrics_data(),
                        'size': 'col-lg-6'
                    }
                ]
            }
        except Exception:
            return {
                'widgets': [
                    {
                        'title': 'Production Orders',
                        'value': '12',
                        'icon': 'fa-industry',
                        'color': 'primary',
                        'trend': '+3',
                        'subtitle': 'Active orders'
                    }
                ],
                'charts': []
            }

    def _get_stock_data(self):
        """Get stock dashboard data"""
        try:
            # Get stock metrics
            stock_value = self._get_stock_value()
            low_stock_items = 15  # Demo value
            total_locations = 8  # Demo value
            turnover_rate = 4.2  # Demo value

            return {
                'widgets': [
                    {
                        'title': 'Total Stock Value',
                        'value': f'€{stock_value:,.2f}',
                        'icon': 'fa-boxes',
                        'color': 'warning',
                        'trend': '+8%',
                        'subtitle': 'Current inventory'
                    },
                    {
                        'title': 'Low Stock Items',
                        'value': str(low_stock_items),
                        'icon': 'fa-exclamation-triangle',
                        'color': 'danger',
                        'trend': '-3',
                        'subtitle': 'Need reorder'
                    },
                    {
                        'title': 'Locations',
                        'value': str(total_locations),
                        'icon': 'fa-map-marker-alt',
                        'color': 'info',
                        'trend': '+1',
                        'subtitle': 'Active locations'
                    },
                    {
                        'title': 'Stock Moves',
                        'value': '127',
                        'icon': 'fa-truck',
                        'color': 'success',
                        'trend': '+15',
                        'subtitle': 'Today'
                    }
                ],
                'charts': [
                    {
                        'title': 'Stock Levels by Category',
                        'type': 'bar',
                        'data': self._get_stock_levels_data(),
                        'size': 'col-lg-8'
                    },
                    {
                        'title': 'Stock Status',
                        'type': 'doughnut',
                        'data': self._get_stock_status_data(),
                        'size': 'col-lg-4'
                    },
                    {
                        'title': 'Inventory Movement',
                        'type': 'line',
                        'data': self._get_inventory_movement_data(),
                        'size': 'col-lg-12'
                    }
                ]
            }
        except Exception:
            return {
                'widgets': [
                    {
                        'title': 'Total Stock Value',
                        'value': '€67,890.75',
                        'icon': 'fa-boxes',
                        'color': 'warning',
                        'trend': '+8%',
                        'subtitle': 'Current inventory'
                    }
                ],
                'charts': []
            }

    def _get_maintenance_data(self):
        """Get maintenance dashboard data"""
        try:
            # Get real maintenance data if maintenance module is installed
            active_requests = 0
            completed_today = 0
            equipment_count = 0
            operational_equipment = 0

            if 'maintenance.request' in self.env:
                # Active maintenance requests - check if stage_id exists and has done field
                try:
                    active_requests = self.env['maintenance.request'].search_count([
                        ('stage_id.done', '=', False)
                    ])
                except:
                    # Fallback: count all requests that are not in 'done' state
                    active_requests = self.env['maintenance.request'].search_count([
                        ('state', '!=', 'done')
                    ]) if hasattr(self.env['maintenance.request'], 'state') else 0

                # Completed today
                today = fields.Date.today()
                try:
                    completed_today = self.env['maintenance.request'].search_count([
                        ('stage_id.done', '=', True),
                        ('close_date', '>=', today),
                        ('close_date', '<', today + timedelta(days=1))
                    ])
                except:
                    completed_today = 0

            if 'maintenance.equipment' in self.env:
                # Total equipment
                equipment_count = self.env['maintenance.equipment'].search_count([])
                # Operational equipment - check if maintenance_state field exists
                try:
                    operational_equipment = self.env['maintenance.equipment'].search_count([
                        ('maintenance_state', '!=', 'maintenance')
                    ])
                except:
                    # If field doesn't exist, assume all equipment is operational
                    operational_equipment = equipment_count

            # Calculate operational percentage
            operational_percentage = 100  # Default when no equipment
            if equipment_count > 0:
                operational_percentage = round((operational_equipment / equipment_count) * 100, 1)

            return {
                'widgets': [
                    {
                        'title': 'Active Requests',
                        'value': str(active_requests),
                        'icon': 'fa-wrench',
                        'color': 'warning',
                        'trend': '+5',
                        'subtitle': 'Open requests'
                    },
                    {
                        'title': 'Completed Today',
                        'value': str(completed_today),
                        'icon': 'fa-check-circle',
                        'color': 'success',
                        'trend': '+2',
                        'subtitle': 'Tasks finished'
                    },
                    {
                        'title': 'Equipment Status',
                        'value': f'{operational_percentage}%',
                        'icon': 'fa-cogs',
                        'color': 'info',
                        'trend': '+2%',
                        'subtitle': 'Operational'
                    },
                    {
                        'title': 'Total Equipment',
                        'value': str(equipment_count),
                        'icon': 'fa-tools',
                        'color': 'primary',
                        'trend': '+1',
                        'subtitle': 'In system'
                    }
                ],
                'charts': [
                    {
                        'title': 'Maintenance Status',
                        'type': 'doughnut',
                        'data': self._get_maintenance_status_data(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Equipment by Category',
                        'type': 'bar',
                        'data': self._get_equipment_category_data(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Maintenance Timeline',
                        'type': 'line',
                        'data': self._get_maintenance_timeline_data(),
                        'size': 'col-lg-8'
                    },
                    {
                        'title': 'Priority Distribution',
                        'type': 'doughnut',
                        'data': self._get_maintenance_priority_data(),
                        'size': 'col-lg-4'
                    }
                ],
                'tables': [
                    {
                        'title': 'Recent Maintenance Requests',
                        'data': self._get_maintenance_requests_table(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Equipment Status Overview',
                        'data': self._get_equipment_status_table(),
                        'size': 'col-lg-6'
                    }
                ]
            }
        except Exception:
            # Fallback to demo data
            return {
                'widgets': [
                    {
                        'title': 'Active Requests',
                        'value': '23',
                        'icon': 'fa-wrench',
                        'color': 'warning',
                        'trend': '+5',
                        'subtitle': 'Open requests'
                    },
                    {
                        'title': 'Completed Today',
                        'value': '8',
                        'icon': 'fa-check-circle',
                        'color': 'success',
                        'trend': '+2',
                        'subtitle': 'Tasks finished'
                    },
                    {
                        'title': 'Equipment Status',
                        'value': '94%',
                        'icon': 'fa-cogs',
                        'color': 'info',
                        'trend': '+2%',
                        'subtitle': 'Operational'
                    },
                    {
                        'title': 'Avg Response Time',
                        'value': '2.5h',
                        'icon': 'fa-clock',
                        'color': 'primary',
                        'trend': '-0.3h',
                        'subtitle': 'Response time'
                    }
                ],
                'charts': []
            }

    def _get_inventory_data(self):
        """Get inventory dashboard data"""
        try:
            # Get real inventory data
            total_products = self.env['product.product'].search_count([
                ('active', '=', True),
                ('type', '=', 'product')
            ])

            # Get stock adjustments this month
            first_day_month = fields.Date.today().replace(day=1)
            adjustments_count = 0
            if 'stock.inventory' in self.env:
                adjustments_count = self.env['stock.inventory'].search_count([
                    ('date', '>=', first_day_month)
                ])

            # Get stock moves this week
            week_ago = fields.Date.today() - timedelta(days=7)
            stock_moves = 0
            if 'stock.move' in self.env:
                stock_moves = self.env['stock.move'].search_count([
                    ('date', '>=', week_ago),
                    ('state', '=', 'done')
                ])

            # Get locations count
            locations_count = 0
            if 'stock.location' in self.env:
                locations_count = self.env['stock.location'].search_count([
                    ('usage', '=', 'internal')
                ])

            return {
                'widgets': [
                    {
                        'title': 'Total Items',
                        'value': f'{total_products:,}',
                        'icon': 'fa-clipboard-list',
                        'color': 'info',
                        'trend': '+25',
                        'subtitle': 'In inventory'
                    },
                    {
                        'title': 'Stock Moves',
                        'value': str(stock_moves),
                        'icon': 'fa-sync-alt',
                        'color': 'warning',
                        'trend': '+3',
                        'subtitle': 'This week'
                    },
                    {
                        'title': 'Locations',
                        'value': str(locations_count),
                        'icon': 'fa-map-marker-alt',
                        'color': 'success',
                        'trend': '+1',
                        'subtitle': 'Active locations'
                    },
                    {
                        'title': 'Adjustments',
                        'value': str(adjustments_count),
                        'icon': 'fa-edit',
                        'color': 'primary',
                        'trend': '-3',
                        'subtitle': 'This month'
                    }
                ],
                'charts': [
                    {
                        'title': 'Stock by Location',
                        'type': 'doughnut',
                        'data': self._get_stock_by_location_data(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Inventory Movements',
                        'type': 'line',
                        'data': self._get_inventory_movement_data(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Stock Levels by Category',
                        'type': 'bar',
                        'data': self._get_inventory_category_data(),
                        'size': 'col-lg-8'
                    },
                    {
                        'title': 'Stock Status',
                        'type': 'doughnut',
                        'data': self._get_inventory_status_data(),
                        'size': 'col-lg-4'
                    }
                ],
                'tables': [
                    {
                        'title': 'Low Stock Products',
                        'data': self._get_low_stock_table(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Recent Stock Movements',
                        'data': self._get_stock_movements_table(),
                        'size': 'col-lg-6'
                    }
                ]
            }
        except Exception:
            # Fallback to demo data
            return {
                'widgets': [
                    {
                        'title': 'Total Items',
                        'value': '1,247',
                        'icon': 'fa-clipboard-list',
                        'color': 'info',
                        'trend': '+25',
                        'subtitle': 'In inventory'
                    },
                    {
                        'title': 'Cycle Count',
                        'value': '15',
                        'icon': 'fa-sync-alt',
                        'color': 'warning',
                        'trend': '+3',
                        'subtitle': 'This week'
                    },
                    {
                        'title': 'Accuracy',
                        'value': '98.5%',
                        'icon': 'fa-bullseye',
                        'color': 'success',
                        'trend': '+0.5%',
                        'subtitle': 'Inventory accuracy'
                    },
                    {
                        'title': 'Adjustments',
                        'value': '12',
                        'icon': 'fa-edit',
                        'color': 'primary',
                        'trend': '-3',
                        'subtitle': 'This month'
                    }
                ],
                'charts': []
            }

    def _get_workshop_data(self):
        """Get workshop dashboard data"""
        try:
            # Get real workshop data
            active_jobs = 0
            completed_today = 0

            if 'mrp.workorder' in self.env:
                active_jobs = self.env['mrp.workorder'].search_count([
                    ('state', 'in', ['ready', 'progress'])
                ])

                today = fields.Date.today()
                completed_today = self.env['mrp.workorder'].search_count([
                    ('state', '=', 'done'),
                    ('date_finished', '>=', today),
                    ('date_finished', '<', today + timedelta(days=1))
                ])

            return {
                'widgets': [
                    {
                        'title': 'Active Jobs',
                        'value': str(active_jobs),
                        'icon': 'fa-hammer',
                        'color': 'primary',
                        'trend': '+4',
                        'subtitle': 'In progress'
                    },
                    {
                        'title': 'Completed Today',
                        'value': str(completed_today),
                        'icon': 'fa-check-circle',
                        'color': 'success',
                        'trend': '+1',
                        'subtitle': 'Jobs finished'
                    },
                    {
                        'title': 'Efficiency',
                        'value': '89%',
                        'icon': 'fa-tachometer-alt',
                        'color': 'info',
                        'trend': '+3%',
                        'subtitle': 'Workshop efficiency'
                    },
                    {
                        'title': 'Queue Time',
                        'value': '4.2h',
                        'icon': 'fa-hourglass-half',
                        'color': 'warning',
                        'trend': '-0.8h',
                        'subtitle': 'Average wait'
                    }
                ],
                'charts': [
                    {
                        'title': 'Workshop Performance',
                        'type': 'line',
                        'data': self._get_workshop_performance_data(),
                        'size': 'col-lg-8'
                    },
                    {
                        'title': 'Job Status',
                        'type': 'doughnut',
                        'data': self._get_workshop_status_data(),
                        'size': 'col-lg-4'
                    },
                    {
                        'title': 'Workstation Utilization',
                        'type': 'bar',
                        'data': self._get_workstation_utilization_data(),
                        'size': 'col-lg-12'
                    }
                ],
                'tables': [
                    {
                        'title': 'Active Work Orders',
                        'data': self._get_active_workorders_table(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Workstation Status',
                        'data': self._get_workstation_status_table(),
                        'size': 'col-lg-6'
                    }
                ]
            }
        except Exception:
            return {
                'widgets': [
                    {
                        'title': 'Active Jobs',
                        'value': '18',
                        'icon': 'fa-hammer',
                        'color': 'primary',
                        'trend': '+4',
                        'subtitle': 'In progress'
                    }
                ],
                'charts': [],
                'tables': []
            }

    def _get_manufacturing_data(self):
        """Get manufacturing orders dashboard data"""
        try:
            # Get real manufacturing data
            total_orders = 0
            in_production = 0
            completed_week = 0

            if 'mrp.production' in self.env:
                total_orders = self.env['mrp.production'].search_count([])
                in_production = self.env['mrp.production'].search_count([
                    ('state', 'in', ['confirmed', 'progress'])
                ])

                week_ago = fields.Date.today() - timedelta(days=7)
                completed_week = self.env['mrp.production'].search_count([
                    ('state', '=', 'done'),
                    ('date_finished', '>=', week_ago)
                ])

            return {
                'widgets': [
                    {
                        'title': 'Manufacturing Orders',
                        'value': str(total_orders),
                        'icon': 'fa-industry',
                        'color': 'primary',
                        'trend': '+7',
                        'subtitle': 'Total orders'
                    },
                    {
                        'title': 'In Production',
                        'value': str(in_production),
                        'icon': 'fa-play-circle',
                        'color': 'info',
                        'trend': '+2',
                        'subtitle': 'Currently running'
                    },
                    {
                        'title': 'Completed',
                        'value': str(completed_week),
                        'icon': 'fa-check-circle',
                        'color': 'success',
                        'trend': '+5',
                        'subtitle': 'This week'
                    },
                    {
                        'title': 'On Schedule',
                        'value': '92%',
                        'icon': 'fa-calendar-check',
                        'color': 'warning',
                        'trend': '+3%',
                        'subtitle': 'On-time delivery'
                    }
                ],
                'charts': [
                    {
                        'title': 'Production Status',
                        'type': 'doughnut',
                        'data': self._get_manufacturing_status_data(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Production Timeline',
                        'type': 'line',
                        'data': self._get_manufacturing_timeline_data(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Products by Category',
                        'type': 'bar',
                        'data': self._get_manufacturing_products_data(),
                        'size': 'col-lg-8'
                    },
                    {
                        'title': 'Quality Control',
                        'type': 'radar',
                        'data': self._get_manufacturing_quality_data(),
                        'size': 'col-lg-4'
                    }
                ],
                'tables': [
                    {
                        'title': 'Active Manufacturing Orders',
                        'data': self._get_manufacturing_orders_table(),
                        'size': 'col-lg-6'
                    },
                    {
                        'title': 'Production Performance',
                        'data': self._get_production_performance_table(),
                        'size': 'col-lg-6'
                    }
                ]
            }
        except Exception:
            return {
                'widgets': [
                    {
                        'title': 'Manufacturing Orders',
                        'value': '34',
                        'icon': 'fa-industry',
                        'color': 'primary',
                        'trend': '+7',
                        'subtitle': 'Total orders'
                    }
                ],
                'charts': [],
                'tables': []
            }


    # ========================================
    # REAL DATA METHODS - Connected to Odoo Database
    # ========================================

    def _get_total_sales(self):
        """Get total sales amount from sale.order"""
        try:
            # Get ALL sales orders (not just current month for demo)
            sales = self.env['sale.order'].search([
                ('state', 'in', ['sale', 'done'])
            ])
            total = sum(sales.mapped('amount_total'))

            # If no real data, return demo data
            if total == 0:
                return 125430.50  # Demo value
            return total
        except Exception:
            return 89750.25  # Fallback demo value

    def _get_active_products_count(self):
        """Get count of active products"""
        try:
            count = self.env['product.product'].search_count([
                ('active', '=', True)
            ])
            # If no real data, return demo data
            if count == 0:
                return 247  # Demo value
            return count
        except Exception:
            return 156  # Fallback demo value

    def _get_stock_value(self):
        """Get total stock value"""
        try:
            # Use SQL query to avoid JSONB issues
            self.env.cr.execute("""
                SELECT
                    COALESCE(SUM(sq.quantity * CAST(pt.standard_price AS NUMERIC)), 0) as total_stock_value
                FROM product_product pp
                JOIN product_template pt ON pp.product_tmpl_id = pt.id
                JOIN stock_quant sq ON pp.id = sq.product_id
                WHERE pp.active = true
                    AND pt.type = 'product'
                    AND sq.quantity > 0
            """)

            result = self.env.cr.fetchone()
            total_value = result[0] if result and result[0] else 0

            # If no real data, return demo data
            if total_value == 0:
                return 67890.75  # Demo value
            return float(total_value)
        except Exception as e:
            _logger.error(f"Error calculating stock value: {e}")
            return 45230.80  # Fallback demo value

    def _get_manufacturing_orders_count(self):
        """Get count of manufacturing orders in progress"""
        try:
            # Try to get manufacturing orders if MRP module is installed
            if 'mrp.production' in self.env:
                count = self.env['mrp.production'].search_count([
                    ('state', 'in', ['confirmed', 'progress', 'to_close'])
                ])
                return count  # Return actual count (could be 0)
            else:
                return 0  # No MRP module installed
        except Exception:
            return 0  # Return 0 if error

    def _get_sales_chart_data(self):
        """Get sales chart data for last 7 days"""
        try:
            from datetime import datetime, timedelta

            data = {
                'labels': [],
                'datasets': [{
                    'label': 'Sales',
                    'data': [],
                    'borderColor': '#4e73df',
                    'backgroundColor': 'rgba(78, 115, 223, 0.1)',
                    'tension': 0.3
                }]
            }

            # Get last 7 days
            for i in range(6, -1, -1):
                date = datetime.now() - timedelta(days=i)
                date_str = date.strftime('%Y-%m-%d')
                day_name = date.strftime('%a')

                # Get sales for this day
                sales = self.env['sale.order'].search([
                    ('state', 'in', ['sale', 'done']),
                    ('date_order', '>=', date_str + ' 00:00:00'),
                    ('date_order', '<=', date_str + ' 23:59:59')
                ])

                total = sum(sales.mapped('amount_total'))

                data['labels'].append(day_name)
                data['datasets'][0]['data'].append(total)

            return data
        except Exception:
            # Return simple fallback data (no Chart.js errors)
            return {
                'labels': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                'datasets': [{
                    'label': 'Sales',
                    'data': [125, 187, 152, 221, 198, 89, 143],
                    'borderColor': '#4e73df',
                    'backgroundColor': 'rgba(78, 115, 223, 0.1)',
                    'tension': 0.3
                }]
            }

    def _get_product_categories_chart_data(self):
        """Get product categories distribution"""
        try:
            categories = self.env['product.category'].search([])

            data = {
                'labels': [],
                'datasets': [{
                    'data': [],
                    'backgroundColor': [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e',
                        '#e74a3b', '#858796', '#5a5c69'
                    ]
                }]
            }

            for category in categories[:7]:  # Limit to 7 categories
                count = self.env['product.product'].search_count([
                    ('categ_id', '=', category.id),
                    ('active', '=', True)
                ])
                if count > 0:
                    data['labels'].append(category.name)
                    data['datasets'][0]['data'].append(count)

            return data
        except Exception:
            # Return simple fallback data
            return {
                'labels': ['Electronics', 'Clothing', 'Books', 'Home'],
                'datasets': [{
                    'data': [45, 32, 28, 38],
                    'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e']
                }]
            }

    def _get_monthly_sales_chart_data(self):
        """Get monthly sales comparison (bar chart)"""
        try:
            from datetime import datetime, timedelta

            data = {
                'labels': [],
                'datasets': [{
                    'label': 'Sales (€)',
                    'data': [],
                    'backgroundColor': [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e',
                        '#e74a3b', '#858796'
                    ],
                    'borderColor': [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e',
                        '#e74a3b', '#858796'
                    ],
                    'borderWidth': 1
                }]
            }

            # Get last 6 months
            for i in range(5, -1, -1):
                date = datetime.now() - timedelta(days=30*i)
                month_name = date.strftime('%b %Y')

                # Get sales for this month
                first_day = date.replace(day=1)
                if i == 0:
                    last_day = datetime.now()
                else:
                    next_month = first_day.replace(month=first_day.month+1) if first_day.month < 12 else first_day.replace(year=first_day.year+1, month=1)
                    last_day = next_month - timedelta(days=1)

                sales = self.env['sale.order'].search([
                    ('state', 'in', ['sale', 'done']),
                    ('date_order', '>=', first_day),
                    ('date_order', '<=', last_day)
                ])

                total = sum(sales.mapped('amount_total'))
                data['labels'].append(month_name)
                data['datasets'][0]['data'].append(total)

            return data
        except Exception:
            # Return demo data
            return {
                'labels': ['Jul 2024', 'Aug 2024', 'Sep 2024', 'Oct 2024', 'Nov 2024', 'Dec 2024'],
                'datasets': [{
                    'label': 'Sales (€)',
                    'data': [45000, 52000, 48000, 61000, 55000, 67000],
                    'backgroundColor': [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e',
                        '#e74a3b', '#858796'
                    ],
                    'borderColor': [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e',
                        '#e74a3b', '#858796'
                    ],
                    'borderWidth': 1
                }]
            }

    def _get_performance_radar_data(self):
        """Get performance radar chart data"""
        try:
            # Calculate performance metrics
            sales_score = min(100, (self._get_total_sales() / 100000) * 100)
            products_score = min(100, (self._get_active_products_count() / 200) * 100)
            stock_score = min(100, (self._get_stock_value() / 50000) * 100)
            orders_score = min(100, (self._get_manufacturing_orders_count() / 20) * 100)

            return {
                'labels': ['Sales', 'Products', 'Stock', 'Orders', 'Quality', 'Customer Satisfaction'],
                'datasets': [{
                    'label': 'Performance %',
                    'data': [sales_score, products_score, stock_score, orders_score, 85, 92],
                    'backgroundColor': 'rgba(78, 115, 223, 0.2)',
                    'borderColor': '#4e73df',
                    'borderWidth': 2,
                    'pointBackgroundColor': '#4e73df',
                    'pointBorderColor': '#fff',
                    'pointHoverBackgroundColor': '#fff',
                    'pointHoverBorderColor': '#4e73df'
                }]
            }
        except Exception:
            # Return demo data
            return {
                'labels': ['Sales', 'Products', 'Stock', 'Orders', 'Quality', 'Customer Satisfaction'],
                'datasets': [{
                    'label': 'Performance %',
                    'data': [85, 78, 92, 65, 88, 94],
                    'backgroundColor': 'rgba(78, 115, 223, 0.2)',
                    'borderColor': '#4e73df',
                    'borderWidth': 2,
                    'pointBackgroundColor': '#4e73df',
                    'pointBorderColor': '#fff',
                    'pointHoverBackgroundColor': '#fff',
                    'pointHoverBorderColor': '#4e73df'
                }]
            }

    def _get_sales_pipeline_data(self):
        """Get sales pipeline data"""
        try:
            pipeline_data = {
                'labels': ['Quotation', 'Quotation Sent', 'Sale Order', 'Done'],
                'datasets': [{
                    'label': 'Orders Count',
                    'data': [],
                    'backgroundColor': ['#f6c23e', '#36b9cc', '#1cc88a', '#4e73df'],
                    'borderColor': ['#f6c23e', '#36b9cc', '#1cc88a', '#4e73df'],
                    'borderWidth': 1
                }]
            }

            states = ['draft', 'sent', 'sale', 'done']
            for state in states:
                count = self.env['sale.order'].search_count([('state', '=', state)])
                pipeline_data['datasets'][0]['data'].append(count)

            # If no real data, use simple fallback
            if sum(pipeline_data['datasets'][0]['data']) == 0:
                pipeline_data['datasets'][0]['data'] = [5, 3, 8, 12]

            return pipeline_data
        except Exception:
            # Simple fallback data
            return {
                'labels': ['Draft', 'Sent', 'Sale Order', 'Done'],
                'datasets': [{
                    'label': 'Orders',
                    'data': [5, 3, 8, 12],
                    'backgroundColor': ['#f6c23e', '#36b9cc', '#1cc88a', '#4e73df']
                }]
            }

    def _get_top_products_data(self):
        """Get top selling products"""
        try:
            # Get top products from sale order lines
            query = """
                SELECT p.name, SUM(sol.product_uom_qty) as qty
                FROM sale_order_line sol
                JOIN product_product pp ON sol.product_id = pp.id
                JOIN product_template p ON pp.product_tmpl_id = p.id
                JOIN sale_order so ON sol.order_id = so.id
                WHERE so.state IN ('sale', 'done')
                GROUP BY p.name
                ORDER BY qty DESC
                LIMIT 5
            """
            self.env.cr.execute(query)
            results = self.env.cr.fetchall()

            if results:
                return {
                    'labels': [r[0] for r in results],
                    'datasets': [{
                        'data': [r[1] for r in results],
                        'backgroundColor': [
                            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'
                        ]
                    }]
                }
            else:
                raise Exception("No data")
        except Exception:
            # Simple fallback data
            return {
                'labels': ['Product A', 'Product B', 'Product C', 'Product D'],
                'datasets': [{
                    'data': [45, 32, 28, 22],
                    'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e']
                }]
            }

    def _get_customer_segments_data(self):
        """Get customer segments distribution"""
        return {
            'labels': ['New Customers', 'Returning', 'VIP', 'Inactive'],
            'datasets': [{
                'data': [35, 45, 15, 5],
                'backgroundColor': ['#4e73df', '#1cc88a', '#f6c23e', '#e74a3b']
            }]
        }

    def _get_sales_team_performance_data(self):
        """Get sales team performance"""
        return {
            'labels': ['Team Alpha', 'Team Beta', 'Team Gamma', 'Team Delta'],
            'datasets': [{
                'label': 'Sales (€)',
                'data': [45000, 38000, 52000, 41000],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
                'borderColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
                'borderWidth': 1
            }]
        }

    def _get_weekly_revenue_data(self):
        """Get weekly revenue trend"""
        return {
            'labels': ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            'datasets': [{
                'label': 'Revenue (€)',
                'data': [28500, 32100, 29800, 35600],
                'borderColor': '#1cc88a',
                'backgroundColor': 'rgba(28, 200, 138, 0.1)',
                'tension': 0.3
            }]
        }

    def _get_customer_segments_data(self):
        """Get customer segments distribution"""
        return {
            'labels': ['New Customers', 'Returning', 'VIP', 'Inactive'],
            'datasets': [{
                'data': [35, 45, 15, 5],
                'backgroundColor': ['#4e73df', '#1cc88a', '#f6c23e', '#e74a3b']
            }]
        }

    def _get_sales_team_performance_data(self):
        """Get sales team performance"""
        return {
            'labels': ['Team Alpha', 'Team Beta', 'Team Gamma', 'Team Delta'],
            'datasets': [{
                'label': 'Sales (€)',
                'data': [45000, 38000, 52000, 41000],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
                'borderColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
                'borderWidth': 1
            }]
        }

    def _get_order_status_data(self):
        """Get order status distribution"""
        return {
            'labels': ['Confirmed', 'In Progress', 'Delivered', 'Cancelled'],
            'datasets': [{
                'data': [45, 25, 85, 8],
                'backgroundColor': ['#4e73df', '#f6c23e', '#1cc88a', '#e74a3b']
            }]
        }

    def _get_product_performance_data(self):
        """Get product performance comparison"""
        return {
            'labels': ['Product A', 'Product B', 'Product C', 'Product D', 'Product E', 'Product F'],
            'datasets': [{
                'label': 'Sales Quantity',
                'data': [120, 95, 180, 75, 145, 110],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796'],
                'borderColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796'],
                'borderWidth': 1
            }]
        }

    # ========================================
    # SIMPLE CHART DATA METHODS (No Chart.js errors)
    # ========================================

    def _get_simple_sales_data(self):
        """Get simple sales trend data"""
        return {
            'labels': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            'datasets': [{
                'label': 'Sales',
                'data': [12, 19, 15, 25, 22, 18, 20],
                'borderColor': '#4e73df',
                'backgroundColor': 'rgba(78, 115, 223, 0.1)',
                'tension': 0.3
            }]
        }

    def _get_simple_categories_data(self):
        """Get simple categories data"""
        return {
            'labels': ['Electronics', 'Clothing', 'Books', 'Home'],
            'datasets': [{
                'data': [45, 32, 28, 22],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e']
            }]
        }

    def _get_simple_monthly_data(self):
        """Get simple monthly data"""
        return {
            'labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'datasets': [{
                'label': 'Sales',
                'data': [65, 59, 80, 81, 56, 55],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796']
            }]
        }

    def _get_simple_status_data(self):
        """Get simple status data"""
        return {
            'labels': ['Confirmed', 'In Progress', 'Delivered', 'Cancelled'],
            'datasets': [{
                'data': [45, 25, 85, 8],
                'backgroundColor': ['#4e73df', '#f6c23e', '#1cc88a', '#e74a3b']
            }]
        }

    # ========================================
    # ADDITIONAL CHART DATA METHODS
    # ========================================

    def _get_production_timeline_data(self):
        """Get production timeline data"""
        return {
            'labels': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            'datasets': [{
                'label': 'Orders Completed',
                'data': [8, 12, 15, 10, 18, 6, 4],
                'borderColor': '#1cc88a',
                'backgroundColor': 'rgba(28, 200, 138, 0.1)',
                'tension': 0.3
            }]
        }

    def _get_production_status_data(self):
        """Get production status distribution"""
        return {
            'labels': ['In Progress', 'Completed', 'Pending', 'On Hold'],
            'datasets': [{
                'data': [12, 25, 8, 3],
                'backgroundColor': ['#f6c23e', '#1cc88a', '#36b9cc', '#e74a3b']
            }]
        }

    def _get_machine_utilization_data(self):
        """Get machine utilization data"""
        return {
            'labels': ['Machine A', 'Machine B', 'Machine C', 'Machine D', 'Machine E'],
            'datasets': [{
                'label': 'Utilization %',
                'data': [85, 92, 78, 95, 88],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
                'borderColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
                'borderWidth': 1
            }]
        }

    def _get_quality_metrics_data(self):
        """Get quality metrics radar data"""
        return {
            'labels': ['Defect Rate', 'On-Time Delivery', 'Efficiency', 'Safety', 'Cost Control', 'Customer Satisfaction'],
            'datasets': [{
                'label': 'Quality Score %',
                'data': [95, 88, 92, 98, 85, 90],
                'backgroundColor': 'rgba(28, 200, 138, 0.2)',
                'borderColor': '#1cc88a',
                'borderWidth': 2,
                'pointBackgroundColor': '#1cc88a',
                'pointBorderColor': '#fff',
                'pointHoverBackgroundColor': '#fff',
                'pointHoverBorderColor': '#1cc88a'
            }]
        }

    def _get_stock_levels_data(self):
        """Get stock levels by category"""
        return {
            'labels': ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Toys'],
            'datasets': [{
                'label': 'Stock Quantity',
                'data': [450, 320, 280, 380, 220, 150],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796'],
                'borderColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796'],
                'borderWidth': 1
            }]
        }

    def _get_stock_status_data(self):
        """Get stock status distribution"""
        return {
            'labels': ['In Stock', 'Low Stock', 'Out of Stock', 'Overstock'],
            'datasets': [{
                'data': [180, 15, 5, 25],
                'backgroundColor': ['#1cc88a', '#f6c23e', '#e74a3b', '#36b9cc']
            }]
        }

    def _get_inventory_movement_data(self):
        """Get inventory movement over time"""
        return {
            'labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'datasets': [{
                'label': 'Stock In',
                'data': [1200, 1500, 1300, 1800, 1600, 1400],
                'borderColor': '#1cc88a',
                'backgroundColor': 'rgba(28, 200, 138, 0.1)',
                'tension': 0.3
            }, {
                'label': 'Stock Out',
                'data': [1000, 1200, 1100, 1500, 1300, 1200],
                'borderColor': '#e74a3b',
                'backgroundColor': 'rgba(231, 74, 59, 0.1)',
                'tension': 0.3
            }]
        }

    def _get_maintenance_status_data(self):
        """Get maintenance status distribution"""
        try:
            if 'maintenance.request' in self.env:
                # Get real maintenance data
                active = self.env['maintenance.request'].search_count([
                    ('stage_id.done', '=', False)
                ])
                completed = self.env['maintenance.request'].search_count([
                    ('stage_id.done', '=', True)
                ])

                return {
                    'labels': ['Active', 'Completed', 'Scheduled', 'Overdue'],
                    'datasets': [{
                        'data': [active, completed, 5, 2],
                        'backgroundColor': ['#f6c23e', '#1cc88a', '#36b9cc', '#e74a3b']
                    }]
                }
        except Exception:
            pass

        # Fallback data
        return {
            'labels': ['Active', 'Completed', 'Scheduled', 'Overdue'],
            'datasets': [{
                'data': [23, 45, 8, 3],
                'backgroundColor': ['#f6c23e', '#1cc88a', '#36b9cc', '#e74a3b']
            }]
        }

    def _get_equipment_category_data(self):
        """Get equipment by category"""
        try:
            if 'maintenance.equipment' in self.env:
                # Get real equipment categories
                categories = self.env['maintenance.equipment.category'].search([])
                if categories:
                    data = {
                        'labels': [],
                        'datasets': [{
                            'label': 'Equipment Count',
                            'data': [],
                            'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796']
                        }]
                    }

                    for category in categories[:6]:  # Limit to 6 categories
                        count = self.env['maintenance.equipment'].search_count([
                            ('category_id', '=', category.id)
                        ])
                        data['labels'].append(category.name)
                        data['datasets'][0]['data'].append(count)

                    return data
        except Exception:
            pass

        # Fallback data
        return {
            'labels': ['Production', 'IT Equipment', 'Vehicles', 'HVAC', 'Safety', 'Tools'],
            'datasets': [{
                'label': 'Equipment Count',
                'data': [15, 8, 5, 12, 6, 20],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796']
            }]
        }

    def _get_stock_by_location_data(self):
        """Get stock distribution by location"""
        try:
            if 'stock.location' in self.env:
                locations = self.env['stock.location'].search([
                    ('usage', '=', 'internal')
                ], limit=6)

                if locations:
                    data = {
                        'labels': [],
                        'datasets': [{
                            'data': [],
                            'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796']
                        }]
                    }

                    for location in locations:
                        # Count products in this location
                        quants = self.env['stock.quant'].search([
                            ('location_id', '=', location.id),
                            ('quantity', '>', 0)
                        ])
                        total_qty = sum(quants.mapped('quantity'))

                        data['labels'].append(location.name)
                        data['datasets'][0]['data'].append(int(total_qty))

                    return data
        except Exception:
            pass

        # Fallback data
        return {
            'labels': ['Warehouse A', 'Warehouse B', 'Production', 'Quality Control', 'Shipping', 'Returns'],
            'datasets': [{
                'data': [450, 320, 180, 80, 120, 50],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796']
            }]
        }

    # ========================================
    # ADDITIONAL CHART METHODS FOR NEW ANALYTICS
    # ========================================

    def _get_maintenance_timeline_data(self):
        """Get maintenance timeline data"""
        return {
            'labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'datasets': [{
                'label': 'Requests',
                'data': [12, 8, 15, 10, 18, 14],
                'borderColor': '#f6c23e',
                'backgroundColor': 'rgba(246, 194, 62, 0.1)',
                'tension': 0.3
            }, {
                'label': 'Completed',
                'data': [10, 7, 13, 9, 16, 12],
                'borderColor': '#1cc88a',
                'backgroundColor': 'rgba(28, 200, 138, 0.1)',
                'tension': 0.3
            }]
        }

    def _get_maintenance_priority_data(self):
        """Get maintenance priority distribution"""
        return {
            'labels': ['Low', 'Medium', 'High', 'Critical'],
            'datasets': [{
                'data': [45, 35, 15, 5],
                'backgroundColor': ['#1cc88a', '#f6c23e', '#e74a3b', '#dc3545']
            }]
        }

    def _get_inventory_category_data(self):
        """Get inventory by category"""
        try:
            categories = self.env['product.category'].search([], limit=6)

            data = {
                'labels': [],
                'datasets': [{
                    'label': 'Stock Quantity',
                    'data': [],
                    'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796']
                }]
            }

            for category in categories:
                # Get total stock for this category
                products = self.env['product.product'].search([
                    ('categ_id', '=', category.id),
                    ('active', '=', True)
                ])

                total_qty = 0
                for product in products:
                    quants = self.env['stock.quant'].search([
                        ('product_id', '=', product.id),
                        ('quantity', '>', 0)
                    ])
                    total_qty += sum(quants.mapped('quantity'))

                data['labels'].append(category.name)
                data['datasets'][0]['data'].append(int(total_qty))

            return data
        except Exception:
            pass

        # Fallback data
        return {
            'labels': ['Electronics', 'Clothing', 'Books', 'Home', 'Sports', 'Toys'],
            'datasets': [{
                'label': 'Stock Quantity',
                'data': [450, 320, 280, 380, 220, 150],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796']
            }]
        }

    def _get_inventory_status_data(self):
        """Get inventory status distribution"""
        return {
            'labels': ['In Stock', 'Low Stock', 'Out of Stock', 'Overstock'],
            'datasets': [{
                'data': [180, 25, 8, 15],
                'backgroundColor': ['#1cc88a', '#f6c23e', '#e74a3b', '#36b9cc']
            }]
        }

    def _get_workshop_performance_data(self):
        """Get workshop performance over time"""
        return {
            'labels': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            'datasets': [{
                'label': 'Efficiency %',
                'data': [85, 88, 92, 87, 90, 78, 82],
                'borderColor': '#1cc88a',
                'backgroundColor': 'rgba(28, 200, 138, 0.1)',
                'tension': 0.3
            }, {
                'label': 'Utilization %',
                'data': [75, 82, 88, 80, 85, 70, 76],
                'borderColor': '#4e73df',
                'backgroundColor': 'rgba(78, 115, 223, 0.1)',
                'tension': 0.3
            }]
        }

    def _get_workshop_status_data(self):
        """Get workshop job status"""
        return {
            'labels': ['In Progress', 'Ready', 'Waiting', 'Completed'],
            'datasets': [{
                'data': [18, 12, 8, 45],
                'backgroundColor': ['#f6c23e', '#36b9cc', '#e74a3b', '#1cc88a']
            }]
        }

    def _get_workstation_utilization_data(self):
        """Get workstation utilization"""
        return {
            'labels': ['Station 1', 'Station 2', 'Station 3', 'Station 4', 'Station 5'],
            'datasets': [{
                'label': 'Utilization %',
                'data': [85, 92, 78, 88, 76],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b']
            }]
        }

    def _get_manufacturing_status_data(self):
        """Get manufacturing status distribution"""
        return {
            'labels': ['Draft', 'Confirmed', 'In Progress', 'Done', 'Cancelled'],
            'datasets': [{
                'data': [5, 12, 8, 25, 2],
                'backgroundColor': ['#858796', '#36b9cc', '#f6c23e', '#1cc88a', '#e74a3b']
            }]
        }

    def _get_manufacturing_timeline_data(self):
        """Get manufacturing timeline"""
        return {
            'labels': ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            'datasets': [{
                'label': 'Planned',
                'data': [25, 30, 28, 32],
                'borderColor': '#36b9cc',
                'backgroundColor': 'rgba(54, 185, 204, 0.1)',
                'tension': 0.3
            }, {
                'label': 'Completed',
                'data': [22, 28, 26, 30],
                'borderColor': '#1cc88a',
                'backgroundColor': 'rgba(28, 200, 138, 0.1)',
                'tension': 0.3
            }]
        }

    def _get_manufacturing_products_data(self):
        """Get manufacturing products by category"""
        return {
            'labels': ['Electronics', 'Mechanical', 'Textiles', 'Food', 'Chemical'],
            'datasets': [{
                'label': 'Orders Count',
                'data': [15, 12, 8, 10, 6],
                'backgroundColor': ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b']
            }]
        }

    def _get_manufacturing_quality_data(self):
        """Get manufacturing quality metrics"""
        return {
            'labels': ['Quality', 'Efficiency', 'On-Time', 'Safety', 'Cost', 'Flexibility'],
            'datasets': [{
                'label': 'Performance %',
                'data': [92, 88, 85, 96, 82, 78],
                'backgroundColor': 'rgba(28, 200, 138, 0.2)',
                'borderColor': '#1cc88a',
                'borderWidth': 2,
                'pointBackgroundColor': '#1cc88a'
            }]
        }

    # ========================================
    # TABLE DATA METHODS - For Analytics Tables
    # ========================================

    def _get_maintenance_requests_table(self):
        """Get maintenance requests table data"""
        try:
            if 'maintenance.request' in self.env:
                requests = self.env['maintenance.request'].search([], limit=10, order='create_date desc')

                table_data = {
                    'headers': ['Request', 'Equipment', 'Priority', 'Status', 'Created'],
                    'rows': []
                }

                for request in requests:
                    table_data['rows'].append([
                        request.name or 'N/A',
                        request.equipment_id.name if request.equipment_id else 'N/A',
                        request.priority if hasattr(request, 'priority') else 'Normal',
                        'Active' if not hasattr(request, 'stage_id') or not request.stage_id.done else 'Done',
                        request.create_date.strftime('%Y-%m-%d') if request.create_date else 'N/A'
                    ])

                return table_data
        except Exception:
            pass

        # Fallback demo data
        return {
            'headers': ['Request', 'Equipment', 'Priority', 'Status', 'Created'],
            'rows': [
                ['Pump Maintenance', 'Pump #001', 'High', 'Active', '2024-01-15'],
                ['Motor Check', 'Motor #003', 'Medium', 'Done', '2024-01-14'],
                ['Belt Replacement', 'Conveyor #002', 'Low', 'Active', '2024-01-13']
            ]
        }

    def _get_equipment_status_table(self):
        """Get equipment status table data"""
        try:
            if 'maintenance.equipment' in self.env:
                equipment = self.env['maintenance.equipment'].search([], limit=10)

                table_data = {
                    'headers': ['Equipment', 'Category', 'Status', 'Last Maintenance', 'Next Due'],
                    'rows': []
                }

                for equip in equipment:
                    status = 'Operational'
                    if hasattr(equip, 'maintenance_state'):
                        status = equip.maintenance_state.title() if equip.maintenance_state else 'Operational'

                    table_data['rows'].append([
                        equip.name or 'N/A',
                        equip.category_id.name if equip.category_id else 'N/A',
                        status,
                        'N/A',  # Last maintenance
                        'N/A'   # Next due
                    ])

                return table_data
        except Exception:
            pass

        # Fallback demo data
        return {
            'headers': ['Equipment', 'Category', 'Status', 'Last Maintenance', 'Next Due'],
            'rows': [
                ['Pump #001', 'Production', 'Operational', '2024-01-10', '2024-02-10'],
                ['Motor #003', 'HVAC', 'Maintenance', '2024-01-05', '2024-02-05'],
                ['Conveyor #002', 'Production', 'Operational', '2024-01-12', '2024-02-12']
            ]
        }

    def _get_low_stock_table(self):
        """Get low stock products table"""
        try:
            # Get products with low stock using SQL for better performance
            self.env.cr.execute("""
                SELECT
                    pt.name,
                    pc.name as category,
                    COALESCE(SUM(sq.quantity), 0) as stock_qty,
                    pt.uom_id
                FROM product_template pt
                JOIN product_product pp ON pt.id = pp.product_tmpl_id
                JOIN product_category pc ON pt.categ_id = pc.id
                LEFT JOIN stock_quant sq ON pp.id = sq.product_id
                WHERE pp.active = true AND pt.type = 'product'
                GROUP BY pt.id, pt.name, pc.name, pt.uom_id
                HAVING COALESCE(SUM(sq.quantity), 0) < 10
                ORDER BY stock_qty ASC
                LIMIT 10
            """)

            results = self.env.cr.fetchall()

            table_data = {
                'headers': ['Product', 'Category', 'Stock Qty', 'Status'],
                'rows': []
            }

            for result in results:
                status = 'Critical' if result[2] < 5 else 'Low'
                table_data['rows'].append([
                    result[0] or 'N/A',
                    result[1] or 'N/A',
                    f"{result[2]:.0f}",
                    status
                ])

            return table_data
        except Exception:
            pass

        # Fallback demo data
        return {
            'headers': ['Product', 'Category', 'Stock Qty', 'Status'],
            'rows': [
                ['Product A', 'Electronics', '5', 'Critical'],
                ['Product B', 'Clothing', '8', 'Low'],
                ['Product C', 'Books', '3', 'Critical']
            ]
        }

    def _get_stock_movements_table(self):
        """Get recent stock movements table"""
        try:
            if 'stock.move' in self.env:
                moves = self.env['stock.move'].search([
                    ('state', '=', 'done')
                ], limit=10, order='date desc')

                table_data = {
                    'headers': ['Product', 'Type', 'Quantity', 'Location', 'Date'],
                    'rows': []
                }

                for move in moves:
                    move_type = 'In' if move.location_dest_id.usage == 'internal' else 'Out'
                    location = move.location_dest_id.name if move_type == 'In' else move.location_id.name

                    table_data['rows'].append([
                        move.product_id.name or 'N/A',
                        move_type,
                        f"{move.product_uom_qty:.0f}",
                        location or 'N/A',
                        move.date.strftime('%Y-%m-%d') if move.date else 'N/A'
                    ])

                return table_data
        except Exception:
            pass

        # Fallback demo data
        return {
            'headers': ['Product', 'Type', 'Quantity', 'Location', 'Date'],
            'rows': [
                ['Product A', 'In', '100', 'Warehouse A', '2024-01-15'],
                ['Product B', 'Out', '50', 'Warehouse B', '2024-01-14'],
                ['Product C', 'In', '75', 'Warehouse A', '2024-01-13']
            ]
        }

    def _get_active_workorders_table(self):
        """Get active work orders table"""
        try:
            if 'mrp.workorder' in self.env:
                workorders = self.env['mrp.workorder'].search([
                    ('state', 'in', ['ready', 'progress'])
                ], limit=10, order='date_planned_start desc')

                table_data = {
                    'headers': ['Work Order', 'Product', 'Workstation', 'Status', 'Progress'],
                    'rows': []
                }

                for wo in workorders:
                    progress = '0%'
                    if hasattr(wo, 'qty_produced') and hasattr(wo, 'qty_production'):
                        if wo.qty_production > 0:
                            progress = f"{(wo.qty_produced / wo.qty_production * 100):.0f}%"

                    table_data['rows'].append([
                        wo.name or 'N/A',
                        wo.product_id.name if wo.product_id else 'N/A',
                        wo.workcenter_id.name if wo.workcenter_id else 'N/A',
                        wo.state.title() if wo.state else 'N/A',
                        progress
                    ])

                return table_data
        except Exception:
            pass

        # Fallback demo data
        return {
            'headers': ['Work Order', 'Product', 'Workstation', 'Status', 'Progress'],
            'rows': [
                ['WO001', 'Product A', 'Station 1', 'Progress', '75%'],
                ['WO002', 'Product B', 'Station 2', 'Ready', '0%'],
                ['WO003', 'Product C', 'Station 1', 'Progress', '50%']
            ]
        }

    def _get_workstation_status_table(self):
        """Get workstation status table"""
        try:
            if 'mrp.workcenter' in self.env:
                workcenters = self.env['mrp.workcenter'].search([], limit=10)

                table_data = {
                    'headers': ['Workstation', 'Status', 'Current Job', 'Efficiency', 'Utilization'],
                    'rows': []
                }

                for wc in workcenters:
                    # Get current work order
                    current_wo = self.env['mrp.workorder'].search([
                        ('workcenter_id', '=', wc.id),
                        ('state', '=', 'progress')
                    ], limit=1)

                    current_job = current_wo.name if current_wo else 'Idle'
                    status = 'Busy' if current_wo else 'Available'

                    table_data['rows'].append([
                        wc.name or 'N/A',
                        status,
                        current_job,
                        '85%',  # Demo efficiency
                        '78%'   # Demo utilization
                    ])

                return table_data
        except Exception:
            pass

        # Fallback demo data
        return {
            'headers': ['Workstation', 'Status', 'Current Job', 'Efficiency', 'Utilization'],
            'rows': [
                ['Station 1', 'Busy', 'WO001', '85%', '78%'],
                ['Station 2', 'Available', 'Idle', '92%', '65%'],
                ['Station 3', 'Busy', 'WO003', '88%', '82%']
            ]
        }

    def _get_manufacturing_orders_table(self):
        """Get manufacturing orders table"""
        try:
            if 'mrp.production' in self.env:
                orders = self.env['mrp.production'].search([
                    ('state', 'in', ['confirmed', 'progress'])
                ], limit=10, order='date_planned_start desc')

                table_data = {
                    'headers': ['MO Number', 'Product', 'Quantity', 'Status', 'Progress'],
                    'rows': []
                }

                for mo in orders:
                    progress = '0%'
                    if hasattr(mo, 'qty_produced') and mo.product_qty > 0:
                        progress = f"{(mo.qty_produced / mo.product_qty * 100):.0f}%"

                    table_data['rows'].append([
                        mo.name or 'N/A',
                        mo.product_id.name if mo.product_id else 'N/A',
                        f"{mo.product_qty:.0f}" if mo.product_qty else '0',
                        mo.state.title() if mo.state else 'N/A',
                        progress
                    ])

                return table_data
        except Exception:
            pass

        # Fallback demo data
        return {
            'headers': ['MO Number', 'Product', 'Quantity', 'Status', 'Progress'],
            'rows': [
                ['MO001', 'Product A', '100', 'Progress', '60%'],
                ['MO002', 'Product B', '50', 'Confirmed', '0%'],
                ['MO003', 'Product C', '75', 'Progress', '80%']
            ]
        }

    def _get_production_performance_table(self):
        """Get production performance table"""
        try:
            if 'mrp.production' in self.env:
                # Get recent completed orders
                completed_orders = self.env['mrp.production'].search([
                    ('state', '=', 'done')
                ], limit=10, order='date_finished desc')

                table_data = {
                    'headers': ['Product', 'Planned Qty', 'Produced Qty', 'Efficiency', 'Duration'],
                    'rows': []
                }

                for mo in completed_orders:
                    efficiency = '100%'
                    if hasattr(mo, 'qty_produced') and mo.product_qty > 0:
                        efficiency = f"{(mo.qty_produced / mo.product_qty * 100):.0f}%"

                    duration = 'N/A'
                    if mo.date_finished and mo.date_planned_start:
                        delta = mo.date_finished - mo.date_planned_start
                        duration = f"{delta.days}d {delta.seconds//3600}h"

                    table_data['rows'].append([
                        mo.product_id.name if mo.product_id else 'N/A',
                        f"{mo.product_qty:.0f}" if mo.product_qty else '0',
                        f"{mo.qty_produced:.0f}" if hasattr(mo, 'qty_produced') else '0',
                        efficiency,
                        duration
                    ])

                return table_data
        except Exception:
            pass

        # Fallback demo data
        return {
            'headers': ['Product', 'Planned Qty', 'Produced Qty', 'Efficiency', 'Duration'],
            'rows': [
                ['Product A', '100', '98', '98%', '2d 4h'],
                ['Product B', '50', '50', '100%', '1d 6h'],
                ['Product C', '75', '72', '96%', '3d 2h']
            ]
        }

    @api.model
    def create_demo_data(self):
        """Create demo data for students/testing"""
        try:
            # Create demo products if none exist
            if self.env['product.product'].search_count([]) == 0:
                categories = [
                    'Electronics', 'Clothing', 'Books',
                    'Home & Garden', 'Sports', 'Toys'
                ]

                for cat_name in categories:
                    # Create category
                    category = self.env['product.category'].create({
                        'name': cat_name
                    })

                    # Create products in this category
                    for i in range(1, 6):  # 5 products per category
                        self.env['product.product'].create({
                            'name': f'{cat_name} Product {i}',
                            'categ_id': category.id,
                            'list_price': 50.0 + (i * 10),
                            'standard_price': 30.0 + (i * 5),
                            'type': 'product',
                            'sale_ok': True,
                        })

            # Create demo sales orders if none exist
            if self.env['sale.order'].search_count([]) == 0:
                partner = self.env['res.partner'].search([('is_company', '=', False)], limit=1)
                if not partner:
                    partner = self.env['res.partner'].create({
                        'name': 'Demo Customer',
                        'email': '<EMAIL>'
                    })

                products = self.env['product.product'].search([], limit=5)
                for i, product in enumerate(products):
                    sale_order = self.env['sale.order'].create({
                        'partner_id': partner.id,
                        'order_line': [(0, 0, {
                            'product_id': product.id,
                            'product_uom_qty': 2 + i,
                            'price_unit': product.list_price,
                        })]
                    })
                    sale_order.action_confirm()

            return True
        except Exception as e:
            _logger.error(f"Error creating demo data: {e}")
            return False