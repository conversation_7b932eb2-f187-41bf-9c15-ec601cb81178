# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)


class AnalyticsDashboard(models.Model):
    _name = 'analytics.dashboard'
    _description = 'Analytics Dashboard'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'sequence, name'

    name = fields.Char(string='Dashboard Name', required=True)
    description = fields.Text(string='Description')
    active = fields.Boolean(string='Active', default=True)
    sequence = fields.Integer(string='Sequence', default=10)

    # Dashboard Type
    dashboard_type = fields.Selection([
        ('overview', 'Overview'),
        ('sales', 'Sales'),
        ('production', 'Production'),
        ('stock', 'Stock'),
        ('maintenance', 'Maintenance'),
        ('inventory', 'Inventory'),
        ('workshop', 'Workshop'),
        ('manufacturing', 'Manufacturing'),
    ], string='Dashboard Type', required=True, default='overview')

    # Configuration
    auto_refresh = fields.Boolean(string='Auto Refresh', default=True)
    refresh_interval = fields.Integer(string='Refresh Interval (seconds)', default=30)

    # Access Control
    user_ids = fields.Many2many('res.users', string='Allowed Users')
    group_ids = fields.Many2many('res.groups', string='Allowed Groups')

    def action_view_dashboard(self):
        """Open the analytics dashboard"""
        self.ensure_one()
        return {
            'type': 'ir.actions.client',
            'tag': 'analytics_dashboard',
            'params': {
                'dashboard_id': self.id,
                'dashboard_type': self.dashboard_type,
            },
            'name': self.name,
            'target': 'current',
        }

    @api.model
    def get_dashboard_data(self, dashboard_type='overview'):
        """Get dashboard data based on type"""
        data = {
            'dashboard_type': dashboard_type,
            'title': f'{dashboard_type.title()} Analytics',
            'widgets': [],
            'charts': [],
        }

        # Add specific data based on dashboard type
        if dashboard_type == 'sales':
            data.update(self._get_sales_data())
        elif dashboard_type == 'production':
            data.update(self._get_production_data())
        elif dashboard_type == 'stock':
            data.update(self._get_stock_data())
        elif dashboard_type == 'overview':
            data.update(self._get_overview_data())

        return data

    def _get_overview_data(self):
        """Get overview dashboard data"""
        return {
            'widgets': [
                {
                    'title': 'Total Sales',
                    'value': '€0',
                    'icon': 'fa-euro',
                    'color': 'success'
                },
                {
                    'title': 'Active Products',
                    'value': '0',
                    'icon': 'fa-cube',
                    'color': 'info'
                },
                {
                    'title': 'Stock Value',
                    'value': '€0',
                    'icon': 'fa-warehouse',
                    'color': 'warning'
                },
                {
                    'title': 'Manufacturing Orders',
                    'value': '0',
                    'icon': 'fa-cogs',
                    'color': 'primary'
                }
            ]
        }

    def _get_sales_data(self):
        """Get sales dashboard data"""
        return {
            'widgets': [
                {
                    'title': 'Monthly Sales',
                    'value': '€0',
                    'icon': 'fa-chart-line',
                    'color': 'success'
                }
            ]
        }

    def _get_production_data(self):
        """Get production dashboard data"""
        return {
            'widgets': [
                {
                    'title': 'Production Orders',
                    'value': '0',
                    'icon': 'fa-industry',
                    'color': 'primary'
                }
            ]
        }

    def _get_stock_data(self):
        """Get stock dashboard data"""
        return {
            'widgets': [
                {
                    'title': 'Stock Moves',
                    'value': '0',
                    'icon': 'fa-truck',
                    'color': 'info'
                }
            ]
        }


# Simplified model structure for clean start
# Additional models will be added as needed in future phases