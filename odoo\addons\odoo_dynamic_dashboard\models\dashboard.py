# -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class Dashboard(models.Model):
    _name = 'dashboard.dashboard'
    _description = 'Dashboard'

    name = fields.Char(string='Name', required=True)
    description = fields.Text(string='Description')
    active = fields.Boolean(string='Active', default=True)
    layout = fields.Selection([
        ('grid', 'Grid'),
        ('flow', 'Flow')
    ], string='Layout', default='grid')
    theme = fields.Many2one('dashboard.theme', string='Theme')
    refresh_interval = fields.Integer(string='Refresh Interval (seconds)', default=0)
    block_ids = fields.One2many('dashboard.block', 'dashboard_id', string='Blocks')

    def action_view_dashboard(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.client',
            'tag': 'odoo_dynamic_dashboard.dashboard',
            'params': {
                'dashboard_id': self.id
            },
            'name': self.name,
        }


class DashboardBlock(models.Model):
    _name = 'dashboard.block'
    _description = 'Dashboard Block'

    name = fields.Char(string='Name', required=True)
    dashboard_id = fields.Many2one('dashboard.dashboard', string='Dashboard', required=True, ondelete='cascade')
    type = fields.Selection([
        ('kpi', 'KPI'),
        ('chart', 'Chart'),
        ('list', 'List'),
        ('html', 'HTML')
    ], string='Type', required=True, default='kpi')
    
    # Position and size
    width = fields.Integer(string='Width', default=2)
    height = fields.Integer(string='Height', default=2)
    data_x = fields.Integer(string='X Position', default=0)
    data_y = fields.Integer(string='Y Position', default=0)
    
    # Style
    background_color = fields.Char(string='Background Color', default='#ffffff')
    text_color = fields.Char(string='Text Color', default='#333333')
    
    # KPI Configuration
    model_name = fields.Char(string='Model')
    domain = fields.Char(string='Domain')
    operation = fields.Selection([
        ('count', 'Count'),
        ('sum', 'Sum'),
        ('avg', 'Average'),
        ('min', 'Minimum'),
        ('max', 'Maximum')
    ], string='Operation', default='count')
    measured_field_id = fields.Many2one('ir.model.fields', string='Measured Field')
    
    # Chart Configuration
    chart_type = fields.Selection([
        ('bar', 'Bar'),
        ('line', 'Line'),
        ('pie', 'Pie'),
        ('doughnut', 'Doughnut'),
        ('radar', 'Radar'),
        ('polarArea', 'Polar Area')
    ], string='Chart Type', default='bar')
    chart_data_model = fields.Char(string='Chart Data Model')
    chart_data_domain = fields.Char(string='Chart Data Domain')
    chart_data_groupby = fields.Char(string='Chart Data Group By')
    chart_data_measure = fields.Many2one('ir.model.fields', string='Chart Data Measure')
    chart_data_limit = fields.Integer(string='Chart Data Limit', default=10)
    
    @api.onchange('model_name')
    def _onchange_model_name(self):
        self.domain = '[]'
        self.measured_field_id = False
        
    @api.onchange('chart_data_model')
    def _onchange_chart_data_model(self):
        self.chart_data_domain = '[]'
        self.chart_data_measure = False
        self.chart_data_groupby = False


class DashboardTheme(models.Model):
    _name = 'dashboard.theme'
    _description = 'Dashboard Theme'

    name = fields.Char(string='Name', required=True)
    background_color = fields.Char(string='Background Color', default='#f5f5f5')
    text_color = fields.Char(string='Text Color', default='#333333')
    tile_background_color = fields.Char(string='Tile Background Color', default='#ffffff')
    tile_text_color = fields.Char(string='Tile Text Color', default='#333333')
    accent_color = fields.Char(string='Accent Color', default='#7c7bad')
    is_dark = fields.Boolean(string='Dark Mode', default=False)