/** @odoo-module **/

import { Component, useState, onMounted, onWillUnmount } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { registry } from "@web/core/registry";

export class AnalyticsDashboardNew extends Component {
    static template = "AnalyticsDashboard";

    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");

        this.state = useState({
            loading: true,
            error: null,
            dashboardData: null,
            dashboardType: this.props.action?.params?.dashboard_type || 'overview',
            dashboardTitle: this.getDashboardTitle(),
            autoRefreshEnabled: true
        });

        this.charts = {};
        this.refreshInterval = null;

        onMounted(() => {
            this.loadDashboardData();
            this.startAutoRefresh();
        });

        onWillUnmount(() => {
            this.stopAutoRefresh();
            this.destroyCharts();
        });
    }

    getDashboardTitle() {
        const titles = {
            'overview': '📈 Dashboard Overview',
            'sales': '💰 Sales Analytics',
            'production': '🏭 Production Analytics',
            'stock': '📦 Stock Analytics',
            'maintenance': '🔧 Maintenance Analytics',
            'inventory': '📋 Inventory Analytics',
            'workshop': '🔨 Workshop Analytics',
            'manufacturing': '⚙️ Manufacturing Analytics'
        };
        return titles[this.state.dashboardType] || '📊 Analytics Dashboard';
    }

    async loadDashboardData() {
        this.state.loading = true;
        this.state.error = null;

        try {
            const data = await this.orm.call(
                'analytics.dashboard',
                'get_dashboard_data',
                [this.state.dashboardType]
            );

            this.state.dashboardData = data;
            this.state.loading = false;

            // Render charts after data is loaded
            await this.nextTick();
            this.renderCharts();

        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.state.error = error.message || 'Failed to load dashboard data';
            this.state.loading = false;
        }
    }

    async renderCharts() {
        if (!this.state.dashboardData?.charts) return;

        // Destroy existing charts
        this.destroyCharts();

        // Import Chart.js dynamically
        const { Chart, registerables } = await import('chart.js');
        Chart.register(...registerables);

        // Render each chart
        this.state.dashboardData.charts.forEach((chartConfig, index) => {
            const canvas = this.el.querySelector(`#chart_${index}`);
            if (canvas) {
                try {
                    this.charts[index] = new Chart(canvas, {
                        type: chartConfig.type,
                        data: chartConfig.data,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        padding: 20,
                                        usePointStyle: true
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0,0,0,0.8)',
                                    titleColor: 'white',
                                    bodyColor: 'white',
                                    borderColor: 'rgba(255,255,255,0.1)',
                                    borderWidth: 1
                                }
                            },
                            scales: chartConfig.type === 'line' || chartConfig.type === 'bar' ? {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: 'rgba(0,0,0,0.1)'
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    }
                                }
                            } : {}
                        }
                    });
                } catch (error) {
                    console.error(`Error rendering chart ${index}:`, error);
                }
            }
        });
    }

    destroyCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};
    }

    async refreshData() {
        this.notification.add("Refreshing dashboard data...", {
            type: "info",
            title: "Dashboard Refresh"
        });

        await this.loadDashboardData();

        this.notification.add("Dashboard data refreshed successfully!", {
            type: "success",
            title: "Refresh Complete"
        });
    }

    async exportData() {
        try {
            // Placeholder for export functionality
            this.notification.add("Export functionality coming soon!", {
                type: "info",
                title: "Export"
            });
        } catch (error) {
            this.notification.add("Export failed", {
                type: "danger",
                title: "Export Error"
            });
        }
    }

    async exportChart(chart) {
        try {
            // Placeholder for chart export
            this.notification.add(`Exporting chart: ${chart.title}`, {
                type: "info",
                title: "Chart Export"
            });
        } catch (error) {
            this.notification.add("Chart export failed", {
                type: "danger",
                title: "Export Error"
            });
        }
    }

    async refreshChart(chart) {
        try {
            // Refresh specific chart
            await this.loadDashboardData();
            this.notification.add(`Chart refreshed: ${chart.title}`, {
                type: "success",
                title: "Chart Refresh"
            });
        } catch (error) {
            this.notification.add("Chart refresh failed", {
                type: "danger",
                title: "Refresh Error"
            });
        }
    }

    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(() => {
            if (this.state.autoRefreshEnabled && this.__owl__ && this.__owl__.status !== 'destroyed') {
                this.loadDashboardData();
            } else {
                this.stopAutoRefresh();
            }
        }, 30000); // Refresh every 30 seconds
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
}

// Register the component
registry.category("actions").add("analytics_dashboard_new", AnalyticsDashboardNew);
