/* AI Assistant Styles */

.ai_assistant_container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.ai_header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.ai_content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
}

.chat_container {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.chat_messages {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: fadeInUp 0.3s ease-out;
}

.message.user {
    flex-direction: row-reverse;
}

.message_avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.message.user .message_avatar {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white;
}

.message.ai .message_avatar {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    color: white;
}

.message_content {
    max-width: 70%;
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    color: #333;
}

.message.user .message_content {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white;
}

.message.ai .message_content {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
}

.message_text {
    line-height: 1.5;
    white-space: pre-line;
    margin-bottom: 5px;
}

.message_time {
    font-size: 11px;
    opacity: 0.7;
    text-align: right;
}

.message.user .message_time {
    text-align: left;
}

.message_suggestions {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e3e6f0;
}

.message_suggestions .btn {
    font-size: 12px;
    padding: 4px 8px;
}

.chat_input_container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.chat_input_container .form-control {
    border: 2px solid #e3e6f0;
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.chat_input_container .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.chat_input_container .btn-primary {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    transition: all 0.3s ease;
}

.chat_input_container .btn-primary:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
}

.quick_actions .btn {
    border-radius: 20px;
    font-size: 12px;
    padding: 6px 12px;
    transition: all 0.3s ease;
}

.quick_actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Typing indicator */
.typing_indicator {
    display: flex;
    gap: 4px;
    padding: 10px 0;
}

.typing_indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4e73df;
    animation: typing 1.4s infinite ease-in-out;
}

.typing_indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing_indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .ai_content {
        padding: 10px;
    }
    
    .chat_container {
        padding: 15px;
    }
    
    .message_content {
        max-width: 85%;
    }
    
    .quick_actions {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .quick_actions .btn {
        flex: 1;
        min-width: calc(50% - 5px);
        margin: 0;
    }
}

/* Scrollbar styling */
.chat_container::-webkit-scrollbar {
    width: 6px;
}

.chat_container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat_container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat_container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* AI Controls */
.ai_controls .btn {
    border-radius: 25px;
    padding: 8px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.ai_controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
