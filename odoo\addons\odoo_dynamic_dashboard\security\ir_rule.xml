<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Dashboard Record Rules -->
        <record id="rule_dashboard_manager" model="ir.rule">
            <field name="name">Dashboard Manager Access</field>
            <field name="model_id" ref="odoo_dynamic_dashboard.model_dashboard_dashboard"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('odoo_dynamic_dashboard.group_dashboard_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="rule_dashboard_user" model="ir.rule">
            <field name="name">Dashboard User Access</field>
            <field name="model_id" ref="odoo_dynamic_dashboard.model_dashboard_dashboard"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('odoo_dynamic_dashboard.group_dashboard_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Dashboard Block Record Rules -->
        <record id="rule_dashboard_block_manager" model="ir.rule">
            <field name="name">Dashboard Block Manager Access</field>
            <field name="model_id" ref="odoo_dynamic_dashboard.model_dashboard_block"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('odoo_dynamic_dashboard.group_dashboard_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="rule_dashboard_block_user" model="ir.rule">
            <field name="name">Dashboard Block User Access</field>
            <field name="model_id" ref="odoo_dynamic_dashboard.model_dashboard_block"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('odoo_dynamic_dashboard.group_dashboard_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
    </data>
</odoo>