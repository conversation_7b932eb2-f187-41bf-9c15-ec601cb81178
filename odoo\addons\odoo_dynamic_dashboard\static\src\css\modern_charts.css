/* Modern Chart Styles for Advanced Dashboard */

/* Chart Container Enhancements */
.chart-container {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
}

.chart-container:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Modern Chart Legends */
.chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 16px;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    color: #2c3e50;
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* Interactive Chart Elements */
.chart-tooltip {
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

/* Chart Animation Classes */
.chart-animate-in {
    animation: chartFadeIn 0.8s ease-out;
}

@keyframes chartFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Modern Chart Colors */
.chart-color-blue { background: linear-gradient(135deg, #667eea, #764ba2); }
.chart-color-orange { background: linear-gradient(135deg, #f093fb, #f5576c); }
.chart-color-green { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.chart-color-purple { background: linear-gradient(135deg, #a8edea, #fed6e3); }
.chart-color-yellow { background: linear-gradient(135deg, #ffecd2, #fcb69f); }
.chart-color-red { background: linear-gradient(135deg, #ff9a9e, #fecfef); }

/* Chart Header Styles */
.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.chart-subtitle {
    font-size: 14px;
    color: #64748b;
    margin: 4px 0 0 0;
}

.chart-actions {
    display: flex;
    gap: 8px;
}

.chart-action-btn {
    background: rgba(102, 126, 234, 0.1);
    border: none;
    border-radius: 8px;
    padding: 8px;
    color: #667eea;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-action-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.1);
}

/* Responsive Chart Styles */
@media (max-width: 768px) {
    .chart-container {
        padding: 16px;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .chart-legend {
        justify-content: flex-start;
        gap: 8px;
    }
    
    .chart-legend-item {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* Chart Loading States */
.chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #667eea;
}

.chart-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.chart-loading-text {
    font-size: 14px;
    font-weight: 500;
}

/* Chart Error States */
.chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #ef4444;
    text-align: center;
}

.chart-error-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.chart-error-text {
    font-size: 14px;
    font-weight: 500;
}

/* Modern Data Table Styles */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

.data-table th,
.data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.data-table th {
    background: rgba(102, 126, 234, 0.05);
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.data-table td {
    font-size: 14px;
    color: #64748b;
}

.data-table tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* Chart Filters */
.chart-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.chart-filter {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-filter:hover,
.chart-filter.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Chart Statistics */
.chart-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

.chart-stat {
    text-align: center;
    padding: 16px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
}

.chart-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 4px;
}

.chart-stat-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
