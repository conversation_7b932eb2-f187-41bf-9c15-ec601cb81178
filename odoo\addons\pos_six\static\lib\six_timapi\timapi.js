var Module=typeof Module!=="undefined"?Module:{};var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}Module["arguments"]=[];Module["thisProgram"]="./this.program";Module["quit"]=function(status,toThrow){throw toThrow};Module["preRun"]=[];Module["postRun"]=[];var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;ENVIRONMENT_IS_WEB=typeof window==="object";ENVIRONMENT_IS_WORKER=typeof importScripts==="function";ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof require==="function"&&!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER;ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}else{return scriptDirectory+path}}if(ENVIRONMENT_IS_NODE){scriptDirectory=__dirname+"/";var nodeFS;var nodePath;Module["read"]=function shell_read(filename,binary){var ret;if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);ret=nodeFS["readFileSync"](filename);return binary?ret:ret.toString()};Module["readBinary"]=function readBinary(filename){var ret=Module["read"](filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};if(process["argv"].length>1){Module["thisProgram"]=process["argv"][1].replace(/\\/g,"/")}Module["arguments"]=process["argv"].slice(2);if(typeof module!=="undefined"){module["exports"]=Module}process["on"]("uncaughtException",function(ex){if(!(ex instanceof ExitStatus)){throw ex}});process["on"]("unhandledRejection",abort);Module["quit"]=function(status){process["exit"](status)};Module["inspect"]=function(){return"[Emscripten Module object]"}}else if(ENVIRONMENT_IS_SHELL){if(typeof read!="undefined"){Module["read"]=function shell_read(f){return read(f)}}Module["readBinary"]=function readBinary(f){var data;if(typeof readbuffer==="function"){return new Uint8Array(readbuffer(f))}data=read(f,"binary");assert(typeof data==="object");return data};if(typeof scriptArgs!="undefined"){Module["arguments"]=scriptArgs}else if(typeof arguments!="undefined"){Module["arguments"]=arguments}if(typeof quit==="function"){Module["quit"]=function(status){quit(status)}}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.lastIndexOf("/")+1)}else{scriptDirectory=""}Module["read"]=function shell_read(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){Module["readBinary"]=function readBinary(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}Module["readAsync"]=function readAsync(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function xhr_onload(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)};Module["setWindowTitle"]=function(title){document.title=title}}else{}var out=Module["print"]||(typeof console!=="undefined"?console.log.bind(console):typeof print!=="undefined"?print:null);var err=Module["printErr"]||(typeof printErr!=="undefined"?printErr:typeof console!=="undefined"&&console.warn.bind(console)||out);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=undefined;function dynamicAlloc(size){var ret=HEAP32[DYNAMICTOP_PTR>>2];var end=ret+size+15&-16;if(end<=_emscripten_get_heap_size()){HEAP32[DYNAMICTOP_PTR>>2]=end}else{return 0}return ret}function getNativeTypeSize(type){switch(type){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:{if(type[type.length-1]==="*"){return 4}else if(type[0]==="i"){var bits=parseInt(type.substr(1));assert(bits%8===0,"getNativeTypeSize invalid bits "+bits+", type "+type);return bits/8}else{return 0}}}}var asm2wasmImports={"f64-rem":function(x,y){return x%y},"debugger":function(){debugger}};var functionPointers=new Array(0);if(typeof WebAssembly!=="object"){err("no native wasm support detected")}function getValue(ptr,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":return HEAP8[ptr>>0];case"i8":return HEAP8[ptr>>0];case"i16":return HEAP16[ptr>>1];case"i32":return HEAP32[ptr>>2];case"i64":return HEAP32[ptr>>2];case"float":return HEAPF32[ptr>>2];case"double":return HEAPF64[ptr>>3];default:abort("invalid type for getValue: "+type)}return null}var wasmMemory;var wasmTable;var ABORT=false;var EXITSTATUS=0;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}function getCFunc(ident){var func=Module["_"+ident];assert(func,"Cannot call unknown function "+ident+", make sure it is exported");return func}function ccall(ident,returnType,argTypes,args,opts){var toC={"string":function(str){var ret=0;if(str!==null&&str!==undefined&&str!==0){var len=(str.length<<2)+1;ret=stackAlloc(len);stringToUTF8(str,ret,len)}return ret},"array":function(arr){var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string")return UTF8ToString(ret);if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);ret=convertReturnValue(ret);if(stack!==0)stackRestore(stack);return ret}function cwrap(ident,returnType,argTypes,opts){argTypes=argTypes||[];var numericArgs=argTypes.every(function(type){return type==="number"});var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return function(){return ccall(ident,returnType,argTypes,arguments,opts)}}function setValue(ptr,value,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":HEAP8[ptr>>0]=value;break;case"i8":HEAP8[ptr>>0]=value;break;case"i16":HEAP16[ptr>>1]=value;break;case"i32":HEAP32[ptr>>2]=value;break;case"i64":tempI64=[value>>>0,(tempDouble=value,+Math_abs(tempDouble)>=1?tempDouble>0?(Math_min(+Math_floor(tempDouble/4294967296),4294967295)|0)>>>0:~~+Math_ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[ptr>>2]=tempI64[0],HEAP32[ptr+4>>2]=tempI64[1];break;case"float":HEAPF32[ptr>>2]=value;break;case"double":HEAPF64[ptr>>3]=value;break;default:abort("invalid type for setValue: "+type)}}var ALLOC_NORMAL=0;var ALLOC_NONE=3;function allocate(slab,types,allocator,ptr){var zeroinit,size;if(typeof slab==="number"){zeroinit=true;size=slab}else{zeroinit=false;size=slab.length}var singleType=typeof types==="string"?types:null;var ret;if(allocator==ALLOC_NONE){ret=ptr}else{ret=[_malloc,stackAlloc,dynamicAlloc][allocator](Math.max(size,singleType?1:types.length))}if(zeroinit){var stop;ptr=ret;assert((ret&3)==0);stop=ret+(size&~3);for(;ptr<stop;ptr+=4){HEAP32[ptr>>2]=0}stop=ret+size;while(ptr<stop){HEAP8[ptr++>>0]=0}return ret}if(singleType==="i8"){if(slab.subarray||slab.slice){HEAPU8.set(slab,ret)}else{HEAPU8.set(new Uint8Array(slab),ret)}return ret}var i=0,type,typeSize,previousType;while(i<size){var curr=slab[i];type=singleType||types[i];if(type===0){i++;continue}if(type=="i64")type="i32";setValue(ret+i,curr,type);if(previousType!==type){typeSize=getNativeTypeSize(type);previousType=type}i+=typeSize}return ret}function getMemory(size){if(!runtimeInitialized)return dynamicAlloc(size);return _malloc(size)}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(u8Array,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(u8Array[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&u8Array.subarray&&UTF8Decoder){return UTF8Decoder.decode(u8Array.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=u8Array[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=u8Array[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=u8Array[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|u8Array[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function stringToUTF8Array(str,outU8Array,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;outU8Array[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;outU8Array[outIdx++]=192|u>>6;outU8Array[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;outU8Array[outIdx++]=224|u>>12;outU8Array[outIdx++]=128|u>>6&63;outU8Array[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;outU8Array[outIdx++]=240|u>>18;outU8Array[outIdx++]=128|u>>12&63;outU8Array[outIdx++]=128|u>>6&63;outU8Array[outIdx++]=128|u&63}}outU8Array[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}var UTF16Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf-16le"):undefined;function writeArrayToMemory(array,buffer){HEAP8.set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}function demangle(func){return func}function demangleAll(text){var regex=/__Z[\w\d_]+/g;return text.replace(regex,function(x){var y=demangle(x);return x===y?x:y+" ["+x+"]"})}function jsStackTrace(){var err=new Error;if(!err.stack){try{throw new Error(0)}catch(e){err=e}if(!err.stack){return"(no stack trace available)"}}return err.stack.toString()}function stackTrace(){var js=jsStackTrace();if(Module["extraStackTrace"])js+="\n"+Module["extraStackTrace"]();return demangleAll(js)}var WASM_PAGE_SIZE=65536;var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferViews(){Module["HEAP8"]=HEAP8=new Int8Array(buffer);Module["HEAP16"]=HEAP16=new Int16Array(buffer);Module["HEAP32"]=HEAP32=new Int32Array(buffer);Module["HEAPU8"]=HEAPU8=new Uint8Array(buffer);Module["HEAPU16"]=HEAPU16=new Uint16Array(buffer);Module["HEAPU32"]=HEAPU32=new Uint32Array(buffer);Module["HEAPF32"]=HEAPF32=new Float32Array(buffer);Module["HEAPF64"]=HEAPF64=new Float64Array(buffer)}var DYNAMIC_BASE=5535200,DYNAMICTOP_PTR=292288;var TOTAL_STACK=5242880;var INITIAL_TOTAL_MEMORY=Module["TOTAL_MEMORY"]||16777216;if(INITIAL_TOTAL_MEMORY<TOTAL_STACK)err("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+INITIAL_TOTAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")");if(Module["buffer"]){buffer=Module["buffer"]}else{if(typeof WebAssembly==="object"&&typeof WebAssembly.Memory==="function"){wasmMemory=new WebAssembly.Memory({"initial":INITIAL_TOTAL_MEMORY/WASM_PAGE_SIZE,"maximum":INITIAL_TOTAL_MEMORY/WASM_PAGE_SIZE});buffer=wasmMemory.buffer}else{buffer=new ArrayBuffer(INITIAL_TOTAL_MEMORY)}}updateGlobalBufferViews();HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE;function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback();continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){Module["dynCall_v"](func)}else{Module["dynCall_vi"](func,callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){if(runtimeInitialized)return;runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var Math_abs=Math.abs;var Math_ceil=Math.ceil;var Math_floor=Math.floor;var Math_min=Math.min;var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return String.prototype.startsWith?filename.startsWith(dataURIPrefix):filename.indexOf(dataURIPrefix)===0}var wasmBinaryFile='/pos_six/static/lib/six_timapi/timapi.wasm';function getBinary(){try{if(Module["wasmBinary"]){return new Uint8Array(Module["wasmBinary"])}if(Module["readBinary"]){return Module["readBinary"](wasmBinaryFile)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!Module["wasmBinary"]&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary()})}return new Promise(function(resolve,reject){resolve(getBinary())})}function createWasm(env){var info={"env":env,"global":{"NaN":NaN,Infinity:Infinity},"global.Math":Math,"asm2wasm":asm2wasmImports};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}function receiveInstantiatedSource(output){receiveInstance(output["instance"])}function instantiateArrayBuffer(receiver){getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}if(!Module["wasmBinary"]&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&typeof fetch==="function"){WebAssembly.instantiateStreaming(fetch(wasmBinaryFile,{credentials:"same-origin"}),info).then(receiveInstantiatedSource,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");instantiateArrayBuffer(receiveInstantiatedSource)})}else{instantiateArrayBuffer(receiveInstantiatedSource)}return{}}Module["asm"]=function(global,env,providedBuffer){env["memory"]=wasmMemory;env["table"]=wasmTable=new WebAssembly.Table({"initial":5216,"maximum":5216,"element":"anyfunc"});env["__memory_base"]=1024;env["__table_base"]=0;var exports=createWasm(env);return exports};__ATINIT__.push({func:function(){___emscripten_environ_constructor()}});function _TAWACancelTimeout(slot){try{timapi._TimApiHelpers.TimerWrapper.cancelTimeout(slot)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWACancelTimeout")}}function _TAWADeferredAuth(terminal,response){try{timapi._TimApiHelpers.deferredAuth(terminal,response)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWADeferredAuth")}}function _TAWAErrorNotification(terminal,timError){try{timapi._TimApiHelpers.errorNotification(terminal,timError)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWAErrorNotification")}}function _TAWALicenseChanged(terminal,response){try{timapi._TimApiHelpers.licenseChanged(terminal,response)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWALicenseChanged")}}function _TAWAPublishLogRecord(record,userPointer){try{timapi._TimApiHelpers.publishLogRecord(record,userPointer)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWAPublishLogRecord")}}function _TAWAScreenshot(terminal,info){try{timapi._TimApiHelpers.screenshot(terminal,info)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWAScreenshot")}}function _TAWASocketClose(slot){try{timapi._TimApiHelpers.CommWebSocket.closeSocket(slot)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWASocketClose");if(err instanceof timapi.TimException){return err.getResultCode()}else{return timapi.constants.ResultCode.systemError._value}}return timapi.constants.ResultCode.ok._value}function _TAWASocketCreate(slot,ip,port){try{timapi._TimApiHelpers.CommWebSocket.createSocket(slot,Module.UTF8ToString(ip),port)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWASocketCreate");if(err instanceof timapi.TimException){return err.getResultCode()}else{return timapi.constants.ResultCode.systemError._value}}return timapi.constants.ResultCode.ok._value}function _TAWASocketSendMessage(slot,message,length){try{timapi._TimApiHelpers.CommWebSocket.sendMessage(slot,new Uint8Array(Module.HEAPU8.buffer,message,length))}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWASocketSendMessage");if(err instanceof timapi.TimException){return err.getResultCode()}else{return timapi.constants.ResultCode.systemError._value}}return timapi.constants.ResultCode.ok._value}function _TAWAStartTimeout(slot,repeat,timeoutMS){try{timapi._TimApiHelpers.TimerWrapper.startTimeout(slot,repeat==1,timeoutMS)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWAStartTimeout");return 1}return 0}function _TAWATerminalCompleted(event,data){try{timapi._TimApiHelpers.terminalCompleted(event,data)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWATerminalCompleted")}}function _TAWATerminalDisconnected(terminal,result_code){try{timapi._TimApiHelpers.terminalDisconnected(terminal,result_code)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWATerminalDisconnected")}}function _TAWATerminalStatusChanged(terminal){try{timapi._TimApiHelpers.terminalStatusChanged(terminal)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWATerminalStatusChanged")}}function _TAWAVasInfo(terminal,vas_checkout_information){try{timapi._TimApiHelpers.vasInfo(terminal,vas_checkout_information)}catch(err){timapi._TimApiHelpers.internalError(err,"timapi_lib.js","TAWAVasInfo")}}var ENV={};function ___buildEnvironment(environ){var MAX_ENV_VALUES=64;var TOTAL_ENV_SIZE=1024;var poolPtr;var envPtr;if(!___buildEnvironment.called){___buildEnvironment.called=true;ENV["USER"]=ENV["LOGNAME"]="web_user";ENV["PATH"]="/";ENV["PWD"]="/";ENV["HOME"]="/home/<USER>";ENV["LANG"]="C.UTF-8";ENV["_"]=Module["thisProgram"];poolPtr=getMemory(TOTAL_ENV_SIZE);envPtr=getMemory(MAX_ENV_VALUES*4);HEAP32[envPtr>>2]=poolPtr;HEAP32[environ>>2]=envPtr}else{envPtr=HEAP32[environ>>2];poolPtr=HEAP32[envPtr>>2]}var strings=[];var totalSize=0;for(var key in ENV){if(typeof ENV[key]==="string"){var line=key+"="+ENV[key];strings.push(line);totalSize+=line.length}}if(totalSize>TOTAL_ENV_SIZE){throw new Error("Environment size exceeded TOTAL_ENV_SIZE!")}var ptrSize=4;for(var i=0;i<strings.length;i++){var line=strings[i];writeAsciiToMemory(line,poolPtr);HEAP32[envPtr+i*ptrSize>>2]=poolPtr;poolPtr+=line.length+1}HEAP32[envPtr+strings.length*ptrSize>>2]=0}var SYSCALLS={buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:0,get:function(varargs){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(){var ret=UTF8ToString(SYSCALLS.get());return ret},get64:function(){var low=SYSCALLS.get(),high=SYSCALLS.get();return low},getZero:function(){SYSCALLS.get()}};function ___syscall140(which,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(),offset_high=SYSCALLS.get(),offset_low=SYSCALLS.get(),result=SYSCALLS.get(),whence=SYSCALLS.get();return 0}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___syscall146(which,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.get(),iov=SYSCALLS.get(),iovcnt=SYSCALLS.get();var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov+i*8>>2];var len=HEAP32[iov+(i*8+4)>>2];for(var j=0;j<len;j++){SYSCALLS.printChar(stream,HEAPU8[ptr+j])}ret+=len}return ret}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___syscall6(which,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD();return 0}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function _emscripten_get_heap_size(){return HEAP8.length}function abortOnCannotGrowMemory(requestedSize){abort("OOM")}function _emscripten_resize_heap(requestedSize){abortOnCannotGrowMemory(requestedSize)}function _llvm_stackrestore(p){var self=_llvm_stacksave;var ret=self.LLVM_SAVEDSTACKS[p];self.LLVM_SAVEDSTACKS.splice(p,1);stackRestore(ret)}function _llvm_stacksave(){var self=_llvm_stacksave;if(!self.LLVM_SAVEDSTACKS){self.LLVM_SAVEDSTACKS=[]}self.LLVM_SAVEDSTACKS.push(stackSave());return self.LLVM_SAVEDSTACKS.length-1}var ___tm_current=292144;var ___tm_timezone=(stringToUTF8("GMT",292192,4),292192);function _tzset(){if(_tzset.called)return;_tzset.called=true;HEAP32[__get_timezone()>>2]=(new Date).getTimezoneOffset()*60;var winter=new Date(2e3,0,1);var summer=new Date(2e3,6,1);HEAP32[__get_daylight()>>2]=Number(winter.getTimezoneOffset()!=summer.getTimezoneOffset());function extractZone(date){var match=date.toTimeString().match(/\(([A-Za-z ]+)\)$/);return match?match[1]:"GMT"}var winterName=extractZone(winter);var summerName=extractZone(summer);var winterNamePtr=allocate(intArrayFromString(winterName),"i8",ALLOC_NORMAL);var summerNamePtr=allocate(intArrayFromString(summerName),"i8",ALLOC_NORMAL);if(summer.getTimezoneOffset()<winter.getTimezoneOffset()){HEAP32[__get_tzname()>>2]=winterNamePtr;HEAP32[__get_tzname()+4>>2]=summerNamePtr}else{HEAP32[__get_tzname()>>2]=summerNamePtr;HEAP32[__get_tzname()+4>>2]=winterNamePtr}}function _localtime_r(time,tmPtr){_tzset();var date=new Date(HEAP32[time>>2]*1e3);HEAP32[tmPtr>>2]=date.getSeconds();HEAP32[tmPtr+4>>2]=date.getMinutes();HEAP32[tmPtr+8>>2]=date.getHours();HEAP32[tmPtr+12>>2]=date.getDate();HEAP32[tmPtr+16>>2]=date.getMonth();HEAP32[tmPtr+20>>2]=date.getFullYear()-1900;HEAP32[tmPtr+24>>2]=date.getDay();var start=new Date(date.getFullYear(),0,1);var yday=(date.getTime()-start.getTime())/(1e3*60*60*24)|0;HEAP32[tmPtr+28>>2]=yday;HEAP32[tmPtr+36>>2]=-(date.getTimezoneOffset()*60);var summerOffset=new Date(2e3,6,1).getTimezoneOffset();var winterOffset=start.getTimezoneOffset();var dst=(summerOffset!=winterOffset&&date.getTimezoneOffset()==Math.min(winterOffset,summerOffset))|0;HEAP32[tmPtr+32>>2]=dst;var zonePtr=HEAP32[__get_tzname()+(dst?4:0)>>2];HEAP32[tmPtr+40>>2]=zonePtr;return tmPtr}function _localtime(time){return _localtime_r(time,___tm_current)}function _emscripten_memcpy_big(dest,src,num){HEAPU8.set(HEAPU8.subarray(src,src+num),dest)}function ___setErrNo(value){if(Module["___errno_location"])HEAP32[Module["___errno_location"]()>>2]=value;return value}function _time(ptr){var ret=Date.now()/1e3|0;if(ptr){HEAP32[ptr>>2]=ret}return ret}function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var asmGlobalArg={};var asmLibraryArg={"b":abort,"C":_TAWACancelTimeout,"j":_TAWADeferredAuth,"i":_TAWAErrorNotification,"h":_TAWALicenseChanged,"s":_TAWAPublishLogRecord,"g":_TAWAScreenshot,"r":_TAWASocketClose,"q":_TAWASocketCreate,"f":_TAWASocketSendMessage,"B":_TAWAStartTimeout,"p":_TAWATerminalCompleted,"o":_TAWATerminalDisconnected,"n":_TAWATerminalStatusChanged,"m":_TAWAVasInfo,"A":___buildEnvironment,"l":___setErrNo,"z":___syscall140,"k":___syscall146,"y":___syscall6,"x":_emscripten_get_heap_size,"w":_emscripten_memcpy_big,"v":_emscripten_resize_heap,"e":_llvm_stackrestore,"d":_llvm_stacksave,"u":_localtime,"c":_time,"t":abortOnCannotGrowMemory,"a":DYNAMICTOP_PTR};var asm=Module["asm"](asmGlobalArg,asmLibraryArg,buffer);Module["asm"]=asm;var _TAWAAmountCreate=Module["_TAWAAmountCreate"]=function(){return Module["asm"]["D"].apply(null,arguments)};var _TAWAAmountDccGetValue=Module["_TAWAAmountDccGetValue"]=function(){return Module["asm"]["E"].apply(null,arguments)};var _TAWAAmountDiscountCreate=Module["_TAWAAmountDiscountCreate"]=function(){return Module["asm"]["F"].apply(null,arguments)};var _TAWAAmountDiscountGetValue=Module["_TAWAAmountDiscountGetValue"]=function(){return Module["asm"]["G"].apply(null,arguments)};var _TAWAAmountFinalGetValue=Module["_TAWAAmountFinalGetValue"]=function(){return Module["asm"]["H"].apply(null,arguments)};var _TAWAAmountGetValue=Module["_TAWAAmountGetValue"]=function(){return Module["asm"]["I"].apply(null,arguments)};var _TAWAIntegerCreate=Module["_TAWAIntegerCreate"]=function(){return Module["asm"]["J"].apply(null,arguments)};var _TAWAIntegerGetValue=Module["_TAWAIntegerGetValue"]=function(){return Module["asm"]["K"].apply(null,arguments)};var _TAWALoyaltyDiscountGetValue=Module["_TAWALoyaltyDiscountGetValue"]=function(){return Module["asm"]["L"].apply(null,arguments)};var _TAWAOnTimeout=Module["_TAWAOnTimeout"]=function(){return Module["asm"]["M"].apply(null,arguments)};var _TAWAOnWebSocketClosed=Module["_TAWAOnWebSocketClosed"]=function(){return Module["asm"]["N"].apply(null,arguments)};var _TAWAOnWebSocketError=Module["_TAWAOnWebSocketError"]=function(){return Module["asm"]["O"].apply(null,arguments)};var _TAWAOnWebSocketMessage=Module["_TAWAOnWebSocketMessage"]=function(){return Module["asm"]["P"].apply(null,arguments)};var _TAWAOnWebSocketOpen=Module["_TAWAOnWebSocketOpen"]=function(){return Module["asm"]["Q"].apply(null,arguments)};var ___emscripten_environ_constructor=Module["___emscripten_environ_constructor"]=function(){return Module["asm"]["R"].apply(null,arguments)};var ___errno_location=Module["___errno_location"]=function(){return Module["asm"]["S"].apply(null,arguments)};var ___six_internal_test=Module["___six_internal_test"]=function(){return Module["asm"]["T"].apply(null,arguments)};var __get_daylight=Module["__get_daylight"]=function(){return Module["asm"]["U"].apply(null,arguments)};var __get_timezone=Module["__get_timezone"]=function(){return Module["asm"]["V"].apply(null,arguments)};var __get_tzname=Module["__get_tzname"]=function(){return Module["asm"]["W"].apply(null,arguments)};var _free=Module["_free"]=function(){return Module["asm"]["X"].apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return Module["asm"]["Y"].apply(null,arguments)};var _ta_activate_response_get_act_seq_counter=Module["_ta_activate_response_get_act_seq_counter"]=function(){return Module["asm"]["Z"].apply(null,arguments)};var _ta_activate_response_get_print_data=Module["_ta_activate_response_get_print_data"]=function(){return Module["asm"]["_"].apply(null,arguments)};var _ta_adjustment_result_e2s=Module["_ta_adjustment_result_e2s"]=function(){return Module["asm"]["$"].apply(null,arguments)};var _ta_admin_functions_e2s=Module["_ta_admin_functions_e2s"]=function(){return Module["asm"]["aa"].apply(null,arguments)};var _ta_amount_create=Module["_ta_amount_create"]=function(){return Module["asm"]["ba"].apply(null,arguments)};var _ta_amount_create_decimal=Module["_ta_amount_create_decimal"]=function(){return Module["asm"]["ca"].apply(null,arguments)};var _ta_amount_create_decimal_exponent=Module["_ta_amount_create_decimal_exponent"]=function(){return Module["asm"]["da"].apply(null,arguments)};var _ta_amount_create_exponent=Module["_ta_amount_create_exponent"]=function(){return Module["asm"]["ea"].apply(null,arguments)};var _ta_amount_dcc_get_currency=Module["_ta_amount_dcc_get_currency"]=function(){return Module["asm"]["fa"].apply(null,arguments)};var _ta_amount_dcc_get_decimal_value=Module["_ta_amount_dcc_get_decimal_value"]=function(){return Module["asm"]["ga"].apply(null,arguments)};var _ta_amount_dcc_get_exponent=Module["_ta_amount_dcc_get_exponent"]=function(){return Module["asm"]["ha"].apply(null,arguments)};var _ta_amount_dcc_get_markup=Module["_ta_amount_dcc_get_markup"]=function(){return Module["asm"]["ia"].apply(null,arguments)};var _ta_amount_dcc_get_markup_decimal=Module["_ta_amount_dcc_get_markup_decimal"]=function(){return Module["asm"]["ja"].apply(null,arguments)};var _ta_amount_dcc_get_markup_decimal_regulated=Module["_ta_amount_dcc_get_markup_decimal_regulated"]=function(){return Module["asm"]["ka"].apply(null,arguments)};var _ta_amount_dcc_get_markup_exponent=Module["_ta_amount_dcc_get_markup_exponent"]=function(){return Module["asm"]["la"].apply(null,arguments)};var _ta_amount_dcc_get_markup_exponent_regulated=Module["_ta_amount_dcc_get_markup_exponent_regulated"]=function(){return Module["asm"]["ma"].apply(null,arguments)};var _ta_amount_dcc_get_markup_regulated=Module["_ta_amount_dcc_get_markup_regulated"]=function(){return Module["asm"]["na"].apply(null,arguments)};var _ta_amount_dcc_get_rate=Module["_ta_amount_dcc_get_rate"]=function(){return Module["asm"]["oa"].apply(null,arguments)};var _ta_amount_dcc_get_rate_decimal=Module["_ta_amount_dcc_get_rate_decimal"]=function(){return Module["asm"]["pa"].apply(null,arguments)};var _ta_amount_dcc_get_rate_decimal_regulated=Module["_ta_amount_dcc_get_rate_decimal_regulated"]=function(){return Module["asm"]["qa"].apply(null,arguments)};var _ta_amount_dcc_get_rate_exponent=Module["_ta_amount_dcc_get_rate_exponent"]=function(){return Module["asm"]["ra"].apply(null,arguments)};var _ta_amount_dcc_get_rate_exponent_regulated=Module["_ta_amount_dcc_get_rate_exponent_regulated"]=function(){return Module["asm"]["sa"].apply(null,arguments)};var _ta_amount_dcc_get_rate_regulated=Module["_ta_amount_dcc_get_rate_regulated"]=function(){return Module["asm"]["ta"].apply(null,arguments)};var _ta_amount_dcc_get_value=Module["_ta_amount_dcc_get_value"]=function(){return Module["asm"]["ua"].apply(null,arguments)};var _ta_amount_discount_create=Module["_ta_amount_discount_create"]=function(){return Module["asm"]["va"].apply(null,arguments)};var _ta_amount_discount_create_decimal=Module["_ta_amount_discount_create_decimal"]=function(){return Module["asm"]["wa"].apply(null,arguments)};var _ta_amount_discount_create_decimal_exponent=Module["_ta_amount_discount_create_decimal_exponent"]=function(){return Module["asm"]["xa"].apply(null,arguments)};var _ta_amount_discount_create_exponent=Module["_ta_amount_discount_create_exponent"]=function(){return Module["asm"]["ya"].apply(null,arguments)};var _ta_amount_discount_get_currency=Module["_ta_amount_discount_get_currency"]=function(){return Module["asm"]["za"].apply(null,arguments)};var _ta_amount_discount_get_decimal_value=Module["_ta_amount_discount_get_decimal_value"]=function(){return Module["asm"]["Aa"].apply(null,arguments)};var _ta_amount_discount_get_discount_id=Module["_ta_amount_discount_get_discount_id"]=function(){return Module["asm"]["Ba"].apply(null,arguments)};var _ta_amount_discount_get_exponent=Module["_ta_amount_discount_get_exponent"]=function(){return Module["asm"]["Ca"].apply(null,arguments)};var _ta_amount_discount_get_value=Module["_ta_amount_discount_get_value"]=function(){return Module["asm"]["Da"].apply(null,arguments)};var _ta_amount_discount_set_currency=Module["_ta_amount_discount_set_currency"]=function(){return Module["asm"]["Ea"].apply(null,arguments)};var _ta_amount_discount_set_decimal_value=Module["_ta_amount_discount_set_decimal_value"]=function(){return Module["asm"]["Fa"].apply(null,arguments)};var _ta_amount_discount_set_discount_id=Module["_ta_amount_discount_set_discount_id"]=function(){return Module["asm"]["Ga"].apply(null,arguments)};var _ta_amount_discount_set_exponent=Module["_ta_amount_discount_set_exponent"]=function(){return Module["asm"]["Ha"].apply(null,arguments)};var _ta_amount_discount_set_value=Module["_ta_amount_discount_set_value"]=function(){return Module["asm"]["Ia"].apply(null,arguments)};var _ta_amount_final_get_adjustment_result=Module["_ta_amount_final_get_adjustment_result"]=function(){return Module["asm"]["Ja"].apply(null,arguments)};var _ta_amount_final_get_currency=Module["_ta_amount_final_get_currency"]=function(){return Module["asm"]["Ka"].apply(null,arguments)};var _ta_amount_final_get_decimal_value=Module["_ta_amount_final_get_decimal_value"]=function(){return Module["asm"]["La"].apply(null,arguments)};var _ta_amount_final_get_exponent=Module["_ta_amount_final_get_exponent"]=function(){return Module["asm"]["Ma"].apply(null,arguments)};var _ta_amount_final_get_value=Module["_ta_amount_final_get_value"]=function(){return Module["asm"]["Na"].apply(null,arguments)};var _ta_amount_get_currency=Module["_ta_amount_get_currency"]=function(){return Module["asm"]["Oa"].apply(null,arguments)};var _ta_amount_get_decimal_value=Module["_ta_amount_get_decimal_value"]=function(){return Module["asm"]["Pa"].apply(null,arguments)};var _ta_amount_get_exponent=Module["_ta_amount_get_exponent"]=function(){return Module["asm"]["Qa"].apply(null,arguments)};var _ta_amount_get_value=Module["_ta_amount_get_value"]=function(){return Module["asm"]["Ra"].apply(null,arguments)};var _ta_amount_set_currency=Module["_ta_amount_set_currency"]=function(){return Module["asm"]["Sa"].apply(null,arguments)};var _ta_amount_set_decimal_value=Module["_ta_amount_set_decimal_value"]=function(){return Module["asm"]["Ta"].apply(null,arguments)};var _ta_amount_set_exponent=Module["_ta_amount_set_exponent"]=function(){return Module["asm"]["Ua"].apply(null,arguments)};var _ta_amount_set_value=Module["_ta_amount_set_value"]=function(){return Module["asm"]["Va"].apply(null,arguments)};var _ta_application_get_aid=Module["_ta_application_get_aid"]=function(){return Module["asm"]["Wa"].apply(null,arguments)};var _ta_application_get_label=Module["_ta_application_get_label"]=function(){return Module["asm"]["Xa"].apply(null,arguments)};var _ta_balance_inquiry_response_get_amount=Module["_ta_balance_inquiry_response_get_amount"]=function(){return Module["asm"]["Ya"].apply(null,arguments)};var _ta_balance_inquiry_response_get_card_data=Module["_ta_balance_inquiry_response_get_card_data"]=function(){return Module["asm"]["Za"].apply(null,arguments)};var _ta_balance_inquiry_response_get_disclaimer=Module["_ta_balance_inquiry_response_get_disclaimer"]=function(){return Module["asm"]["_a"].apply(null,arguments)};var _ta_balance_inquiry_response_get_print_data=Module["_ta_balance_inquiry_response_get_print_data"]=function(){return Module["asm"]["$a"].apply(null,arguments)};var _ta_balance_inquiry_response_get_transaction_information=Module["_ta_balance_inquiry_response_get_transaction_information"]=function(){return Module["asm"]["ab"].apply(null,arguments)};var _ta_balance_response_get_counters=Module["_ta_balance_response_get_counters"]=function(){return Module["asm"]["bb"].apply(null,arguments)};var _ta_balance_response_get_partial_exceptions=Module["_ta_balance_response_get_partial_exceptions"]=function(){return Module["asm"]["cb"].apply(null,arguments)};var _ta_balance_response_get_print_data=Module["_ta_balance_response_get_print_data"]=function(){return Module["asm"]["db"].apply(null,arguments)};var _ta_basket_copy=Module["_ta_basket_copy"]=function(){return Module["asm"]["eb"].apply(null,arguments)};var _ta_basket_create=Module["_ta_basket_create"]=function(){return Module["asm"]["fb"].apply(null,arguments)};var _ta_basket_get_items=Module["_ta_basket_get_items"]=function(){return Module["asm"]["gb"].apply(null,arguments)};var _ta_basket_get_loyalty_auth_result=Module["_ta_basket_get_loyalty_auth_result"]=function(){return Module["asm"]["hb"].apply(null,arguments)};var _ta_basket_item_copy=Module["_ta_basket_item_copy"]=function(){return Module["asm"]["ib"].apply(null,arguments)};var _ta_basket_item_create=Module["_ta_basket_item_create"]=function(){return Module["asm"]["jb"].apply(null,arguments)};var _ta_basket_item_get_amount=Module["_ta_basket_item_get_amount"]=function(){return Module["asm"]["kb"].apply(null,arguments)};var _ta_basket_item_get_amount_discount=Module["_ta_basket_item_get_amount_discount"]=function(){return Module["asm"]["lb"].apply(null,arguments)};var _ta_basket_item_get_amount_gross=Module["_ta_basket_item_get_amount_gross"]=function(){return Module["asm"]["mb"].apply(null,arguments)};var _ta_basket_item_get_amount_tax=Module["_ta_basket_item_get_amount_tax"]=function(){return Module["asm"]["nb"].apply(null,arguments)};var _ta_basket_item_get_amount_total=Module["_ta_basket_item_get_amount_total"]=function(){return Module["asm"]["ob"].apply(null,arguments)};var _ta_basket_item_get_auth_result=Module["_ta_basket_item_get_auth_result"]=function(){return Module["asm"]["pb"].apply(null,arguments)};var _ta_basket_item_get_item_id=Module["_ta_basket_item_get_item_id"]=function(){return Module["asm"]["qb"].apply(null,arguments)};var _ta_basket_item_get_item_quantity=Module["_ta_basket_item_get_item_quantity"]=function(){return Module["asm"]["rb"].apply(null,arguments)};var _ta_basket_item_get_loyalty_id=Module["_ta_basket_item_get_loyalty_id"]=function(){return Module["asm"]["sb"].apply(null,arguments)};var _ta_basket_item_get_prod_description=Module["_ta_basket_item_get_prod_description"]=function(){return Module["asm"]["tb"].apply(null,arguments)};var _ta_basket_item_get_unit_amount_discount=Module["_ta_basket_item_get_unit_amount_discount"]=function(){return Module["asm"]["ub"].apply(null,arguments)};var _ta_basket_item_get_unit_amount_gross=Module["_ta_basket_item_get_unit_amount_gross"]=function(){return Module["asm"]["vb"].apply(null,arguments)};var _ta_basket_item_set_amount=Module["_ta_basket_item_set_amount"]=function(){return Module["asm"]["wb"].apply(null,arguments)};var _ta_basket_item_set_amount_discount=Module["_ta_basket_item_set_amount_discount"]=function(){return Module["asm"]["xb"].apply(null,arguments)};var _ta_basket_item_set_amount_gross=Module["_ta_basket_item_set_amount_gross"]=function(){return Module["asm"]["yb"].apply(null,arguments)};var _ta_basket_item_set_amount_tax=Module["_ta_basket_item_set_amount_tax"]=function(){return Module["asm"]["zb"].apply(null,arguments)};var _ta_basket_item_set_amount_total=Module["_ta_basket_item_set_amount_total"]=function(){return Module["asm"]["Ab"].apply(null,arguments)};var _ta_basket_item_set_auth_result=Module["_ta_basket_item_set_auth_result"]=function(){return Module["asm"]["Bb"].apply(null,arguments)};var _ta_basket_item_set_item_id=Module["_ta_basket_item_set_item_id"]=function(){return Module["asm"]["Cb"].apply(null,arguments)};var _ta_basket_item_set_item_quantity=Module["_ta_basket_item_set_item_quantity"]=function(){return Module["asm"]["Db"].apply(null,arguments)};var _ta_basket_item_set_loyalty_id=Module["_ta_basket_item_set_loyalty_id"]=function(){return Module["asm"]["Eb"].apply(null,arguments)};var _ta_basket_item_set_prod_description=Module["_ta_basket_item_set_prod_description"]=function(){return Module["asm"]["Fb"].apply(null,arguments)};var _ta_basket_item_set_unit_amount_discount=Module["_ta_basket_item_set_unit_amount_discount"]=function(){return Module["asm"]["Gb"].apply(null,arguments)};var _ta_basket_item_set_unit_amount_gross=Module["_ta_basket_item_set_unit_amount_gross"]=function(){return Module["asm"]["Hb"].apply(null,arguments)};var _ta_basket_set_items=Module["_ta_basket_set_items"]=function(){return Module["asm"]["Ib"].apply(null,arguments)};var _ta_basket_set_loyalty_auth_result=Module["_ta_basket_set_loyalty_auth_result"]=function(){return Module["asm"]["Jb"].apply(null,arguments)};var _ta_boolean_create=Module["_ta_boolean_create"]=function(){return Module["asm"]["Kb"].apply(null,arguments)};var _ta_boolean_get_value=Module["_ta_boolean_get_value"]=function(){return Module["asm"]["Lb"].apply(null,arguments)};var _ta_brand_bar_brand_e2s=Module["_ta_brand_bar_brand_e2s"]=function(){return Module["asm"]["Mb"].apply(null,arguments)};var _ta_brand_get_acq_id=Module["_ta_brand_get_acq_id"]=function(){return Module["asm"]["Nb"].apply(null,arguments)};var _ta_brand_get_applications=Module["_ta_brand_get_applications"]=function(){return Module["asm"]["Ob"].apply(null,arguments)};var _ta_brand_get_currencies=Module["_ta_brand_get_currencies"]=function(){return Module["asm"]["Pb"].apply(null,arguments)};var _ta_brand_get_dcc_available=Module["_ta_brand_get_dcc_available"]=function(){return Module["asm"]["Qb"].apply(null,arguments)};var _ta_brand_get_last_init_date=Module["_ta_brand_get_last_init_date"]=function(){return Module["asm"]["Rb"].apply(null,arguments)};var _ta_brand_get_name=Module["_ta_brand_get_name"]=function(){return Module["asm"]["Sb"].apply(null,arguments)};var _ta_brand_get_payment_protocol=Module["_ta_brand_get_payment_protocol"]=function(){return Module["asm"]["Tb"].apply(null,arguments)};var _ta_brand_mode_e2s=Module["_ta_brand_mode_e2s"]=function(){return Module["asm"]["Ub"].apply(null,arguments)};var _ta_card_data_get_acc=Module["_ta_card_data_get_acc"]=function(){return Module["asm"]["Vb"].apply(null,arguments)};var _ta_card_data_get_aid=Module["_ta_card_data_get_aid"]=function(){return Module["asm"]["Wb"].apply(null,arguments)};var _ta_card_data_get_asrpd=Module["_ta_card_data_get_asrpd"]=function(){return Module["asm"]["Xb"].apply(null,arguments)};var _ta_card_data_get_brand_name=Module["_ta_card_data_get_brand_name"]=function(){return Module["asm"]["Yb"].apply(null,arguments)};var _ta_card_data_get_card_country_code=Module["_ta_card_data_get_card_country_code"]=function(){return Module["asm"]["Zb"].apply(null,arguments)};var _ta_card_data_get_card_expiry_date=Module["_ta_card_data_get_card_expiry_date"]=function(){return Module["asm"]["_b"].apply(null,arguments)};var _ta_card_data_get_card_number=Module["_ta_card_data_get_card_number"]=function(){return Module["asm"]["$b"].apply(null,arguments)};var _ta_card_data_get_card_number_enc=Module["_ta_card_data_get_card_number_enc"]=function(){return Module["asm"]["ac"].apply(null,arguments)};var _ta_card_data_get_card_number_enc_key_index=Module["_ta_card_data_get_card_number_enc_key_index"]=function(){return Module["asm"]["bc"].apply(null,arguments)};var _ta_card_data_get_card_number_printable=Module["_ta_card_data_get_card_number_printable"]=function(){return Module["asm"]["cc"].apply(null,arguments)};var _ta_card_data_get_card_number_printable_cardholder=Module["_ta_card_data_get_card_number_printable_cardholder"]=function(){return Module["asm"]["dc"].apply(null,arguments)};var _ta_card_data_get_card_product_type=Module["_ta_card_data_get_card_product_type"]=function(){return Module["asm"]["ec"].apply(null,arguments)};var _ta_card_data_get_card_ref=Module["_ta_card_data_get_card_ref"]=function(){return Module["asm"]["fc"].apply(null,arguments)};var _ta_card_data_get_card_track_datas=Module["_ta_card_data_get_card_track_datas"]=function(){return Module["asm"]["gc"].apply(null,arguments)};var _ta_card_data_get_card_type=Module["_ta_card_data_get_card_type"]=function(){return Module["asm"]["hc"].apply(null,arguments)};var _ta_card_data_get_cardholder=Module["_ta_card_data_get_cardholder"]=function(){return Module["asm"]["ic"].apply(null,arguments)};var _ta_card_data_get_language=Module["_ta_card_data_get_language"]=function(){return Module["asm"]["jc"].apply(null,arguments)};var _ta_card_data_get_loyalty_information=Module["_ta_card_data_get_loyalty_information"]=function(){return Module["asm"]["kc"].apply(null,arguments)};var _ta_card_data_get_pos_entry_mode=Module["_ta_card_data_get_pos_entry_mode"]=function(){return Module["asm"]["lc"].apply(null,arguments)};var _ta_card_data_get_processing_disposition=Module["_ta_card_data_get_processing_disposition"]=function(){return Module["asm"]["mc"].apply(null,arguments)};var _ta_card_data_get_tender_name=Module["_ta_card_data_get_tender_name"]=function(){return Module["asm"]["nc"].apply(null,arguments)};var _ta_card_data_get_terminal_country_code=Module["_ta_card_data_get_terminal_country_code"]=function(){return Module["asm"]["oc"].apply(null,arguments)};var _ta_card_data_get_uid=Module["_ta_card_data_get_uid"]=function(){return Module["asm"]["pc"].apply(null,arguments)};var _ta_card_product_type_e2s=Module["_ta_card_product_type_e2s"]=function(){return Module["asm"]["qc"].apply(null,arguments)};var _ta_card_reader_e2s=Module["_ta_card_reader_e2s"]=function(){return Module["asm"]["rc"].apply(null,arguments)};var _ta_card_reader_status_e2s=Module["_ta_card_reader_status_e2s"]=function(){return Module["asm"]["sc"].apply(null,arguments)};var _ta_card_track_data_get_data=Module["_ta_card_track_data_get_data"]=function(){return Module["asm"]["tc"].apply(null,arguments)};var _ta_card_track_data_get_track_number=Module["_ta_card_track_data_get_track_number"]=function(){return Module["asm"]["uc"].apply(null,arguments)};var _ta_command_request_create=Module["_ta_command_request_create"]=function(){return Module["asm"]["vc"].apply(null,arguments)};var _ta_command_request_get_card_command=Module["_ta_command_request_get_card_command"]=function(){return Module["asm"]["wc"].apply(null,arguments)};var _ta_command_request_get_card_reader=Module["_ta_command_request_get_card_reader"]=function(){return Module["asm"]["xc"].apply(null,arguments)};var _ta_command_request_get_execution_resource=Module["_ta_command_request_get_execution_resource"]=function(){return Module["asm"]["yc"].apply(null,arguments)};var _ta_command_request_get_negative_resource=Module["_ta_command_request_get_negative_resource"]=function(){return Module["asm"]["zc"].apply(null,arguments)};var _ta_command_request_get_order=Module["_ta_command_request_get_order"]=function(){return Module["asm"]["Ac"].apply(null,arguments)};var _ta_command_request_get_positive_answers=Module["_ta_command_request_get_positive_answers"]=function(){return Module["asm"]["Bc"].apply(null,arguments)};var _ta_command_request_get_positive_resource=Module["_ta_command_request_get_positive_resource"]=function(){return Module["asm"]["Cc"].apply(null,arguments)};var _ta_command_request_get_pre_resource=Module["_ta_command_request_get_pre_resource"]=function(){return Module["asm"]["Dc"].apply(null,arguments)};var _ta_command_request_set_card_command=Module["_ta_command_request_set_card_command"]=function(){return Module["asm"]["Ec"].apply(null,arguments)};var _ta_command_request_set_card_reader=Module["_ta_command_request_set_card_reader"]=function(){return Module["asm"]["Fc"].apply(null,arguments)};var _ta_command_request_set_execution_resource=Module["_ta_command_request_set_execution_resource"]=function(){return Module["asm"]["Gc"].apply(null,arguments)};var _ta_command_request_set_negative_resource=Module["_ta_command_request_set_negative_resource"]=function(){return Module["asm"]["Hc"].apply(null,arguments)};var _ta_command_request_set_order=Module["_ta_command_request_set_order"]=function(){return Module["asm"]["Ic"].apply(null,arguments)};var _ta_command_request_set_positive_answers=Module["_ta_command_request_set_positive_answers"]=function(){return Module["asm"]["Jc"].apply(null,arguments)};var _ta_command_request_set_positive_resource=Module["_ta_command_request_set_positive_resource"]=function(){return Module["asm"]["Kc"].apply(null,arguments)};var _ta_command_request_set_pre_resource=Module["_ta_command_request_set_pre_resource"]=function(){return Module["asm"]["Lc"].apply(null,arguments)};var _ta_command_response_get_atr=Module["_ta_command_response_get_atr"]=function(){return Module["asm"]["Mc"].apply(null,arguments)};var _ta_command_response_get_card_response=Module["_ta_command_response_get_card_response"]=function(){return Module["asm"]["Nc"].apply(null,arguments)};var _ta_command_response_get_order=Module["_ta_command_response_get_order"]=function(){return Module["asm"]["Oc"].apply(null,arguments)};var _ta_command_response_get_response_type=Module["_ta_command_response_get_response_type"]=function(){return Module["asm"]["Pc"].apply(null,arguments)};var _ta_command_response_get_uid=Module["_ta_command_response_get_uid"]=function(){return Module["asm"]["Qc"].apply(null,arguments)};var _ta_compact_receipt_formatter_create=Module["_ta_compact_receipt_formatter_create"]=function(){return Module["asm"]["Rc"].apply(null,arguments)};var _ta_config_data_get_language=Module["_ta_config_data_get_language"]=function(){return Module["asm"]["Sc"].apply(null,arguments)};var _ta_config_data_get_receipt_header=Module["_ta_config_data_get_receipt_header"]=function(){return Module["asm"]["Tc"].apply(null,arguments)};var _ta_connection_status_e2s=Module["_ta_connection_status_e2s"]=function(){return Module["asm"]["Uc"].apply(null,arguments)};var _ta_counter_get_acq_id=Module["_ta_counter_get_acq_id"]=function(){return Module["asm"]["Vc"].apply(null,arguments)};var _ta_counter_get_brand_name=Module["_ta_counter_get_brand_name"]=function(){return Module["asm"]["Wc"].apply(null,arguments)};var _ta_counter_get_count=Module["_ta_counter_get_count"]=function(){return Module["asm"]["Xc"].apply(null,arguments)};var _ta_counter_get_count_dcc=Module["_ta_counter_get_count_dcc"]=function(){return Module["asm"]["Yc"].apply(null,arguments)};var _ta_counter_get_count_foreign=Module["_ta_counter_get_count_foreign"]=function(){return Module["asm"]["Zc"].apply(null,arguments)};var _ta_counter_get_payment_protocol=Module["_ta_counter_get_payment_protocol"]=function(){return Module["asm"]["_c"].apply(null,arguments)};var _ta_counter_get_totals=Module["_ta_counter_get_totals"]=function(){return Module["asm"]["$c"].apply(null,arguments)};var _ta_counter_type_e2s=Module["_ta_counter_type_e2s"]=function(){return Module["asm"]["ad"].apply(null,arguments)};var _ta_counters_get_counter_type=Module["_ta_counters_get_counter_type"]=function(){return Module["asm"]["bd"].apply(null,arguments)};var _ta_counters_get_counters=Module["_ta_counters_get_counters"]=function(){return Module["asm"]["cd"].apply(null,arguments)};var _ta_counters_get_seq_counter=Module["_ta_counters_get_seq_counter"]=function(){return Module["asm"]["dd"].apply(null,arguments)};var _ta_coupon_rejection_reason_e2s=Module["_ta_coupon_rejection_reason_e2s"]=function(){return Module["asm"]["ed"].apply(null,arguments)};var _ta_currency_get_parameters=Module["_ta_currency_get_parameters"]=function(){return Module["asm"]["fd"].apply(null,arguments)};var _ta_currency_item_get_currency=Module["_ta_currency_item_get_currency"]=function(){return Module["asm"]["gd"].apply(null,arguments)};var _ta_currency_item_get_currency_type=Module["_ta_currency_item_get_currency_type"]=function(){return Module["asm"]["hd"].apply(null,arguments)};var _ta_currency_type_e2s=Module["_ta_currency_type_e2s"]=function(){return Module["asm"]["id"].apply(null,arguments)};var _ta_currency_with_code=Module["_ta_currency_with_code"]=function(){return Module["asm"]["jd"].apply(null,arguments)};var _ta_custom_receipt_formatter_create=Module["_ta_custom_receipt_formatter_create"]=function(){return Module["asm"]["kd"].apply(null,arguments)};var _ta_customer_data_type_e2s=Module["_ta_customer_data_type_e2s"]=function(){return Module["asm"]["ld"].apply(null,arguments)};var _ta_cvm_e2s=Module["_ta_cvm_e2s"]=function(){return Module["asm"]["md"].apply(null,arguments)};var _ta_deactivate_response_get_counters=Module["_ta_deactivate_response_get_counters"]=function(){return Module["asm"]["nd"].apply(null,arguments)};var _ta_deactivate_response_get_print_data=Module["_ta_deactivate_response_get_print_data"]=function(){return Module["asm"]["od"].apply(null,arguments)};var _ta_dialog_functions_e2s=Module["_ta_dialog_functions_e2s"]=function(){return Module["asm"]["pd"].apply(null,arguments)};var _ta_display_product_info_copy=Module["_ta_display_product_info_copy"]=function(){return Module["asm"]["qd"].apply(null,arguments)};var _ta_display_product_info_create=Module["_ta_display_product_info_create"]=function(){return Module["asm"]["rd"].apply(null,arguments)};var _ta_display_product_info_get_background_color=Module["_ta_display_product_info_get_background_color"]=function(){return Module["asm"]["sd"].apply(null,arguments)};var _ta_display_product_info_get_image_data=Module["_ta_display_product_info_get_image_data"]=function(){return Module["asm"]["td"].apply(null,arguments)};var _ta_display_product_info_get_image_file_format=Module["_ta_display_product_info_get_image_file_format"]=function(){return Module["asm"]["ud"].apply(null,arguments)};var _ta_display_product_info_get_image_file_height=Module["_ta_display_product_info_get_image_file_height"]=function(){return Module["asm"]["vd"].apply(null,arguments)};var _ta_display_product_info_get_image_file_width=Module["_ta_display_product_info_get_image_file_width"]=function(){return Module["asm"]["wd"].apply(null,arguments)};var _ta_display_product_info_get_product_display_name=Module["_ta_display_product_info_get_product_display_name"]=function(){return Module["asm"]["xd"].apply(null,arguments)};var _ta_display_product_info_set_background_color=Module["_ta_display_product_info_set_background_color"]=function(){return Module["asm"]["yd"].apply(null,arguments)};var _ta_display_product_info_set_image_data=Module["_ta_display_product_info_set_image_data"]=function(){return Module["asm"]["zd"].apply(null,arguments)};var _ta_display_product_info_set_image_file_format=Module["_ta_display_product_info_set_image_file_format"]=function(){return Module["asm"]["Ad"].apply(null,arguments)};var _ta_display_product_info_set_image_file_height=Module["_ta_display_product_info_set_image_file_height"]=function(){return Module["asm"]["Bd"].apply(null,arguments)};var _ta_display_product_info_set_image_file_width=Module["_ta_display_product_info_set_image_file_width"]=function(){return Module["asm"]["Cd"].apply(null,arguments)};var _ta_display_product_info_set_product_display_name=Module["_ta_display_product_info_set_product_display_name"]=function(){return Module["asm"]["Dd"].apply(null,arguments)};var _ta_ecr_info_create=Module["_ta_ecr_info_create"]=function(){return Module["asm"]["Ed"].apply(null,arguments)};var _ta_ecr_info_get_architecture=Module["_ta_ecr_info_get_architecture"]=function(){return Module["asm"]["Fd"].apply(null,arguments)};var _ta_ecr_info_get_integrator_solution=Module["_ta_ecr_info_get_integrator_solution"]=function(){return Module["asm"]["Gd"].apply(null,arguments)};var _ta_ecr_info_get_manufacturer_name=Module["_ta_ecr_info_get_manufacturer_name"]=function(){return Module["asm"]["Hd"].apply(null,arguments)};var _ta_ecr_info_get_name=Module["_ta_ecr_info_get_name"]=function(){return Module["asm"]["Id"].apply(null,arguments)};var _ta_ecr_info_get_remote_ip=Module["_ta_ecr_info_get_remote_ip"]=function(){return Module["asm"]["Jd"].apply(null,arguments)};var _ta_ecr_info_get_serial_number=Module["_ta_ecr_info_get_serial_number"]=function(){return Module["asm"]["Kd"].apply(null,arguments)};var _ta_ecr_info_get_type=Module["_ta_ecr_info_get_type"]=function(){return Module["asm"]["Ld"].apply(null,arguments)};var _ta_ecr_info_get_version=Module["_ta_ecr_info_get_version"]=function(){return Module["asm"]["Md"].apply(null,arguments)};var _ta_ecr_info_set_architecture=Module["_ta_ecr_info_set_architecture"]=function(){return Module["asm"]["Nd"].apply(null,arguments)};var _ta_ecr_info_set_integrator_solution=Module["_ta_ecr_info_set_integrator_solution"]=function(){return Module["asm"]["Od"].apply(null,arguments)};var _ta_ecr_info_set_manufacturer_name=Module["_ta_ecr_info_set_manufacturer_name"]=function(){return Module["asm"]["Pd"].apply(null,arguments)};var _ta_ecr_info_set_name=Module["_ta_ecr_info_set_name"]=function(){return Module["asm"]["Qd"].apply(null,arguments)};var _ta_ecr_info_set_remote_ip=Module["_ta_ecr_info_set_remote_ip"]=function(){return Module["asm"]["Rd"].apply(null,arguments)};var _ta_ecr_info_set_serial_number=Module["_ta_ecr_info_set_serial_number"]=function(){return Module["asm"]["Sd"].apply(null,arguments)};var _ta_ecr_info_set_type=Module["_ta_ecr_info_set_type"]=function(){return Module["asm"]["Td"].apply(null,arguments)};var _ta_ecr_info_set_version=Module["_ta_ecr_info_set_version"]=function(){return Module["asm"]["Ud"].apply(null,arguments)};var _ta_ecr_info_type_e2s=Module["_ta_ecr_info_type_e2s"]=function(){return Module["asm"]["Vd"].apply(null,arguments)};var _ta_error_message_get_language=Module["_ta_error_message_get_language"]=function(){return Module["asm"]["Wd"].apply(null,arguments)};var _ta_error_message_get_terminal=Module["_ta_error_message_get_terminal"]=function(){return Module["asm"]["Xd"].apply(null,arguments)};var _ta_financial_transactions_e2s=Module["_ta_financial_transactions_e2s"]=function(){return Module["asm"]["Yd"].apply(null,arguments)};var _ta_function_hint_e2s=Module["_ta_function_hint_e2s"]=function(){return Module["asm"]["Zd"].apply(null,arguments)};var _ta_guides_e2s=Module["_ta_guides_e2s"]=function(){return Module["asm"]["_d"].apply(null,arguments)};var _ta_hardware_get_firmware_version=Module["_ta_hardware_get_firmware_version"]=function(){return Module["asm"]["$d"].apply(null,arguments)};var _ta_hardware_get_hardware_address=Module["_ta_hardware_get_hardware_address"]=function(){return Module["asm"]["ae"].apply(null,arguments)};var _ta_hardware_get_hardware_description=Module["_ta_hardware_get_hardware_description"]=function(){return Module["asm"]["be"].apply(null,arguments)};var _ta_hardware_get_hardware_type=Module["_ta_hardware_get_hardware_type"]=function(){return Module["asm"]["ce"].apply(null,arguments)};var _ta_hardware_get_iccid=Module["_ta_hardware_get_iccid"]=function(){return Module["asm"]["de"].apply(null,arguments)};var _ta_hardware_get_imei=Module["_ta_hardware_get_imei"]=function(){return Module["asm"]["ee"].apply(null,arguments)};var _ta_hardware_get_imsi=Module["_ta_hardware_get_imsi"]=function(){return Module["asm"]["fe"].apply(null,arguments)};var _ta_hardware_get_last_cleaning_date=Module["_ta_hardware_get_last_cleaning_date"]=function(){return Module["asm"]["ge"].apply(null,arguments)};var _ta_hardware_get_product_version=Module["_ta_hardware_get_product_version"]=function(){return Module["asm"]["he"].apply(null,arguments)};var _ta_hardware_get_production_date=Module["_ta_hardware_get_production_date"]=function(){return Module["asm"]["ie"].apply(null,arguments)};var _ta_hardware_get_security_status=Module["_ta_hardware_get_security_status"]=function(){return Module["asm"]["je"].apply(null,arguments)};var _ta_hardware_get_serial_number=Module["_ta_hardware_get_serial_number"]=function(){return Module["asm"]["ke"].apply(null,arguments)};var _ta_hardware_information_response_get_battery_charging=Module["_ta_hardware_information_response_get_battery_charging"]=function(){return Module["asm"]["le"].apply(null,arguments)};var _ta_hardware_information_response_get_battery_level=Module["_ta_hardware_information_response_get_battery_level"]=function(){return Module["asm"]["me"].apply(null,arguments)};var _ta_hardware_information_response_get_hardwares=Module["_ta_hardware_information_response_get_hardwares"]=function(){return Module["asm"]["ne"].apply(null,arguments)};var _ta_hardware_information_response_get_kernel_versions=Module["_ta_hardware_information_response_get_kernel_versions"]=function(){return Module["asm"]["oe"].apply(null,arguments)};var _ta_hardware_information_response_get_settings=Module["_ta_hardware_information_response_get_settings"]=function(){return Module["asm"]["pe"].apply(null,arguments)};var _ta_hardware_information_response_get_statistics=Module["_ta_hardware_information_response_get_statistics"]=function(){return Module["asm"]["qe"].apply(null,arguments)};var _ta_hardware_type_e2s=Module["_ta_hardware_type_e2s"]=function(){return Module["asm"]["re"].apply(null,arguments)};var _ta_image_file_format_e2s=Module["_ta_image_file_format_e2s"]=function(){return Module["asm"]["se"].apply(null,arguments)};var _ta_integer_create=Module["_ta_integer_create"]=function(){return Module["asm"]["te"].apply(null,arguments)};var _ta_integer_get_value=Module["_ta_integer_get_value"]=function(){return Module["asm"]["ue"].apply(null,arguments)};var _ta_item_quantity_create=Module["_ta_item_quantity_create"]=function(){return Module["asm"]["ve"].apply(null,arguments)};var _ta_item_quantity_get_exponent=Module["_ta_item_quantity_get_exponent"]=function(){return Module["asm"]["we"].apply(null,arguments)};var _ta_item_quantity_get_quantity=Module["_ta_item_quantity_get_quantity"]=function(){return Module["asm"]["xe"].apply(null,arguments)};var _ta_item_quantity_get_quantity_type=Module["_ta_item_quantity_get_quantity_type"]=function(){return Module["asm"]["ye"].apply(null,arguments)};var _ta_item_quantity_set_exponent=Module["_ta_item_quantity_set_exponent"]=function(){return Module["asm"]["ze"].apply(null,arguments)};var _ta_item_quantity_set_quantity=Module["_ta_item_quantity_set_quantity"]=function(){return Module["asm"]["Ae"].apply(null,arguments)};var _ta_item_quantity_set_type=Module["_ta_item_quantity_set_type"]=function(){return Module["asm"]["Be"].apply(null,arguments)};var _ta_kernel_type_e2s=Module["_ta_kernel_type_e2s"]=function(){return Module["asm"]["Ce"].apply(null,arguments)};var _ta_list_add=Module["_ta_list_add"]=function(){return Module["asm"]["De"].apply(null,arguments)};var _ta_list_create=Module["_ta_list_create"]=function(){return Module["asm"]["Ee"].apply(null,arguments)};var _ta_list_get_at=Module["_ta_list_get_at"]=function(){return Module["asm"]["Fe"].apply(null,arguments)};var _ta_list_get_count=Module["_ta_list_get_count"]=function(){return Module["asm"]["Ge"].apply(null,arguments)};var _ta_list_has=Module["_ta_list_has"]=function(){return Module["asm"]["He"].apply(null,arguments)};var _ta_list_index_of=Module["_ta_list_index_of"]=function(){return Module["asm"]["Ie"].apply(null,arguments)};var _ta_list_remove=Module["_ta_list_remove"]=function(){return Module["asm"]["Je"].apply(null,arguments)};var _ta_list_remove_all=Module["_ta_list_remove_all"]=function(){return Module["asm"]["Ke"].apply(null,arguments)};var _ta_logger_set_global_logger=Module["_ta_logger_set_global_logger"]=function(){return Module["asm"]["Le"].apply(null,arguments)};var _ta_loyalty_coupon_create=Module["_ta_loyalty_coupon_create"]=function(){return Module["asm"]["Me"].apply(null,arguments)};var _ta_loyalty_coupon_get_amount=Module["_ta_loyalty_coupon_get_amount"]=function(){return Module["asm"]["Ne"].apply(null,arguments)};var _ta_loyalty_coupon_get_currency=Module["_ta_loyalty_coupon_get_currency"]=function(){return Module["asm"]["Oe"].apply(null,arguments)};var _ta_loyalty_coupon_get_exponent=Module["_ta_loyalty_coupon_get_exponent"]=function(){return Module["asm"]["Pe"].apply(null,arguments)};var _ta_loyalty_coupon_get_id=Module["_ta_loyalty_coupon_get_id"]=function(){return Module["asm"]["Qe"].apply(null,arguments)};var _ta_loyalty_coupon_get_rejection_reason=Module["_ta_loyalty_coupon_get_rejection_reason"]=function(){return Module["asm"]["Re"].apply(null,arguments)};var _ta_loyalty_discount_get_currency=Module["_ta_loyalty_discount_get_currency"]=function(){return Module["asm"]["Se"].apply(null,arguments)};var _ta_loyalty_discount_get_decimal_value=Module["_ta_loyalty_discount_get_decimal_value"]=function(){return Module["asm"]["Te"].apply(null,arguments)};var _ta_loyalty_discount_get_discount_description=Module["_ta_loyalty_discount_get_discount_description"]=function(){return Module["asm"]["Ue"].apply(null,arguments)};var _ta_loyalty_discount_get_exponent=Module["_ta_loyalty_discount_get_exponent"]=function(){return Module["asm"]["Ve"].apply(null,arguments)};var _ta_loyalty_discount_get_value=Module["_ta_loyalty_discount_get_value"]=function(){return Module["asm"]["We"].apply(null,arguments)};var _ta_loyalty_function_type_e2s=Module["_ta_loyalty_function_type_e2s"]=function(){return Module["asm"]["Xe"].apply(null,arguments)};var _ta_loyalty_information_create=Module["_ta_loyalty_information_create"]=function(){return Module["asm"]["Ye"].apply(null,arguments)};var _ta_loyalty_information_get_loyalty_function_type=Module["_ta_loyalty_information_get_loyalty_function_type"]=function(){return Module["asm"]["Ze"].apply(null,arguments)};var _ta_loyalty_information_get_loyalty_info_type=Module["_ta_loyalty_information_get_loyalty_info_type"]=function(){return Module["asm"]["_e"].apply(null,arguments)};var _ta_loyalty_information_get_loyalty_number=Module["_ta_loyalty_information_get_loyalty_number"]=function(){return Module["asm"]["$e"].apply(null,arguments)};var _ta_loyalty_information_get_value=Module["_ta_loyalty_information_get_value"]=function(){return Module["asm"]["af"].apply(null,arguments)};var _ta_loyalty_item_copy=Module["_ta_loyalty_item_copy"]=function(){return Module["asm"]["bf"].apply(null,arguments)};var _ta_loyalty_item_create=Module["_ta_loyalty_item_create"]=function(){return Module["asm"]["cf"].apply(null,arguments)};var _ta_loyalty_item_get_amount=Module["_ta_loyalty_item_get_amount"]=function(){return Module["asm"]["df"].apply(null,arguments)};var _ta_loyalty_item_get_amount_total=Module["_ta_loyalty_item_get_amount_total"]=function(){return Module["asm"]["ef"].apply(null,arguments)};var _ta_loyalty_item_get_display_product_info=Module["_ta_loyalty_item_get_display_product_info"]=function(){return Module["asm"]["ff"].apply(null,arguments)};var _ta_loyalty_item_get_item_id=Module["_ta_loyalty_item_get_item_id"]=function(){return Module["asm"]["gf"].apply(null,arguments)};var _ta_loyalty_item_get_item_quantity=Module["_ta_loyalty_item_get_item_quantity"]=function(){return Module["asm"]["hf"].apply(null,arguments)};var _ta_loyalty_item_get_loyalty_coupons=Module["_ta_loyalty_item_get_loyalty_coupons"]=function(){return Module["asm"]["jf"].apply(null,arguments)};var _ta_loyalty_item_get_loyalty_discounts=Module["_ta_loyalty_item_get_loyalty_discounts"]=function(){return Module["asm"]["kf"].apply(null,arguments)};var _ta_loyalty_item_get_prod_description=Module["_ta_loyalty_item_get_prod_description"]=function(){return Module["asm"]["lf"].apply(null,arguments)};var _ta_loyalty_item_set_amount=Module["_ta_loyalty_item_set_amount"]=function(){return Module["asm"]["mf"].apply(null,arguments)};var _ta_loyalty_item_set_amount_total=Module["_ta_loyalty_item_set_amount_total"]=function(){return Module["asm"]["nf"].apply(null,arguments)};var _ta_loyalty_item_set_display_product_info=Module["_ta_loyalty_item_set_display_product_info"]=function(){return Module["asm"]["of"].apply(null,arguments)};var _ta_loyalty_item_set_item_id=Module["_ta_loyalty_item_set_item_id"]=function(){return Module["asm"]["pf"].apply(null,arguments)};var _ta_loyalty_item_set_item_quantity=Module["_ta_loyalty_item_set_item_quantity"]=function(){return Module["asm"]["qf"].apply(null,arguments)};var _ta_loyalty_item_set_prod_description=Module["_ta_loyalty_item_set_prod_description"]=function(){return Module["asm"]["rf"].apply(null,arguments)};var _ta_maintenance_type_e2s=Module["_ta_maintenance_type_e2s"]=function(){return Module["asm"]["sf"].apply(null,arguments)};var _ta_management_status_e2s=Module["_ta_management_status_e2s"]=function(){return Module["asm"]["tf"].apply(null,arguments)};var _ta_map_create=Module["_ta_map_create"]=function(){return Module["asm"]["uf"].apply(null,arguments)};var _ta_map_get=Module["_ta_map_get"]=function(){return Module["asm"]["vf"].apply(null,arguments)};var _ta_map_get_at=Module["_ta_map_get_at"]=function(){return Module["asm"]["wf"].apply(null,arguments)};var _ta_map_get_count=Module["_ta_map_get_count"]=function(){return Module["asm"]["xf"].apply(null,arguments)};var _ta_map_get_default=Module["_ta_map_get_default"]=function(){return Module["asm"]["yf"].apply(null,arguments)};var _ta_map_has=Module["_ta_map_has"]=function(){return Module["asm"]["zf"].apply(null,arguments)};var _ta_map_remove=Module["_ta_map_remove"]=function(){return Module["asm"]["Af"].apply(null,arguments)};var _ta_map_remove_all=Module["_ta_map_remove_all"]=function(){return Module["asm"]["Bf"].apply(null,arguments)};var _ta_map_set=Module["_ta_map_set"]=function(){return Module["asm"]["Cf"].apply(null,arguments)};var _ta_merchant_action_e2s=Module["_ta_merchant_action_e2s"]=function(){return Module["asm"]["Df"].apply(null,arguments)};var _ta_merchant_option_create=Module["_ta_merchant_option_create"]=function(){return Module["asm"]["Ef"].apply(null,arguments)};var _ta_merchant_option_get_type=Module["_ta_merchant_option_get_type"]=function(){return Module["asm"]["Ff"].apply(null,arguments)};var _ta_merchant_option_get_value=Module["_ta_merchant_option_get_value"]=function(){return Module["asm"]["Gf"].apply(null,arguments)};var _ta_merchant_option_type_e2s=Module["_ta_merchant_option_type_e2s"]=function(){return Module["asm"]["Hf"].apply(null,arguments)};var _ta_native_error_get_code=Module["_ta_native_error_get_code"]=function(){return Module["asm"]["If"].apply(null,arguments)};var _ta_native_error_get_message=Module["_ta_native_error_get_message"]=function(){return Module["asm"]["Jf"].apply(null,arguments)};var _ta_native_error_get_source=Module["_ta_native_error_get_source"]=function(){return Module["asm"]["Kf"].apply(null,arguments)};var _ta_network_information_get_terminal_ip=Module["_ta_network_information_get_terminal_ip"]=function(){return Module["asm"]["Lf"].apply(null,arguments)};var _ta_network_information_get_terminal_ip_dns=Module["_ta_network_information_get_terminal_ip_dns"]=function(){return Module["asm"]["Mf"].apply(null,arguments)};var _ta_network_information_get_terminal_ip_gw=Module["_ta_network_information_get_terminal_ip_gw"]=function(){return Module["asm"]["Nf"].apply(null,arguments)};var _ta_network_information_get_terminal_ip_mask=Module["_ta_network_information_get_terminal_ip_mask"]=function(){return Module["asm"]["Of"].apply(null,arguments)};var _ta_ngv_mode_e2s=Module["_ta_ngv_mode_e2s"]=function(){return Module["asm"]["Pf"].apply(null,arguments)};var _ta_non_financial_transactions_e2s=Module["_ta_non_financial_transactions_e2s"]=function(){return Module["asm"]["Qf"].apply(null,arguments)};var _ta_normal_receipt_formatter_create=Module["_ta_normal_receipt_formatter_create"]=function(){return Module["asm"]["Rf"].apply(null,arguments)};var _ta_object_release=Module["_ta_object_release"]=function(){return Module["asm"]["Sf"].apply(null,arguments)};var _ta_object_release_if_valid=Module["_ta_object_release_if_valid"]=function(){return Module["asm"]["Tf"].apply(null,arguments)};var _ta_object_retain=Module["_ta_object_retain"]=function(){return Module["asm"]["Uf"].apply(null,arguments)};var _ta_object_to_string=Module["_ta_object_to_string"]=function(){return Module["asm"]["Vf"].apply(null,arguments)};var _ta_payment_protocol_e2s=Module["_ta_payment_protocol_e2s"]=function(){return Module["asm"]["Wf"].apply(null,arguments)};var _ta_pos_entry_mode_e2s=Module["_ta_pos_entry_mode_e2s"]=function(){return Module["asm"]["Xf"].apply(null,arguments)};var _ta_ppinfo_get_payment_protocol=Module["_ta_ppinfo_get_payment_protocol"]=function(){return Module["asm"]["Yf"].apply(null,arguments)};var _ta_ppinfo_get_pp_ep2_auth_reslt=Module["_ta_ppinfo_get_pp_ep2_auth_reslt"]=function(){return Module["asm"]["Zf"].apply(null,arguments)};var _ta_ppinfo_get_pp_ep2_auth_resp_c=Module["_ta_ppinfo_get_pp_ep2_auth_resp_c"]=function(){return Module["asm"]["_f"].apply(null,arguments)};var _ta_ppinfo_get_pp_ep2_trans_seq_cnt=Module["_ta_ppinfo_get_pp_ep2_trans_seq_cnt"]=function(){return Module["asm"]["$f"].apply(null,arguments)};var _ta_ppinfo_get_pp_ep2_trans_seq_cnt_orig=Module["_ta_ppinfo_get_pp_ep2_trans_seq_cnt_orig"]=function(){return Module["asm"]["ag"].apply(null,arguments)};var _ta_print_data_get_receipt_items=Module["_ta_print_data_get_receipt_items"]=function(){return Module["asm"]["bg"].apply(null,arguments)};var _ta_print_data_get_receipts=Module["_ta_print_data_get_receipts"]=function(){return Module["asm"]["cg"].apply(null,arguments)};var _ta_print_flag_e2s=Module["_ta_print_flag_e2s"]=function(){return Module["asm"]["dg"].apply(null,arguments)};var _ta_print_format_e2s=Module["_ta_print_format_e2s"]=function(){return Module["asm"]["eg"].apply(null,arguments)};var _ta_print_option_create=Module["_ta_print_option_create"]=function(){return Module["asm"]["fg"].apply(null,arguments)};var _ta_print_option_get_print_flags=Module["_ta_print_option_get_print_flags"]=function(){return Module["asm"]["gg"].apply(null,arguments)};var _ta_print_option_get_print_format=Module["_ta_print_option_get_print_format"]=function(){return Module["asm"]["hg"].apply(null,arguments)};var _ta_print_option_get_print_width=Module["_ta_print_option_get_print_width"]=function(){return Module["asm"]["ig"].apply(null,arguments)};var _ta_print_option_get_recipient=Module["_ta_print_option_get_recipient"]=function(){return Module["asm"]["jg"].apply(null,arguments)};var _ta_process_print_receipts=Module["_ta_process_print_receipts"]=function(){return Module["asm"]["kg"].apply(null,arguments)};var _ta_processing_disposition_e2s=Module["_ta_processing_disposition_e2s"]=function(){return Module["asm"]["lg"].apply(null,arguments)};var _ta_protocol_level_e2s=Module["_ta_protocol_level_e2s"]=function(){return Module["asm"]["mg"].apply(null,arguments)};var _ta_protocol_type_e2s=Module["_ta_protocol_type_e2s"]=function(){return Module["asm"]["ng"].apply(null,arguments)};var _ta_reason_e2s=Module["_ta_reason_e2s"]=function(){return Module["asm"]["og"].apply(null,arguments)};var _ta_receipt_create=Module["_ta_receipt_create"]=function(){return Module["asm"]["pg"].apply(null,arguments)};var _ta_receipt_formatter_create=Module["_ta_receipt_formatter_create"]=function(){return Module["asm"]["qg"].apply(null,arguments)};var _ta_receipt_get_recipient=Module["_ta_receipt_get_recipient"]=function(){return Module["asm"]["rg"].apply(null,arguments)};var _ta_receipt_get_value=Module["_ta_receipt_get_value"]=function(){return Module["asm"]["sg"].apply(null,arguments)};var _ta_receipt_item_get_receipt_item_type=Module["_ta_receipt_item_get_receipt_item_type"]=function(){return Module["asm"]["tg"].apply(null,arguments)};var _ta_receipt_item_get_recipient=Module["_ta_receipt_item_get_recipient"]=function(){return Module["asm"]["ug"].apply(null,arguments)};var _ta_receipt_item_get_value=Module["_ta_receipt_item_get_value"]=function(){return Module["asm"]["vg"].apply(null,arguments)};var _ta_receipt_item_type_e2s=Module["_ta_receipt_item_type_e2s"]=function(){return Module["asm"]["wg"].apply(null,arguments)};var _ta_receipt_items_get_receipt_item=Module["_ta_receipt_items_get_receipt_item"]=function(){return Module["asm"]["xg"].apply(null,arguments)};var _ta_receipt_items_get_receipt_type=Module["_ta_receipt_items_get_receipt_type"]=function(){return Module["asm"]["yg"].apply(null,arguments)};var _ta_receipt_request_response_get_has_more_receipts=Module["_ta_receipt_request_response_get_has_more_receipts"]=function(){return Module["asm"]["zg"].apply(null,arguments)};var _ta_receipt_request_response_get_print_data=Module["_ta_receipt_request_response_get_print_data"]=function(){return Module["asm"]["Ag"].apply(null,arguments)};var _ta_receipt_request_type_e2s=Module["_ta_receipt_request_type_e2s"]=function(){return Module["asm"]["Bg"].apply(null,arguments)};var _ta_receipt_type_e2s=Module["_ta_receipt_type_e2s"]=function(){return Module["asm"]["Cg"].apply(null,arguments)};var _ta_recipient_e2s=Module["_ta_recipient_e2s"]=function(){return Module["asm"]["Dg"].apply(null,arguments)};var _ta_reconciliation_response_get_counters=Module["_ta_reconciliation_response_get_counters"]=function(){return Module["asm"]["Eg"].apply(null,arguments)};var _ta_reconciliation_response_get_print_data=Module["_ta_reconciliation_response_get_print_data"]=function(){return Module["asm"]["Fg"].apply(null,arguments)};var _ta_remote_functions_e2s=Module["_ta_remote_functions_e2s"]=function(){return Module["asm"]["Gg"].apply(null,arguments)};var _ta_resource_id_e2s=Module["_ta_resource_id_e2s"]=function(){return Module["asm"]["Hg"].apply(null,arguments)};var _ta_resource_parameter_type_e2s=Module["_ta_resource_parameter_type_e2s"]=function(){return Module["asm"]["Ig"].apply(null,arguments)};var _ta_response_type_e2s=Module["_ta_response_type_e2s"]=function(){return Module["asm"]["Jg"].apply(null,arguments)};var _ta_result_code_e2s=Module["_ta_result_code_e2s"]=function(){return Module["asm"]["Kg"].apply(null,arguments)};var _ta_screenshot_information_get_image_data=Module["_ta_screenshot_information_get_image_data"]=function(){return Module["asm"]["Lg"].apply(null,arguments)};var _ta_screenshot_information_get_image_file_format=Module["_ta_screenshot_information_get_image_file_format"]=function(){return Module["asm"]["Mg"].apply(null,arguments)};var _ta_screenshot_information_get_image_height=Module["_ta_screenshot_information_get_image_height"]=function(){return Module["asm"]["Ng"].apply(null,arguments)};var _ta_screenshot_information_get_image_width=Module["_ta_screenshot_information_get_image_width"]=function(){return Module["asm"]["Og"].apply(null,arguments)};var _ta_security_status_e2s=Module["_ta_security_status_e2s"]=function(){return Module["asm"]["Pg"].apply(null,arguments)};var _ta_setting_type_e2s=Module["_ta_setting_type_e2s"]=function(){return Module["asm"]["Qg"].apply(null,arguments)};var _ta_show_dialog_request_copy=Module["_ta_show_dialog_request_copy"]=function(){return Module["asm"]["Rg"].apply(null,arguments)};var _ta_show_dialog_request_create=Module["_ta_show_dialog_request_create"]=function(){return Module["asm"]["Sg"].apply(null,arguments)};var _ta_show_dialog_request_get_brand_bar=Module["_ta_show_dialog_request_get_brand_bar"]=function(){return Module["asm"]["Tg"].apply(null,arguments)};var _ta_show_dialog_request_get_brand_mode=Module["_ta_show_dialog_request_get_brand_mode"]=function(){return Module["asm"]["Ug"].apply(null,arguments)};var _ta_show_dialog_request_get_language=Module["_ta_show_dialog_request_get_language"]=function(){return Module["asm"]["Vg"].apply(null,arguments)};var _ta_show_dialog_request_get_placeholder_items=Module["_ta_show_dialog_request_get_placeholder_items"]=function(){return Module["asm"]["Wg"].apply(null,arguments)};var _ta_show_dialog_request_get_resource_id=Module["_ta_show_dialog_request_get_resource_id"]=function(){return Module["asm"]["Xg"].apply(null,arguments)};var _ta_show_dialog_request_get_theme=Module["_ta_show_dialog_request_get_theme"]=function(){return Module["asm"]["Yg"].apply(null,arguments)};var _ta_show_dialog_request_get_timeout=Module["_ta_show_dialog_request_get_timeout"]=function(){return Module["asm"]["Zg"].apply(null,arguments)};var _ta_show_dialog_request_resource_get_parameters=Module["_ta_show_dialog_request_resource_get_parameters"]=function(){return Module["asm"]["_g"].apply(null,arguments)};var _ta_show_dialog_request_set_brand_bar=Module["_ta_show_dialog_request_set_brand_bar"]=function(){return Module["asm"]["$g"].apply(null,arguments)};var _ta_show_dialog_request_set_brand_mode=Module["_ta_show_dialog_request_set_brand_mode"]=function(){return Module["asm"]["ah"].apply(null,arguments)};var _ta_show_dialog_request_set_language=Module["_ta_show_dialog_request_set_language"]=function(){return Module["asm"]["bh"].apply(null,arguments)};var _ta_show_dialog_request_set_placeholder_items=Module["_ta_show_dialog_request_set_placeholder_items"]=function(){return Module["asm"]["ch"].apply(null,arguments)};var _ta_show_dialog_request_set_resource_id=Module["_ta_show_dialog_request_set_resource_id"]=function(){return Module["asm"]["dh"].apply(null,arguments)};var _ta_show_dialog_request_set_resource_parameters=Module["_ta_show_dialog_request_set_resource_parameters"]=function(){return Module["asm"]["eh"].apply(null,arguments)};var _ta_show_dialog_request_set_theme=Module["_ta_show_dialog_request_set_theme"]=function(){return Module["asm"]["fh"].apply(null,arguments)};var _ta_show_dialog_request_set_timeout=Module["_ta_show_dialog_request_set_timeout"]=function(){return Module["asm"]["gh"].apply(null,arguments)};var _ta_show_dialog_response_get_card_data=Module["_ta_show_dialog_response_get_card_data"]=function(){return Module["asm"]["hh"].apply(null,arguments)};var _ta_show_dialog_response_get_reason=Module["_ta_show_dialog_response_get_reason"]=function(){return Module["asm"]["ih"].apply(null,arguments)};var _ta_show_dialog_response_get_user_input=Module["_ta_show_dialog_response_get_user_input"]=function(){return Module["asm"]["jh"].apply(null,arguments)};var _ta_show_signature_capture_request_copy=Module["_ta_show_signature_capture_request_copy"]=function(){return Module["asm"]["kh"].apply(null,arguments)};var _ta_show_signature_capture_request_create=Module["_ta_show_signature_capture_request_create"]=function(){return Module["asm"]["lh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_background_color=Module["_ta_show_signature_capture_request_get_background_color"]=function(){return Module["asm"]["mh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_brand_bar=Module["_ta_show_signature_capture_request_get_brand_bar"]=function(){return Module["asm"]["nh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_brand_mode=Module["_ta_show_signature_capture_request_get_brand_mode"]=function(){return Module["asm"]["oh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_image_file_format=Module["_ta_show_signature_capture_request_get_image_file_format"]=function(){return Module["asm"]["ph"].apply(null,arguments)};var _ta_show_signature_capture_request_get_image_file_height=Module["_ta_show_signature_capture_request_get_image_file_height"]=function(){return Module["asm"]["qh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_image_file_width=Module["_ta_show_signature_capture_request_get_image_file_width"]=function(){return Module["asm"]["rh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_language=Module["_ta_show_signature_capture_request_get_language"]=function(){return Module["asm"]["sh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_resource_id=Module["_ta_show_signature_capture_request_get_resource_id"]=function(){return Module["asm"]["th"].apply(null,arguments)};var _ta_show_signature_capture_request_get_signature_color=Module["_ta_show_signature_capture_request_get_signature_color"]=function(){return Module["asm"]["uh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_theme=Module["_ta_show_signature_capture_request_get_theme"]=function(){return Module["asm"]["vh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_timeout=Module["_ta_show_signature_capture_request_get_timeout"]=function(){return Module["asm"]["wh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_watermark_color=Module["_ta_show_signature_capture_request_get_watermark_color"]=function(){return Module["asm"]["xh"].apply(null,arguments)};var _ta_show_signature_capture_request_get_watermark_items=Module["_ta_show_signature_capture_request_get_watermark_items"]=function(){return Module["asm"]["yh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_background_color=Module["_ta_show_signature_capture_request_set_background_color"]=function(){return Module["asm"]["zh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_brand_bar=Module["_ta_show_signature_capture_request_set_brand_bar"]=function(){return Module["asm"]["Ah"].apply(null,arguments)};var _ta_show_signature_capture_request_set_brand_mode=Module["_ta_show_signature_capture_request_set_brand_mode"]=function(){return Module["asm"]["Bh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_image_file_format=Module["_ta_show_signature_capture_request_set_image_file_format"]=function(){return Module["asm"]["Ch"].apply(null,arguments)};var _ta_show_signature_capture_request_set_image_file_height=Module["_ta_show_signature_capture_request_set_image_file_height"]=function(){return Module["asm"]["Dh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_image_file_width=Module["_ta_show_signature_capture_request_set_image_file_width"]=function(){return Module["asm"]["Eh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_language=Module["_ta_show_signature_capture_request_set_language"]=function(){return Module["asm"]["Fh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_resource_id=Module["_ta_show_signature_capture_request_set_resource_id"]=function(){return Module["asm"]["Gh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_signature_color=Module["_ta_show_signature_capture_request_set_signature_color"]=function(){return Module["asm"]["Hh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_theme=Module["_ta_show_signature_capture_request_set_theme"]=function(){return Module["asm"]["Ih"].apply(null,arguments)};var _ta_show_signature_capture_request_set_timeout=Module["_ta_show_signature_capture_request_set_timeout"]=function(){return Module["asm"]["Jh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_watermark_color=Module["_ta_show_signature_capture_request_set_watermark_color"]=function(){return Module["asm"]["Kh"].apply(null,arguments)};var _ta_show_signature_capture_request_set_watermark_items=Module["_ta_show_signature_capture_request_set_watermark_items"]=function(){return Module["asm"]["Lh"].apply(null,arguments)};var _ta_show_signature_capture_response_get_image_data=Module["_ta_show_signature_capture_response_get_image_data"]=function(){return Module["asm"]["Mh"].apply(null,arguments)};var _ta_show_signature_capture_response_get_image_file_format=Module["_ta_show_signature_capture_response_get_image_file_format"]=function(){return Module["asm"]["Nh"].apply(null,arguments)};var _ta_show_signature_capture_response_get_image_height=Module["_ta_show_signature_capture_response_get_image_height"]=function(){return Module["asm"]["Oh"].apply(null,arguments)};var _ta_show_signature_capture_response_get_image_width=Module["_ta_show_signature_capture_response_get_image_width"]=function(){return Module["asm"]["Ph"].apply(null,arguments)};var _ta_show_signature_capture_response_get_reason=Module["_ta_show_signature_capture_response_get_reason"]=function(){return Module["asm"]["Qh"].apply(null,arguments)};var _ta_signature_information_get_image_data=Module["_ta_signature_information_get_image_data"]=function(){return Module["asm"]["Rh"].apply(null,arguments)};var _ta_signature_information_get_image_file_format=Module["_ta_signature_information_get_image_file_format"]=function(){return Module["asm"]["Sh"].apply(null,arguments)};var _ta_signature_information_get_image_height=Module["_ta_signature_information_get_image_height"]=function(){return Module["asm"]["Th"].apply(null,arguments)};var _ta_signature_information_get_image_width=Module["_ta_signature_information_get_image_width"]=function(){return Module["asm"]["Uh"].apply(null,arguments)};var _ta_sleep_mode_status_e2s=Module["_ta_sleep_mode_status_e2s"]=function(){return Module["asm"]["Vh"].apply(null,arguments)};var _ta_status_functions_e2s=Module["_ta_status_functions_e2s"]=function(){return Module["asm"]["Wh"].apply(null,arguments)};var _ta_string_create=Module["_ta_string_create"]=function(){return Module["asm"]["Xh"].apply(null,arguments)};var _ta_string_format=Module["_ta_string_format"]=function(){return Module["asm"]["Yh"].apply(null,arguments)};var _ta_string_format_args=Module["_ta_string_format_args"]=function(){return Module["asm"]["Zh"].apply(null,arguments)};var _ta_string_get_length=Module["_ta_string_get_length"]=function(){return Module["asm"]["_h"].apply(null,arguments)};var _ta_string_get_pointer=Module["_ta_string_get_pointer"]=function(){return Module["asm"]["$h"].apply(null,arguments)};var _ta_sub_transaction_copy=Module["_ta_sub_transaction_copy"]=function(){return Module["asm"]["ai"].apply(null,arguments)};var _ta_sub_transaction_create=Module["_ta_sub_transaction_create"]=function(){return Module["asm"]["bi"].apply(null,arguments)};var _ta_sub_transaction_get_amount=Module["_ta_sub_transaction_get_amount"]=function(){return Module["asm"]["ci"].apply(null,arguments)};var _ta_sub_transaction_get_function=Module["_ta_sub_transaction_get_function"]=function(){return Module["asm"]["di"].apply(null,arguments)};var _ta_sub_transaction_get_rate=Module["_ta_sub_transaction_get_rate"]=function(){return Module["asm"]["ei"].apply(null,arguments)};var _ta_sub_transaction_get_rate_exponent=Module["_ta_sub_transaction_get_rate_exponent"]=function(){return Module["asm"]["fi"].apply(null,arguments)};var _ta_sub_transaction_set_amount=Module["_ta_sub_transaction_set_amount"]=function(){return Module["asm"]["gi"].apply(null,arguments)};var _ta_sub_transaction_set_function=Module["_ta_sub_transaction_set_function"]=function(){return Module["asm"]["hi"].apply(null,arguments)};var _ta_sub_transaction_set_rate=Module["_ta_sub_transaction_set_rate"]=function(){return Module["asm"]["ii"].apply(null,arguments)};var _ta_sub_transaction_set_rate_exponent=Module["_ta_sub_transaction_set_rate_exponent"]=function(){return Module["asm"]["ji"].apply(null,arguments)};var _ta_super_compact_receipt_formatter_create=Module["_ta_super_compact_receipt_formatter_create"]=function(){return Module["asm"]["ki"].apply(null,arguments)};var _ta_system_information_response_get_network_information=Module["_ta_system_information_response_get_network_information"]=function(){return Module["asm"]["li"].apply(null,arguments)};var _ta_terminal_activate_async=Module["_ta_terminal_activate_async"]=function(){return Module["asm"]["mi"].apply(null,arguments)};var _ta_terminal_activate_service_menu_async=Module["_ta_terminal_activate_service_menu_async"]=function(){return Module["asm"]["ni"].apply(null,arguments)};var _ta_terminal_add_ecr_data=Module["_ta_terminal_add_ecr_data"]=function(){return Module["asm"]["oi"].apply(null,arguments)};var _ta_terminal_add_listener=Module["_ta_terminal_add_listener"]=function(){return Module["asm"]["pi"].apply(null,arguments)};var _ta_terminal_amt_adjustment=Module["_ta_terminal_amt_adjustment"]=function(){return Module["asm"]["qi"].apply(null,arguments)};var _ta_terminal_amt_adjustment_2=Module["_ta_terminal_amt_adjustment_2"]=function(){return Module["asm"]["ri"].apply(null,arguments)};var _ta_terminal_application_information_async=Module["_ta_terminal_application_information_async"]=function(){return Module["asm"]["si"].apply(null,arguments)};var _ta_terminal_balance_async=Module["_ta_terminal_balance_async"]=function(){return Module["asm"]["ti"].apply(null,arguments)};var _ta_terminal_balance_inquiry_async=Module["_ta_terminal_balance_inquiry_async"]=function(){return Module["asm"]["ui"].apply(null,arguments)};var _ta_terminal_can_dcc=Module["_ta_terminal_can_dcc"]=function(){return Module["asm"]["vi"].apply(null,arguments)};var _ta_terminal_can_declined_receipts=Module["_ta_terminal_can_declined_receipts"]=function(){return Module["asm"]["wi"].apply(null,arguments)};var _ta_terminal_can_multi_account_selection=Module["_ta_terminal_can_multi_account_selection"]=function(){return Module["asm"]["xi"].apply(null,arguments)};var _ta_terminal_cancel=Module["_ta_terminal_cancel"]=function(){return Module["asm"]["yi"].apply(null,arguments)};var _ta_terminal_cancel2=Module["_ta_terminal_cancel2"]=function(){return Module["asm"]["zi"].apply(null,arguments)};var _ta_terminal_change_settings_async=Module["_ta_terminal_change_settings_async"]=function(){return Module["asm"]["Ai"].apply(null,arguments)};var _ta_terminal_close_dialog_mode_async=Module["_ta_terminal_close_dialog_mode_async"]=function(){return Module["asm"]["Bi"].apply(null,arguments)};var _ta_terminal_close_maintenance_window_async=Module["_ta_terminal_close_maintenance_window_async"]=function(){return Module["asm"]["Ci"].apply(null,arguments)};var _ta_terminal_close_reader_async=Module["_ta_terminal_close_reader_async"]=function(){return Module["asm"]["Di"].apply(null,arguments)};var _ta_terminal_commit_amount_async=Module["_ta_terminal_commit_amount_async"]=function(){return Module["asm"]["Ei"].apply(null,arguments)};var _ta_terminal_commit_async=Module["_ta_terminal_commit_async"]=function(){return Module["asm"]["Fi"].apply(null,arguments)};var _ta_terminal_connect_async=Module["_ta_terminal_connect_async"]=function(){return Module["asm"]["Gi"].apply(null,arguments)};var _ta_terminal_counter_request_async=Module["_ta_terminal_counter_request_async"]=function(){return Module["asm"]["Hi"].apply(null,arguments)};var _ta_terminal_create=Module["_ta_terminal_create"]=function(){return Module["asm"]["Ii"].apply(null,arguments)};var _ta_terminal_dcc_rates_async=Module["_ta_terminal_dcc_rates_async"]=function(){return Module["asm"]["Ji"].apply(null,arguments)};var _ta_terminal_deactivate_async=Module["_ta_terminal_deactivate_async"]=function(){return Module["asm"]["Ki"].apply(null,arguments)};var _ta_terminal_device_maintenance_async=Module["_ta_terminal_device_maintenance_async"]=function(){return Module["asm"]["Li"].apply(null,arguments)};var _ta_terminal_disconnect_async=Module["_ta_terminal_disconnect_async"]=function(){return Module["asm"]["Mi"].apply(null,arguments)};var _ta_terminal_dispose=Module["_ta_terminal_dispose"]=function(){return Module["asm"]["Ni"].apply(null,arguments)};var _ta_terminal_eject_card_async=Module["_ta_terminal_eject_card_async"]=function(){return Module["asm"]["Oi"].apply(null,arguments)};var _ta_terminal_finish_checkout_async=Module["_ta_terminal_finish_checkout_async"]=function(){return Module["asm"]["Pi"].apply(null,arguments)};var _ta_terminal_get_act_seq_counter=Module["_ta_terminal_get_act_seq_counter"]=function(){return Module["asm"]["Qi"].apply(null,arguments)};var _ta_terminal_get_brands=Module["_ta_terminal_get_brands"]=function(){return Module["asm"]["Ri"].apply(null,arguments)};var _ta_terminal_get_config_data=Module["_ta_terminal_get_config_data"]=function(){return Module["asm"]["Si"].apply(null,arguments)};var _ta_terminal_get_customer_data=Module["_ta_terminal_get_customer_data"]=function(){return Module["asm"]["Ti"].apply(null,arguments)};var _ta_terminal_get_ecr_data=Module["_ta_terminal_get_ecr_data"]=function(){return Module["asm"]["Ui"].apply(null,arguments)};var _ta_terminal_get_features=Module["_ta_terminal_get_features"]=function(){return Module["asm"]["Vi"].apply(null,arguments)};var _ta_terminal_get_license=Module["_ta_terminal_get_license"]=function(){return Module["asm"]["Wi"].apply(null,arguments)};var _ta_terminal_get_merchant_options=Module["_ta_terminal_get_merchant_options"]=function(){return Module["asm"]["Xi"].apply(null,arguments)};var _ta_terminal_get_pos_id=Module["_ta_terminal_get_pos_id"]=function(){return Module["asm"]["Yi"].apply(null,arguments)};var _ta_terminal_get_print_options=Module["_ta_terminal_get_print_options"]=function(){return Module["asm"]["Zi"].apply(null,arguments)};var _ta_terminal_get_receipt_formatter=Module["_ta_terminal_get_receipt_formatter"]=function(){return Module["asm"]["_i"].apply(null,arguments)};var _ta_terminal_get_settings=Module["_ta_terminal_get_settings"]=function(){return Module["asm"]["$i"].apply(null,arguments)};var _ta_terminal_get_terminal_id=Module["_ta_terminal_get_terminal_id"]=function(){return Module["asm"]["aj"].apply(null,arguments)};var _ta_terminal_get_terminal_status=Module["_ta_terminal_get_terminal_status"]=function(){return Module["asm"]["bj"].apply(null,arguments)};var _ta_terminal_get_tim_api_version=Module["_ta_terminal_get_tim_api_version"]=function(){return Module["asm"]["cj"].apply(null,arguments)};var _ta_terminal_get_transaction_data=Module["_ta_terminal_get_transaction_data"]=function(){return Module["asm"]["dj"].apply(null,arguments)};var _ta_terminal_get_user_id=Module["_ta_terminal_get_user_id"]=function(){return Module["asm"]["ej"].apply(null,arguments)};var _ta_terminal_hardware_information_async=Module["_ta_terminal_hardware_information_async"]=function(){return Module["asm"]["fj"].apply(null,arguments)};var _ta_terminal_has_sw_update=Module["_ta_terminal_has_sw_update"]=function(){return Module["asm"]["gj"].apply(null,arguments)};var _ta_terminal_hold_commit=Module["_ta_terminal_hold_commit"]=function(){return Module["asm"]["hj"].apply(null,arguments)};var _ta_terminal_init_transaction2_async=Module["_ta_terminal_init_transaction2_async"]=function(){return Module["asm"]["ij"].apply(null,arguments)};var _ta_terminal_init_transaction_async=Module["_ta_terminal_init_transaction_async"]=function(){return Module["asm"]["jj"].apply(null,arguments)};var _ta_terminal_listener_create=Module["_ta_terminal_listener_create"]=function(){return Module["asm"]["kj"].apply(null,arguments)};var _ta_terminal_login_async=Module["_ta_terminal_login_async"]=function(){return Module["asm"]["lj"].apply(null,arguments)};var _ta_terminal_logout_async=Module["_ta_terminal_logout_async"]=function(){return Module["asm"]["mj"].apply(null,arguments)};var _ta_terminal_loyalty_data_async=Module["_ta_terminal_loyalty_data_async"]=function(){return Module["asm"]["nj"].apply(null,arguments)};var _ta_terminal_open_dialog_mode_async=Module["_ta_terminal_open_dialog_mode_async"]=function(){return Module["asm"]["oj"].apply(null,arguments)};var _ta_terminal_open_maintenance_window_async=Module["_ta_terminal_open_maintenance_window_async"]=function(){return Module["asm"]["pj"].apply(null,arguments)};var _ta_terminal_open_reader_async=Module["_ta_terminal_open_reader_async"]=function(){return Module["asm"]["qj"].apply(null,arguments)};var _ta_terminal_print_on_terminal_async=Module["_ta_terminal_print_on_terminal_async"]=function(){return Module["asm"]["rj"].apply(null,arguments)};var _ta_terminal_provide_loyalty_basket_async=Module["_ta_terminal_provide_loyalty_basket_async"]=function(){return Module["asm"]["sj"].apply(null,arguments)};var _ta_terminal_provide_vas_result_async=Module["_ta_terminal_provide_vas_result_async"]=function(){return Module["asm"]["tj"].apply(null,arguments)};var _ta_terminal_reboot_async=Module["_ta_terminal_reboot_async"]=function(){return Module["asm"]["uj"].apply(null,arguments)};var _ta_terminal_receipt_request_async=Module["_ta_terminal_receipt_request_async"]=function(){return Module["asm"]["vj"].apply(null,arguments)};var _ta_terminal_reconciliation_async=Module["_ta_terminal_reconciliation_async"]=function(){return Module["asm"]["wj"].apply(null,arguments)};var _ta_terminal_reconfig_async=Module["_ta_terminal_reconfig_async"]=function(){return Module["asm"]["xj"].apply(null,arguments)};var _ta_terminal_remove_listener=Module["_ta_terminal_remove_listener"]=function(){return Module["asm"]["yj"].apply(null,arguments)};var _ta_terminal_request_alias_async=Module["_ta_terminal_request_alias_async"]=function(){return Module["asm"]["zj"].apply(null,arguments)};var _ta_terminal_rollback_async=Module["_ta_terminal_rollback_async"]=function(){return Module["asm"]["Aj"].apply(null,arguments)};var _ta_terminal_send_card_command_async=Module["_ta_terminal_send_card_command_async"]=function(){return Module["asm"]["Bj"].apply(null,arguments)};var _ta_terminal_set_custom_logger=Module["_ta_terminal_set_custom_logger"]=function(){return Module["asm"]["Cj"].apply(null,arguments)};var _ta_terminal_set_customer_data=Module["_ta_terminal_set_customer_data"]=function(){return Module["asm"]["Dj"].apply(null,arguments)};var _ta_terminal_set_ecr_data=Module["_ta_terminal_set_ecr_data"]=function(){return Module["asm"]["Ej"].apply(null,arguments)};var _ta_terminal_set_merchant_options=Module["_ta_terminal_set_merchant_options"]=function(){return Module["asm"]["Fj"].apply(null,arguments)};var _ta_terminal_set_pos_id=Module["_ta_terminal_set_pos_id"]=function(){return Module["asm"]["Gj"].apply(null,arguments)};var _ta_terminal_set_print_options=Module["_ta_terminal_set_print_options"]=function(){return Module["asm"]["Hj"].apply(null,arguments)};var _ta_terminal_set_receipt_formatter=Module["_ta_terminal_set_receipt_formatter"]=function(){return Module["asm"]["Ij"].apply(null,arguments)};var _ta_terminal_set_transaction_data=Module["_ta_terminal_set_transaction_data"]=function(){return Module["asm"]["Jj"].apply(null,arguments)};var _ta_terminal_set_user_id=Module["_ta_terminal_set_user_id"]=function(){return Module["asm"]["Kj"].apply(null,arguments)};var _ta_terminal_settings_create=Module["_ta_terminal_settings_create"]=function(){return Module["asm"]["Lj"].apply(null,arguments)};var _ta_terminal_settings_get_broadcast_interface=Module["_ta_terminal_settings_get_broadcast_interface"]=function(){return Module["asm"]["Mj"].apply(null,arguments)};var _ta_terminal_settings_get_card_insertion_timeout=Module["_ta_terminal_settings_get_card_insertion_timeout"]=function(){return Module["asm"]["Nj"].apply(null,arguments)};var _ta_terminal_settings_get_card_removal_timeout=Module["_ta_terminal_settings_get_card_removal_timeout"]=function(){return Module["asm"]["Oj"].apply(null,arguments)};var _ta_terminal_settings_get_commit_timeout=Module["_ta_terminal_settings_get_commit_timeout"]=function(){return Module["asm"]["Pj"].apply(null,arguments)};var _ta_terminal_settings_get_connection_ip_port=Module["_ta_terminal_settings_get_connection_ip_port"]=function(){return Module["asm"]["Qj"].apply(null,arguments)};var _ta_terminal_settings_get_connection_ip_string=Module["_ta_terminal_settings_get_connection_ip_string"]=function(){return Module["asm"]["Rj"].apply(null,arguments)};var _ta_terminal_settings_get_connection_mode=Module["_ta_terminal_settings_get_connection_mode"]=function(){return Module["asm"]["Sj"].apply(null,arguments)};var _ta_terminal_settings_get_guides=Module["_ta_terminal_settings_get_guides"]=function(){return Module["asm"]["Tj"].apply(null,arguments)};var _ta_terminal_settings_get_integrator_id=Module["_ta_terminal_settings_get_integrator_id"]=function(){return Module["asm"]["Uj"].apply(null,arguments)};var _ta_terminal_settings_get_log_dir=Module["_ta_terminal_settings_get_log_dir"]=function(){return Module["asm"]["Vj"].apply(null,arguments)};var _ta_terminal_settings_get_log_file_count_per_archive=Module["_ta_terminal_settings_get_log_file_count_per_archive"]=function(){return Module["asm"]["Wj"].apply(null,arguments)};var _ta_terminal_settings_get_log_retain_archive_count=Module["_ta_terminal_settings_get_log_retain_archive_count"]=function(){return Module["asm"]["Xj"].apply(null,arguments)};var _ta_terminal_settings_get_log_retain_file_count=Module["_ta_terminal_settings_get_log_retain_file_count"]=function(){return Module["asm"]["Yj"].apply(null,arguments)};var _ta_terminal_settings_get_manufacturer_flags=Module["_ta_terminal_settings_get_manufacturer_flags"]=function(){return Module["asm"]["Zj"].apply(null,arguments)};var _ta_terminal_settings_get_persistent_state=Module["_ta_terminal_settings_get_persistent_state"]=function(){return Module["asm"]["_j"].apply(null,arguments)};var _ta_terminal_settings_get_proceed_timeout=Module["_ta_terminal_settings_get_proceed_timeout"]=function(){return Module["asm"]["$j"].apply(null,arguments)};var _ta_terminal_settings_get_protocol_type=Module["_ta_terminal_settings_get_protocol_type"]=function(){return Module["asm"]["ak"].apply(null,arguments)};var _ta_terminal_settings_get_request_repetition=Module["_ta_terminal_settings_get_request_repetition"]=function(){return Module["asm"]["bk"].apply(null,arguments)};var _ta_terminal_settings_get_saferpay_base_url=Module["_ta_terminal_settings_get_saferpay_base_url"]=function(){return Module["asm"]["ck"].apply(null,arguments)};var _ta_terminal_settings_get_saferpay_credentials=Module["_ta_terminal_settings_get_saferpay_credentials"]=function(){return Module["asm"]["dk"].apply(null,arguments)};var _ta_terminal_settings_get_saferpay_customer_id=Module["_ta_terminal_settings_get_saferpay_customer_id"]=function(){return Module["asm"]["ek"].apply(null,arguments)};var _ta_terminal_settings_get_saferpay_terminal_id=Module["_ta_terminal_settings_get_saferpay_terminal_id"]=function(){return Module["asm"]["fk"].apply(null,arguments)};var _ta_terminal_settings_get_terminal_id=Module["_ta_terminal_settings_get_terminal_id"]=function(){return Module["asm"]["gk"].apply(null,arguments)};var _ta_terminal_settings_is_allow_closed_card_insert=Module["_ta_terminal_settings_is_allow_closed_card_insert"]=function(){return Module["asm"]["hk"].apply(null,arguments)};var _ta_terminal_settings_is_auto_commit=Module["_ta_terminal_settings_is_auto_commit"]=function(){return Module["asm"]["ik"].apply(null,arguments)};var _ta_terminal_settings_is_auto_shift_management=Module["_ta_terminal_settings_is_auto_shift_management"]=function(){return Module["asm"]["jk"].apply(null,arguments)};var _ta_terminal_settings_is_auto_shutter_management=Module["_ta_terminal_settings_is_auto_shutter_management"]=function(){return Module["asm"]["kk"].apply(null,arguments)};var _ta_terminal_settings_is_dcc=Module["_ta_terminal_settings_is_dcc"]=function(){return Module["asm"]["lk"].apply(null,arguments)};var _ta_terminal_settings_is_enabled_keep_alive=Module["_ta_terminal_settings_is_enabled_keep_alive"]=function(){return Module["asm"]["mk"].apply(null,arguments)};var _ta_terminal_settings_is_fast_ntf_mode=Module["_ta_terminal_settings_is_fast_ntf_mode"]=function(){return Module["asm"]["nk"].apply(null,arguments)};var _ta_terminal_settings_is_fetch_brands=Module["_ta_terminal_settings_is_fetch_brands"]=function(){return Module["asm"]["ok"].apply(null,arguments)};var _ta_terminal_settings_is_partial_approval=Module["_ta_terminal_settings_is_partial_approval"]=function(){return Module["asm"]["pk"].apply(null,arguments)};var _ta_terminal_settings_is_tip_allowed=Module["_ta_terminal_settings_is_tip_allowed"]=function(){return Module["asm"]["qk"].apply(null,arguments)};var _ta_terminal_settings_late_checkin_timeout=Module["_ta_terminal_settings_late_checkin_timeout"]=function(){return Module["asm"]["rk"].apply(null,arguments)};var _ta_terminal_settings_set_allow_closed_card_insert=Module["_ta_terminal_settings_set_allow_closed_card_insert"]=function(){return Module["asm"]["sk"].apply(null,arguments)};var _ta_terminal_settings_set_auto_commit=Module["_ta_terminal_settings_set_auto_commit"]=function(){return Module["asm"]["tk"].apply(null,arguments)};var _ta_terminal_settings_set_auto_shift_management=Module["_ta_terminal_settings_set_auto_shift_management"]=function(){return Module["asm"]["uk"].apply(null,arguments)};var _ta_terminal_settings_set_auto_shutter_management=Module["_ta_terminal_settings_set_auto_shutter_management"]=function(){return Module["asm"]["vk"].apply(null,arguments)};var _ta_terminal_settings_set_broadcast_interface=Module["_ta_terminal_settings_set_broadcast_interface"]=function(){return Module["asm"]["wk"].apply(null,arguments)};var _ta_terminal_settings_set_card_insertion_timeout=Module["_ta_terminal_settings_set_card_insertion_timeout"]=function(){return Module["asm"]["xk"].apply(null,arguments)};var _ta_terminal_settings_set_card_removal_timeout=Module["_ta_terminal_settings_set_card_removal_timeout"]=function(){return Module["asm"]["yk"].apply(null,arguments)};var _ta_terminal_settings_set_commit_timeout=Module["_ta_terminal_settings_set_commit_timeout"]=function(){return Module["asm"]["zk"].apply(null,arguments)};var _ta_terminal_settings_set_connection_ip_port=Module["_ta_terminal_settings_set_connection_ip_port"]=function(){return Module["asm"]["Ak"].apply(null,arguments)};var _ta_terminal_settings_set_connection_ip_string=Module["_ta_terminal_settings_set_connection_ip_string"]=function(){return Module["asm"]["Bk"].apply(null,arguments)};var _ta_terminal_settings_set_connection_mode=Module["_ta_terminal_settings_set_connection_mode"]=function(){return Module["asm"]["Ck"].apply(null,arguments)};var _ta_terminal_settings_set_dcc=Module["_ta_terminal_settings_set_dcc"]=function(){return Module["asm"]["Dk"].apply(null,arguments)};var _ta_terminal_settings_set_enabled_keep_alive=Module["_ta_terminal_settings_set_enabled_keep_alive"]=function(){return Module["asm"]["Ek"].apply(null,arguments)};var _ta_terminal_settings_set_fast_ntf_mode=Module["_ta_terminal_settings_set_fast_ntf_mode"]=function(){return Module["asm"]["Fk"].apply(null,arguments)};var _ta_terminal_settings_set_fetch_brands=Module["_ta_terminal_settings_set_fetch_brands"]=function(){return Module["asm"]["Gk"].apply(null,arguments)};var _ta_terminal_settings_set_guides=Module["_ta_terminal_settings_set_guides"]=function(){return Module["asm"]["Hk"].apply(null,arguments)};var _ta_terminal_settings_set_integrator_id=Module["_ta_terminal_settings_set_integrator_id"]=function(){return Module["asm"]["Ik"].apply(null,arguments)};var _ta_terminal_settings_set_late_checkin_timeout=Module["_ta_terminal_settings_set_late_checkin_timeout"]=function(){return Module["asm"]["Jk"].apply(null,arguments)};var _ta_terminal_settings_set_log_dir=Module["_ta_terminal_settings_set_log_dir"]=function(){return Module["asm"]["Kk"].apply(null,arguments)};var _ta_terminal_settings_set_log_file_count_per_archive=Module["_ta_terminal_settings_set_log_file_count_per_archive"]=function(){return Module["asm"]["Lk"].apply(null,arguments)};var _ta_terminal_settings_set_log_retain_archive_count=Module["_ta_terminal_settings_set_log_retain_archive_count"]=function(){return Module["asm"]["Mk"].apply(null,arguments)};var _ta_terminal_settings_set_log_retain_file_count=Module["_ta_terminal_settings_set_log_retain_file_count"]=function(){return Module["asm"]["Nk"].apply(null,arguments)};var _ta_terminal_settings_set_manufacturer_flags=Module["_ta_terminal_settings_set_manufacturer_flags"]=function(){return Module["asm"]["Ok"].apply(null,arguments)};var _ta_terminal_settings_set_partial_approval=Module["_ta_terminal_settings_set_partial_approval"]=function(){return Module["asm"]["Pk"].apply(null,arguments)};var _ta_terminal_settings_set_persistent_state=Module["_ta_terminal_settings_set_persistent_state"]=function(){return Module["asm"]["Qk"].apply(null,arguments)};var _ta_terminal_settings_set_proceed_timeout=Module["_ta_terminal_settings_set_proceed_timeout"]=function(){return Module["asm"]["Rk"].apply(null,arguments)};var _ta_terminal_settings_set_protocol_type=Module["_ta_terminal_settings_set_protocol_type"]=function(){return Module["asm"]["Sk"].apply(null,arguments)};var _ta_terminal_settings_set_request_repetition=Module["_ta_terminal_settings_set_request_repetition"]=function(){return Module["asm"]["Tk"].apply(null,arguments)};var _ta_terminal_settings_set_saferpay_base_url=Module["_ta_terminal_settings_set_saferpay_base_url"]=function(){return Module["asm"]["Uk"].apply(null,arguments)};var _ta_terminal_settings_set_saferpay_credentials=Module["_ta_terminal_settings_set_saferpay_credentials"]=function(){return Module["asm"]["Vk"].apply(null,arguments)};var _ta_terminal_settings_set_saferpay_customer_id=Module["_ta_terminal_settings_set_saferpay_customer_id"]=function(){return Module["asm"]["Wk"].apply(null,arguments)};var _ta_terminal_settings_set_saferpay_terminal_id=Module["_ta_terminal_settings_set_saferpay_terminal_id"]=function(){return Module["asm"]["Xk"].apply(null,arguments)};var _ta_terminal_settings_set_terminal_id=Module["_ta_terminal_settings_set_terminal_id"]=function(){return Module["asm"]["Yk"].apply(null,arguments)};var _ta_terminal_settings_set_tip_allowed=Module["_ta_terminal_settings_set_tip_allowed"]=function(){return Module["asm"]["Zk"].apply(null,arguments)};var _ta_terminal_show_dialog_async=Module["_ta_terminal_show_dialog_async"]=function(){return Module["asm"]["_k"].apply(null,arguments)};var _ta_terminal_show_signature_capture_async=Module["_ta_terminal_show_signature_capture_async"]=function(){return Module["asm"]["$k"].apply(null,arguments)};var _ta_terminal_software_update_async=Module["_ta_terminal_software_update_async"]=function(){return Module["asm"]["al"].apply(null,arguments)};var _ta_terminal_start_checkout_async=Module["_ta_terminal_start_checkout_async"]=function(){return Module["asm"]["bl"].apply(null,arguments)};var _ta_terminal_status_get_card_data=Module["_ta_terminal_status_get_card_data"]=function(){return Module["asm"]["cl"].apply(null,arguments)};var _ta_terminal_status_get_card_reader_status=Module["_ta_terminal_status_get_card_reader_status"]=function(){return Module["asm"]["dl"].apply(null,arguments)};var _ta_terminal_status_get_connection_status=Module["_ta_terminal_status_get_connection_status"]=function(){return Module["asm"]["el"].apply(null,arguments)};var _ta_terminal_status_get_display_content=Module["_ta_terminal_status_get_display_content"]=function(){return Module["asm"]["fl"].apply(null,arguments)};var _ta_terminal_status_get_final_amount=Module["_ta_terminal_status_get_final_amount"]=function(){return Module["asm"]["gl"].apply(null,arguments)};var _ta_terminal_status_get_management_status=Module["_ta_terminal_status_get_management_status"]=function(){return Module["asm"]["hl"].apply(null,arguments)};var _ta_terminal_status_get_receipt_information=Module["_ta_terminal_status_get_receipt_information"]=function(){return Module["asm"]["il"].apply(null,arguments)};var _ta_terminal_status_get_sleep_mode_status=Module["_ta_terminal_status_get_sleep_mode_status"]=function(){return Module["asm"]["jl"].apply(null,arguments)};var _ta_terminal_status_get_sw_update_available=Module["_ta_terminal_status_get_sw_update_available"]=function(){return Module["asm"]["kl"].apply(null,arguments)};var _ta_terminal_status_get_transaction_status=Module["_ta_terminal_status_get_transaction_status"]=function(){return Module["asm"]["ll"].apply(null,arguments)};var _ta_terminal_system_information_async=Module["_ta_terminal_system_information_async"]=function(){return Module["asm"]["ml"].apply(null,arguments)};var _ta_terminal_transaction2_async=Module["_ta_terminal_transaction2_async"]=function(){return Module["asm"]["nl"].apply(null,arguments)};var _ta_terminal_transaction_async=Module["_ta_terminal_transaction_async"]=function(){return Module["asm"]["ol"].apply(null,arguments)};var _ta_terminal_transaction_cashback_async=Module["_ta_terminal_transaction_cashback_async"]=function(){return Module["asm"]["pl"].apply(null,arguments)};var _ta_terminal_transaction_tip_async=Module["_ta_terminal_transaction_tip_async"]=function(){return Module["asm"]["ql"].apply(null,arguments)};var _ta_theme_e2s=Module["_ta_theme_e2s"]=function(){return Module["asm"]["rl"].apply(null,arguments)};var _ta_tim_error_get_additional_error_info=Module["_ta_tim_error_get_additional_error_info"]=function(){return Module["asm"]["sl"].apply(null,arguments)};var _ta_tim_error_get_error_message=Module["_ta_tim_error_get_error_message"]=function(){return Module["asm"]["tl"].apply(null,arguments)};var _ta_tim_error_get_native_error=Module["_ta_tim_error_get_native_error"]=function(){return Module["asm"]["ul"].apply(null,arguments)};var _ta_tim_error_get_ppinfo=Module["_ta_tim_error_get_ppinfo"]=function(){return Module["asm"]["vl"].apply(null,arguments)};var _ta_tim_error_get_print_data=Module["_ta_tim_error_get_print_data"]=function(){return Module["asm"]["wl"].apply(null,arguments)};var _ta_tim_error_get_rejected_basket=Module["_ta_tim_error_get_rejected_basket"]=function(){return Module["asm"]["xl"].apply(null,arguments)};var _ta_tim_error_get_result_code=Module["_ta_tim_error_get_result_code"]=function(){return Module["asm"]["yl"].apply(null,arguments)};var _ta_timedate_create=Module["_ta_timedate_create"]=function(){return Module["asm"]["zl"].apply(null,arguments)};var _ta_timedate_get_values=Module["_ta_timedate_get_values"]=function(){return Module["asm"]["Al"].apply(null,arguments)};var _ta_total_get_amount_sum=Module["_ta_total_get_amount_sum"]=function(){return Module["asm"]["Bl"].apply(null,arguments)};var _ta_total_get_amount_sum_other=Module["_ta_total_get_amount_sum_other"]=function(){return Module["asm"]["Cl"].apply(null,arguments)};var _ta_total_get_amount_sum_tip=Module["_ta_total_get_amount_sum_tip"]=function(){return Module["asm"]["Dl"].apply(null,arguments)};var _ta_total_get_count=Module["_ta_total_get_count"]=function(){return Module["asm"]["El"].apply(null,arguments)};var _ta_total_get_currency=Module["_ta_total_get_currency"]=function(){return Module["asm"]["Fl"].apply(null,arguments)};var _ta_total_get_exponent=Module["_ta_total_get_exponent"]=function(){return Module["asm"]["Gl"].apply(null,arguments)};var _ta_total_get_trx_details=Module["_ta_total_get_trx_details"]=function(){return Module["asm"]["Hl"].apply(null,arguments)};var _ta_transaction_data_copy=Module["_ta_transaction_data_copy"]=function(){return Module["asm"]["Il"].apply(null,arguments)};var _ta_transaction_data_create=Module["_ta_transaction_data_create"]=function(){return Module["asm"]["Jl"].apply(null,arguments)};var _ta_transaction_data_get_acq_id=Module["_ta_transaction_data_get_acq_id"]=function(){return Module["asm"]["Kl"].apply(null,arguments)};var _ta_transaction_data_get_acq_trans_ref=Module["_ta_transaction_data_get_acq_trans_ref"]=function(){return Module["asm"]["Ll"].apply(null,arguments)};var _ta_transaction_data_get_app_expiration_date=Module["_ta_transaction_data_get_app_expiration_date"]=function(){return Module["asm"]["Ml"].apply(null,arguments)};var _ta_transaction_data_get_card_ref=Module["_ta_transaction_data_get_card_ref"]=function(){return Module["asm"]["Nl"].apply(null,arguments)};var _ta_transaction_data_get_cvc2=Module["_ta_transaction_data_get_cvc2"]=function(){return Module["asm"]["Ol"].apply(null,arguments)};var _ta_transaction_data_get_dcc_allowed=Module["_ta_transaction_data_get_dcc_allowed"]=function(){return Module["asm"]["Pl"].apply(null,arguments)};var _ta_transaction_data_get_deferred_auth_ind=Module["_ta_transaction_data_get_deferred_auth_ind"]=function(){return Module["asm"]["Ql"].apply(null,arguments)};var _ta_transaction_data_get_ecr_seq_counter=Module["_ta_transaction_data_get_ecr_seq_counter"]=function(){return Module["asm"]["Rl"].apply(null,arguments)};var _ta_transaction_data_get_installment_allowed=Module["_ta_transaction_data_get_installment_allowed"]=function(){return Module["asm"]["Sl"].apply(null,arguments)};var _ta_transaction_data_get_language=Module["_ta_transaction_data_get_language"]=function(){return Module["asm"]["Tl"].apply(null,arguments)};var _ta_transaction_data_get_multi_currency_flag=Module["_ta_transaction_data_get_multi_currency_flag"]=function(){return Module["asm"]["Ul"].apply(null,arguments)};var _ta_transaction_data_get_ngv_clearing_delay=Module["_ta_transaction_data_get_ngv_clearing_delay"]=function(){return Module["asm"]["Vl"].apply(null,arguments)};var _ta_transaction_data_get_ngv_mode=Module["_ta_transaction_data_get_ngv_mode"]=function(){return Module["asm"]["Wl"].apply(null,arguments)};var _ta_transaction_data_get_partial_approval_allowed=Module["_ta_transaction_data_get_partial_approval_allowed"]=function(){return Module["asm"]["Xl"].apply(null,arguments)};var _ta_transaction_data_get_phone_auth_code=Module["_ta_transaction_data_get_phone_auth_code"]=function(){return Module["asm"]["Yl"].apply(null,arguments)};var _ta_transaction_data_get_saferpay_alias=Module["_ta_transaction_data_get_saferpay_alias"]=function(){return Module["asm"]["Zl"].apply(null,arguments)};var _ta_transaction_data_get_saferpay_recurring=Module["_ta_transaction_data_get_saferpay_recurring"]=function(){return Module["asm"]["_l"].apply(null,arguments)};var _ta_transaction_data_get_six_trx_ref_num=Module["_ta_transaction_data_get_six_trx_ref_num"]=function(){return Module["asm"]["$l"].apply(null,arguments)};var _ta_transaction_data_get_sub_transactions=Module["_ta_transaction_data_get_sub_transactions"]=function(){return Module["asm"]["am"].apply(null,arguments)};var _ta_transaction_data_get_tip_allowed=Module["_ta_transaction_data_get_tip_allowed"]=function(){return Module["asm"]["bm"].apply(null,arguments)};var _ta_transaction_data_get_trans_ref=Module["_ta_transaction_data_get_trans_ref"]=function(){return Module["asm"]["cm"].apply(null,arguments)};var _ta_transaction_data_get_trans_seq=Module["_ta_transaction_data_get_trans_seq"]=function(){return Module["asm"]["dm"].apply(null,arguments)};var _ta_transaction_data_get_transaction_reason=Module["_ta_transaction_data_get_transaction_reason"]=function(){return Module["asm"]["em"].apply(null,arguments)};var _ta_transaction_data_get_trm_trans_ref=Module["_ta_transaction_data_get_trm_trans_ref"]=function(){return Module["asm"]["fm"].apply(null,arguments)};var _ta_transaction_data_get_trx_original_date=Module["_ta_transaction_data_get_trx_original_date"]=function(){return Module["asm"]["gm"].apply(null,arguments)};var _ta_transaction_data_set_acq_id=Module["_ta_transaction_data_set_acq_id"]=function(){return Module["asm"]["hm"].apply(null,arguments)};var _ta_transaction_data_set_acq_trans_ref=Module["_ta_transaction_data_set_acq_trans_ref"]=function(){return Module["asm"]["im"].apply(null,arguments)};var _ta_transaction_data_set_app_expiration_date=Module["_ta_transaction_data_set_app_expiration_date"]=function(){return Module["asm"]["jm"].apply(null,arguments)};var _ta_transaction_data_set_card_ref=Module["_ta_transaction_data_set_card_ref"]=function(){return Module["asm"]["km"].apply(null,arguments)};var _ta_transaction_data_set_cvc2=Module["_ta_transaction_data_set_cvc2"]=function(){return Module["asm"]["lm"].apply(null,arguments)};var _ta_transaction_data_set_dcc_allowed=Module["_ta_transaction_data_set_dcc_allowed"]=function(){return Module["asm"]["mm"].apply(null,arguments)};var _ta_transaction_data_set_deferred_auth_ind=Module["_ta_transaction_data_set_deferred_auth_ind"]=function(){return Module["asm"]["nm"].apply(null,arguments)};var _ta_transaction_data_set_ecr_seq_counter=Module["_ta_transaction_data_set_ecr_seq_counter"]=function(){return Module["asm"]["om"].apply(null,arguments)};var _ta_transaction_data_set_installment_allowed=Module["_ta_transaction_data_set_installment_allowed"]=function(){return Module["asm"]["pm"].apply(null,arguments)};var _ta_transaction_data_set_language=Module["_ta_transaction_data_set_language"]=function(){return Module["asm"]["qm"].apply(null,arguments)};var _ta_transaction_data_set_multi_currency_flag=Module["_ta_transaction_data_set_multi_currency_flag"]=function(){return Module["asm"]["rm"].apply(null,arguments)};var _ta_transaction_data_set_ngv_clearing_delay=Module["_ta_transaction_data_set_ngv_clearing_delay"]=function(){return Module["asm"]["sm"].apply(null,arguments)};var _ta_transaction_data_set_ngv_mode=Module["_ta_transaction_data_set_ngv_mode"]=function(){return Module["asm"]["tm"].apply(null,arguments)};var _ta_transaction_data_set_partial_approval_allowed=Module["_ta_transaction_data_set_partial_approval_allowed"]=function(){return Module["asm"]["um"].apply(null,arguments)};var _ta_transaction_data_set_phone_auth_code=Module["_ta_transaction_data_set_phone_auth_code"]=function(){return Module["asm"]["vm"].apply(null,arguments)};var _ta_transaction_data_set_saferpay_alias=Module["_ta_transaction_data_set_saferpay_alias"]=function(){return Module["asm"]["wm"].apply(null,arguments)};var _ta_transaction_data_set_saferpay_recurring=Module["_ta_transaction_data_set_saferpay_recurring"]=function(){return Module["asm"]["xm"].apply(null,arguments)};var _ta_transaction_data_set_six_trx_ref_num=Module["_ta_transaction_data_set_six_trx_ref_num"]=function(){return Module["asm"]["ym"].apply(null,arguments)};var _ta_transaction_data_set_sub_transactions=Module["_ta_transaction_data_set_sub_transactions"]=function(){return Module["asm"]["zm"].apply(null,arguments)};var _ta_transaction_data_set_tip_allowed=Module["_ta_transaction_data_set_tip_allowed"]=function(){return Module["asm"]["Am"].apply(null,arguments)};var _ta_transaction_data_set_trans_ref=Module["_ta_transaction_data_set_trans_ref"]=function(){return Module["asm"]["Bm"].apply(null,arguments)};var _ta_transaction_data_set_trans_seq=Module["_ta_transaction_data_set_trans_seq"]=function(){return Module["asm"]["Cm"].apply(null,arguments)};var _ta_transaction_data_set_transaction_reason=Module["_ta_transaction_data_set_transaction_reason"]=function(){return Module["asm"]["Dm"].apply(null,arguments)};var _ta_transaction_data_set_trm_trans_ref=Module["_ta_transaction_data_set_trm_trans_ref"]=function(){return Module["asm"]["Em"].apply(null,arguments)};var _ta_transaction_data_set_trx_original_date=Module["_ta_transaction_data_set_trx_original_date"]=function(){return Module["asm"]["Fm"].apply(null,arguments)};var _ta_transaction_information_get_account_number=Module["_ta_transaction_information_get_account_number"]=function(){return Module["asm"]["Gm"].apply(null,arguments)};var _ta_transaction_information_get_acq=Module["_ta_transaction_information_get_acq"]=function(){return Module["asm"]["Hm"].apply(null,arguments)};var _ta_transaction_information_get_acq_trans_ref=Module["_ta_transaction_information_get_acq_trans_ref"]=function(){return Module["asm"]["Im"].apply(null,arguments)};var _ta_transaction_information_get_auth_code=Module["_ta_transaction_information_get_auth_code"]=function(){return Module["asm"]["Jm"].apply(null,arguments)};var _ta_transaction_information_get_card_id=Module["_ta_transaction_information_get_card_id"]=function(){return Module["asm"]["Km"].apply(null,arguments)};var _ta_transaction_information_get_cardholder_name=Module["_ta_transaction_information_get_cardholder_name"]=function(){return Module["asm"]["Lm"].apply(null,arguments)};var _ta_transaction_information_get_client_identifier=Module["_ta_transaction_information_get_client_identifier"]=function(){return Module["asm"]["Mm"].apply(null,arguments)};var _ta_transaction_information_get_cvm=Module["_ta_transaction_information_get_cvm"]=function(){return Module["asm"]["Nm"].apply(null,arguments)};var _ta_transaction_information_get_merchant_action=Module["_ta_transaction_information_get_merchant_action"]=function(){return Module["asm"]["Om"].apply(null,arguments)};var _ta_transaction_information_get_ngv_used_flag=Module["_ta_transaction_information_get_ngv_used_flag"]=function(){return Module["asm"]["Pm"].apply(null,arguments)};var _ta_transaction_information_get_person_oid=Module["_ta_transaction_information_get_person_oid"]=function(){return Module["asm"]["Qm"].apply(null,arguments)};var _ta_transaction_information_get_pos_entry_mode=Module["_ta_transaction_information_get_pos_entry_mode"]=function(){return Module["asm"]["Rm"].apply(null,arguments)};var _ta_transaction_information_get_signature_information=Module["_ta_transaction_information_get_signature_information"]=function(){return Module["asm"]["Sm"].apply(null,arguments)};var _ta_transaction_information_get_six_trx_ref_num=Module["_ta_transaction_information_get_six_trx_ref_num"]=function(){return Module["asm"]["Tm"].apply(null,arguments)};var _ta_transaction_information_get_time_stamp=Module["_ta_transaction_information_get_time_stamp"]=function(){return Module["asm"]["Um"].apply(null,arguments)};var _ta_transaction_information_get_trans_ref=Module["_ta_transaction_information_get_trans_ref"]=function(){return Module["asm"]["Vm"].apply(null,arguments)};var _ta_transaction_information_get_trans_seq=Module["_ta_transaction_information_get_trans_seq"]=function(){return Module["asm"]["Wm"].apply(null,arguments)};var _ta_transaction_information_get_trm_trans_ref=Module["_ta_transaction_information_get_trm_trans_ref"]=function(){return Module["asm"]["Xm"].apply(null,arguments)};var _ta_transaction_reason_e2s=Module["_ta_transaction_reason_e2s"]=function(){return Module["asm"]["Ym"].apply(null,arguments)};var _ta_transaction_request_copy=Module["_ta_transaction_request_copy"]=function(){return Module["asm"]["Zm"].apply(null,arguments)};var _ta_transaction_request_create=Module["_ta_transaction_request_create"]=function(){return Module["asm"]["_m"].apply(null,arguments)};var _ta_transaction_request_get_additional_info=Module["_ta_transaction_request_get_additional_info"]=function(){return Module["asm"]["$m"].apply(null,arguments)};var _ta_transaction_request_get_amount=Module["_ta_transaction_request_get_amount"]=function(){return Module["asm"]["an"].apply(null,arguments)};var _ta_transaction_request_get_amount_discount=Module["_ta_transaction_request_get_amount_discount"]=function(){return Module["asm"]["bn"].apply(null,arguments)};var _ta_transaction_request_get_amount_other=Module["_ta_transaction_request_get_amount_other"]=function(){return Module["asm"]["cn"].apply(null,arguments)};var _ta_transaction_request_get_amount_tip=Module["_ta_transaction_request_get_amount_tip"]=function(){return Module["asm"]["dn"].apply(null,arguments)};var _ta_transaction_request_get_basket=Module["_ta_transaction_request_get_basket"]=function(){return Module["asm"]["en"].apply(null,arguments)};var _ta_transaction_request_get_customer_data=Module["_ta_transaction_request_get_customer_data"]=function(){return Module["asm"]["fn"].apply(null,arguments)};var _ta_transaction_request_get_loyalty_coupon_list=Module["_ta_transaction_request_get_loyalty_coupon_list"]=function(){return Module["asm"]["gn"].apply(null,arguments)};var _ta_transaction_request_get_merchant_options=Module["_ta_transaction_request_get_merchant_options"]=function(){return Module["asm"]["hn"].apply(null,arguments)};var _ta_transaction_request_get_retain_card=Module["_ta_transaction_request_get_retain_card"]=function(){return Module["asm"]["jn"].apply(null,arguments)};var _ta_transaction_request_get_transaction_data=Module["_ta_transaction_request_get_transaction_data"]=function(){return Module["asm"]["kn"].apply(null,arguments)};var _ta_transaction_request_get_user_id=Module["_ta_transaction_request_get_user_id"]=function(){return Module["asm"]["ln"].apply(null,arguments)};var _ta_transaction_request_set_additional_info=Module["_ta_transaction_request_set_additional_info"]=function(){return Module["asm"]["mn"].apply(null,arguments)};var _ta_transaction_request_set_amount=Module["_ta_transaction_request_set_amount"]=function(){return Module["asm"]["nn"].apply(null,arguments)};var _ta_transaction_request_set_amount_discount=Module["_ta_transaction_request_set_amount_discount"]=function(){return Module["asm"]["on"].apply(null,arguments)};var _ta_transaction_request_set_amount_other=Module["_ta_transaction_request_set_amount_other"]=function(){return Module["asm"]["pn"].apply(null,arguments)};var _ta_transaction_request_set_amount_tip=Module["_ta_transaction_request_set_amount_tip"]=function(){return Module["asm"]["qn"].apply(null,arguments)};var _ta_transaction_request_set_basket=Module["_ta_transaction_request_set_basket"]=function(){return Module["asm"]["rn"].apply(null,arguments)};var _ta_transaction_request_set_customer_data=Module["_ta_transaction_request_set_customer_data"]=function(){return Module["asm"]["sn"].apply(null,arguments)};var _ta_transaction_request_set_loyalty_coupon_list=Module["_ta_transaction_request_set_loyalty_coupon_list"]=function(){return Module["asm"]["tn"].apply(null,arguments)};var _ta_transaction_request_set_merchant_options=Module["_ta_transaction_request_set_merchant_options"]=function(){return Module["asm"]["un"].apply(null,arguments)};var _ta_transaction_request_set_retain_card=Module["_ta_transaction_request_set_retain_card"]=function(){return Module["asm"]["vn"].apply(null,arguments)};var _ta_transaction_request_set_transaction_data=Module["_ta_transaction_request_set_transaction_data"]=function(){return Module["asm"]["wn"].apply(null,arguments)};var _ta_transaction_request_set_user_id=Module["_ta_transaction_request_set_user_id"]=function(){return Module["asm"]["xn"].apply(null,arguments)};var _ta_transaction_response_get_additional_info=Module["_ta_transaction_response_get_additional_info"]=function(){return Module["asm"]["yn"].apply(null,arguments)};var _ta_transaction_response_get_amount=Module["_ta_transaction_response_get_amount"]=function(){return Module["asm"]["zn"].apply(null,arguments)};var _ta_transaction_response_get_amount_dcc=Module["_ta_transaction_response_get_amount_dcc"]=function(){return Module["asm"]["An"].apply(null,arguments)};var _ta_transaction_response_get_amount_due=Module["_ta_transaction_response_get_amount_due"]=function(){return Module["asm"]["Bn"].apply(null,arguments)};var _ta_transaction_response_get_amount_loyalty_cashback=Module["_ta_transaction_response_get_amount_loyalty_cashback"]=function(){return Module["asm"]["Cn"].apply(null,arguments)};var _ta_transaction_response_get_amount_other=Module["_ta_transaction_response_get_amount_other"]=function(){return Module["asm"]["Dn"].apply(null,arguments)};var _ta_transaction_response_get_amount_saldo=Module["_ta_transaction_response_get_amount_saldo"]=function(){return Module["asm"]["En"].apply(null,arguments)};var _ta_transaction_response_get_amount_tip=Module["_ta_transaction_response_get_amount_tip"]=function(){return Module["asm"]["Fn"].apply(null,arguments)};var _ta_transaction_response_get_basket=Module["_ta_transaction_response_get_basket"]=function(){return Module["asm"]["Gn"].apply(null,arguments)};var _ta_transaction_response_get_card_data=Module["_ta_transaction_response_get_card_data"]=function(){return Module["asm"]["Hn"].apply(null,arguments)};var _ta_transaction_response_get_dcc_disclaimer=Module["_ta_transaction_response_get_dcc_disclaimer"]=function(){return Module["asm"]["In"].apply(null,arguments)};var _ta_transaction_response_get_print_data=Module["_ta_transaction_response_get_print_data"]=function(){return Module["asm"]["Jn"].apply(null,arguments)};var _ta_transaction_response_get_transaction_information=Module["_ta_transaction_response_get_transaction_information"]=function(){return Module["asm"]["Kn"].apply(null,arguments)};var _ta_transaction_response_get_type=Module["_ta_transaction_response_get_type"]=function(){return Module["asm"]["Ln"].apply(null,arguments)};var _ta_transaction_response_needs_action=Module["_ta_transaction_response_needs_action"]=function(){return Module["asm"]["Mn"].apply(null,arguments)};var _ta_transaction_response_was_dcc=Module["_ta_transaction_response_was_dcc"]=function(){return Module["asm"]["Nn"].apply(null,arguments)};var _ta_transaction_response_was_partial_approved=Module["_ta_transaction_response_was_partial_approved"]=function(){return Module["asm"]["On"].apply(null,arguments)};var _ta_transaction_response_was_tip=Module["_ta_transaction_response_was_tip"]=function(){return Module["asm"]["Pn"].apply(null,arguments)};var _ta_transaction_status_e2s=Module["_ta_transaction_status_e2s"]=function(){return Module["asm"]["Qn"].apply(null,arguments)};var _ta_transaction_type_e2s=Module["_ta_transaction_type_e2s"]=function(){return Module["asm"]["Rn"].apply(null,arguments)};var _ta_trx_detail_get_aid=Module["_ta_trx_detail_get_aid"]=function(){return Module["asm"]["Sn"].apply(null,arguments)};var _ta_trx_detail_get_amount_sum=Module["_ta_trx_detail_get_amount_sum"]=function(){return Module["asm"]["Tn"].apply(null,arguments)};var _ta_trx_detail_get_amount_sum_other=Module["_ta_trx_detail_get_amount_sum_other"]=function(){return Module["asm"]["Un"].apply(null,arguments)};var _ta_trx_detail_get_amount_sum_tip=Module["_ta_trx_detail_get_amount_sum_tip"]=function(){return Module["asm"]["Vn"].apply(null,arguments)};var _ta_trx_detail_get_count=Module["_ta_trx_detail_get_count"]=function(){return Module["asm"]["Wn"].apply(null,arguments)};var _ta_trx_detail_get_dcc_flag=Module["_ta_trx_detail_get_dcc_flag"]=function(){return Module["asm"]["Xn"].apply(null,arguments)};var _ta_trx_detail_get_markup=Module["_ta_trx_detail_get_markup"]=function(){return Module["asm"]["Yn"].apply(null,arguments)};var _ta_trx_detail_get_markup_exponent=Module["_ta_trx_detail_get_markup_exponent"]=function(){return Module["asm"]["Zn"].apply(null,arguments)};var _ta_trx_detail_get_ngvused_flag=Module["_ta_trx_detail_get_ngvused_flag"]=function(){return Module["asm"]["_n"].apply(null,arguments)};var _ta_trx_detail_get_transaction_type=Module["_ta_trx_detail_get_transaction_type"]=function(){return Module["asm"]["$n"].apply(null,arguments)};var _ta_ultra_compact_receipt_formatter_create=Module["_ta_ultra_compact_receipt_formatter_create"]=function(){return Module["asm"]["ao"].apply(null,arguments)};var _ta_update_status_e2s=Module["_ta_update_status_e2s"]=function(){return Module["asm"]["bo"].apply(null,arguments)};var _ta_vas_checkout_information_get_loyalty_coupons=Module["_ta_vas_checkout_information_get_loyalty_coupons"]=function(){return Module["asm"]["co"].apply(null,arguments)};var _ta_vas_checkout_information_get_loyalty_information=Module["_ta_vas_checkout_information_get_loyalty_information"]=function(){return Module["asm"]["eo"].apply(null,arguments)};var _ta_vas_checkout_information_get_provide_basket=Module["_ta_vas_checkout_information_get_provide_basket"]=function(){return Module["asm"]["fo"].apply(null,arguments)};var _ta_vas_checkout_information_get_vas_information=Module["_ta_vas_checkout_information_get_vas_information"]=function(){return Module["asm"]["go"].apply(null,arguments)};var _ta_vas_checkout_information_get_vas_information_list_type=Module["_ta_vas_checkout_information_get_vas_information_list_type"]=function(){return Module["asm"]["ho"].apply(null,arguments)};var _ta_vas_info_list_type_e2s=Module["_ta_vas_info_list_type_e2s"]=function(){return Module["asm"]["io"].apply(null,arguments)};var _ta_vas_info_type_e2s=Module["_ta_vas_info_type_e2s"]=function(){return Module["asm"]["jo"].apply(null,arguments)};var _ta_vas_result_create=Module["_ta_vas_result_create"]=function(){return Module["asm"]["ko"].apply(null,arguments)};var _ta_vas_result_get_vas_information=Module["_ta_vas_result_get_vas_information"]=function(){return Module["asm"]["lo"].apply(null,arguments)};var _ta_vas_result_get_vas_information_list_type=Module["_ta_vas_result_get_vas_information_list_type"]=function(){return Module["asm"]["mo"].apply(null,arguments)};var stackAlloc=Module["stackAlloc"]=function(){return Module["asm"]["oo"].apply(null,arguments)};var stackRestore=Module["stackRestore"]=function(){return Module["asm"]["po"].apply(null,arguments)};var stackSave=Module["stackSave"]=function(){return Module["asm"]["qo"].apply(null,arguments)};var dynCall_vi=Module["dynCall_vi"]=function(){return Module["asm"]["no"].apply(null,arguments)};Module["asm"]=asm;Module["cwrap"]=cwrap;Module["setValue"]=setValue;Module["getValue"]=getValue;Module["UTF8ToString"]=UTF8ToString;Module["stringToUTF8"]=stringToUTF8;Module["lengthBytesUTF8"]=lengthBytesUTF8;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}ExitStatus.prototype=new Error;ExitStatus.prototype.constructor=ExitStatus;dependenciesFulfilled=function runCaller(){if(!Module["calledRun"])run();if(!Module["calledRun"])dependenciesFulfilled=runCaller};function run(args){args=args||Module["arguments"];if(runDependencies>0){return}preRun();if(runDependencies>0)return;if(Module["calledRun"])return;function doRun(){if(Module["calledRun"])return;Module["calledRun"]=true;if(ABORT)return;ensureInitRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}if(what!==undefined){out(what);err(what);what=JSON.stringify(what)}else{what=""}ABORT=true;EXITSTATUS=1;throw"abort("+what+"). Build with -s ASSERTIONS=1 for more info."}Module["abort"]=abort;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}Module["noExitRuntime"]=true;run();onTimApiReady=undefined;onTimApiPublishLogRecord=undefined;onTimApiLog=undefined;Module.onRuntimeInitialized=function(){var values=timapi.constants.ResultCode.values().map(function(each){return[each.name,each]});values.push(["invalidArgument",new timapi.constants.EnumValue("invalidArgument",values.length+1)]);values.push(["outOfMemory",new timapi.constants.EnumValue("outOfMemory",values.length+1)]);values.push(["systemError",new timapi.constants.EnumValue("systemError",values.length+1)]);values.push(["invalidState",new timapi.constants.EnumValue("invalidState",values.length+1)]);timapi.constants.ResultCode=new timapi.constants.Enumeration(values);timapi.log("TimApi "+timapi.getApiVersion()+" Ready");timapi._TimApiHelpers.getFunction("__six_internal_test")(5);if(onTimApiReady){onTimApiReady()}else{let error="Global callback onTimApiReady is undefined. "+'Add "onTimApiReady = function() { ... }" to your script file to use TIM API';timapi.log(error);throw error}};timapi={getApiVersion:function(){let t=timapi._TimApiHelpers;if(!t.getApiVersion){t.getApiVersion=t.getFunction2("ta_terminal_get_tim_api_version","string")}return t.getApiVersion()},log:function(message){if(onTimApiLog){onTimApiLog(message)}else{console.log(message)}}};timapi.constants={};timapi.constants.EnumValue=class{constructor(name,value){this.name=name;this._value=value;Object.freeze(this)}toString(){return this.name}};timapi.constants.Enumeration=class{constructor(names,startValue){let _this=this;let value=startValue!==undefined?startValue:1;names.forEach(function(name){if(Array.isArray(name)){_this[name[0]]=name[1]}else{_this[name]=new timapi.constants.EnumValue(name,value++)}});Object.freeze(this)}values(){return Object.values(this)}toString(){return`Enumeration(${this.values().length} entries)`}};timapi.TimException=class extends Error{constructor(resultCode,message){if(message!==undefined){super(`timapi.TimException(${resultCode}, message=${message})`)}else{super(`timapi.TimException(${resultCode})`)}this.resultCode=resultCode;this.errorText=String(resultCode);if(message){this.errorText=`${message} (${resultCode})`}this.nativeError=undefined;this.ppInfo=undefined;this.additionalErrorInfo=undefined;this.printData=undefined;this.rejectedBasket=undefined;this.stack;if(typeof jsStackTrace!==undefined){this.stack=jsStackTrace()}}toString(){let s=`${this.constructor.name}(`+`resultCode=${this.resultCode}`+` errorText=${this.errorText}`+` nativeError=${this.nativeError}`+` ppInfo=${this.ppInfo}`+` additionalErrorInfo=${this.additionalErrorInfo}`+` printData=${this.printData}`+` rejectedBasket=${this.rejectedBasket}`;if(this.stack){s=s+" \n stack: "+this.stack}s=s+")";return s}static assert(resultCode){if(resultCode!=timapi.constants.ResultCode.ok._value){throw new timapi.TimException(timapi._TimApiHelpers.enumByValue(timapi.constants.ResultCode,resultCode))}}};Object.defineProperty(timapi.TimException,"name",{value:"timapi.TimException"});timapi._TimApiHelpers={getApiVersion:undefined,getFunction:function(name,argTypeAfterFirst){let f=Module.cwrap(name,"number",["number"].concat(argTypeAfterFirst||[]));if(!f){throw"Missing binding for "+name}return f},getFunction2:function(name,returnType,args){let f=undefined;if(Array.isArray(returnType)){f=Module.cwrap(name,"undefined",returnType)}else{f=Module.cwrap(name,returnType||"undefined",args||[])}if(!f){throw"Missing binding for "+name}return f},enumByValue:function(enumeration,value){return enumeration.values().filter(function(each){return each._value==value})[0]},currencyByValue:function(enumeration,value){return enumeration.values().filter(function(each){return each._wav==value})[0]},enumSetToEnumBitcode:function(enumSet){let enumBitSet=0;for(let enumItem of enumSet){enumBitSet+=Math.pow(2,enumItem._value-1)}return enumBitSet},enumBitcodeToEnumSet:function(enumeration,enumBitSet){let enumSet=new Set;for(let enumItem of enumeration.values()){if(enumBitSet&Math.pow(2,enumItem._value-1)){enumSet.add(enumItem)}}return enumSet},boolConvertJsToC:function(jsBool){if(jsBool===true){return 1}else if(jsBool===false){return 2}return 0},boolConvertCToJs:function(cBool){if(cBool===1){return true}else if(cBool===2){return false}return undefined},getColorStruct:function(color){let mem_color;try{mem_color=Module._malloc(3);if(mem_color!=0){Module.setValue(mem_color,color.r,"i8");Module.setValue(mem_color+1,color.g,"i8");Module.setValue(mem_color+2,color.b,"i8")}}catch(err){if(mem_color)Module._free(mem_color);throw err}return mem_color},alingMemoryAddress(address){return Math.round(address/4)*4},helpers:undefined,prepareHelpers:function(){let h=timapi._TimApiHelpers.helpers;if(!h){h=timapi._TimApiHelpers.helpers={ebv:timapi._TimApiHelpers.enumByValue,cbv:timapi._TimApiHelpers.currencyByValue,eSet_t_eBit:timapi._TimApiHelpers.enumSetToEnumBitcode,eBit_t_eSet:timapi._TimApiHelpers.enumBitcodeToEnumSet,bc_js_t_c:timapi._TimApiHelpers.boolConvertJsToC,bc_c_t_js:timapi._TimApiHelpers.boolConvertCToJs,contentOf:timapi._TimApiHelpers.TAString.contentOf,contentOfIfValid:timapi._TimApiHelpers.TAString.contentOfIfValid,contentOfUint8ArrayIfValid:timapi._TimApiHelpers.TAString.contentOfUint8ArrayIfValid,assert:timapi.TimException.assert,lit:timapi._TimApiHelpers.iterateList,col:timapi._TimApiHelpers.getColorStruct,alm:timapi._TimApiHelpers.alingMemoryAddress}}return h},convert64BitTo32Bit:function(value){if(typeof value=="bigint"){value=Number(value)}if(value>Number.MAX_SAFE_INTEGER){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,"Amount is too large (only 52bits supported by JavaScript)")}let sign=value<0?1:0;value=Math.abs(value);let low=value&2147483647;let high=Math.floor(value/2147483648);return{low:low,high:high,sign:sign}},getBinaryContent:function(c_datastring){if(!c_datastring.isValid()){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.TAString.prepareString();let p;let binaryContent=undefined;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_length(c_datastring.v(),p.p()));let len=p.v();binaryContent=new Uint8Array(len);h.assert(f.get_pointer(c_datastring.v(),p.p()));for(let i=0;i<len;i++){binaryContent[i]=Module.getValue(p.v()+i)}}finally{if(p)p.dispose()}return binaryContent},uint8ArrayAsDebugString:function(binaryContent,num){if(binaryContent==undefined){return"undefined"}let s="Uint8Array(";let i=0;for(i=0;i<binaryContent.length&&(i<num||num===undefined);i++){s+=`0x${binaryContent[i].toString(16)},`}if(i<binaryContent.length){s+="..."}else{s=s.substring(0,s.length-1)}s+=")";return s},arrayAsDebugString:function(array){if(array==undefined){return`[]`}return`[${array.toString()}]`},setAsDebugString:function(set){if(set==undefined){return`[]`}return`[${[...set].join("|")}]`},mapAsDebugString:function(map){if(map==undefined||map.size<=0){return`{}`}let mapString="{";for(let entry of map.entries()){mapString+=entry[0]+"="+entry[1]+", "}return mapString.substring(0,mapString.length-2)+"}"},stringUint8MapAsDebugString:function(map){if(map==undefined||map.size<=0){return`{}`}let mapString="{";for(let entry of map.entries()){mapString+=entry[0]+"="+timapi._TimApiHelpers.uint8ArrayAsDebugString(entry[1])+", "}return mapString.substring(0,mapString.length-2)+"}"},terminalStatusChanged:function(terminal){let timTerminal=timapi._TimApiHelpers.terminalMap[Number(terminal)];timTerminal._terminalStatus=undefined;timTerminal._listenersLocked=true;try{timTerminal._listeners.forEach(function(each){each.terminalStatusChanged(timTerminal)})}finally{timTerminal._listenersLocked=false}},terminalDisconnected:function(terminal,result_code){let timTerminal=timapi._TimApiHelpers.terminalMap[Number(terminal)];let timResultCode=timapi._TimApiHelpers.enumByValue(timapi.constants.ResultCode,result_code);timTerminal._listeners.forEach(function(each){each.disconnected(timTerminal,timResultCode)})},terminalCompleted:function(event,c_data){let h=timapi._TimApiHelpers.prepareHelpers();let data;let timEvent=new timapi.TimEvent;let p;let timTerminal=timapi._TimApiHelpers.terminalMap[Module.getValue(event,"i32")];timEvent.terminal=timTerminal;let resultCode=h.ebv(timapi.constants.ResultCode,Module.getValue(event+4,"i32"));timEvent.requestType=h.ebv(timapi.constants.RequestType,Module.getValue(event+8,"i32"));timEvent.exception=timapi._TimApiHelpers.unwrapTimError(Module.getValue(event+12,"i32"),resultCode);Object.freeze(timEvent);timTerminal._listenersLocked=true;try{switch(timEvent.requestType){case timapi.constants.RequestType.activate:if(c_data){p=new timapi._TimApiHelpers.TAPointer;h.assert(timapi._TimApiHelpers.prepareActivateResponse().get_print_data(c_data,p.p()));if(p.isValid()){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),p.v()))}}data=timapi._TimApiHelpers.unwrapActivateResponse(c_data);for(let each of timTerminal._listeners){each.activateCompleted(timEvent,data)}break;case timapi.constants.RequestType.activateServiceMenu:for(let each of timTerminal._listeners){each.activateServiceMenuCompleted(timEvent)}break;case timapi.constants.RequestType.applicationInformation:for(let each of timTerminal._listeners){each.applicationInformationCompleted(timEvent)}break;case timapi.constants.RequestType.balance:if(c_data){p=new timapi._TimApiHelpers.TAPointer;h.assert(timapi._TimApiHelpers.prepareBalanceResponse().get_print_data(c_data,p.p()));if(p.isValid()){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),p.v()))}}data=timapi._TimApiHelpers.unwrapBalanceResponse(c_data);for(let each of timTerminal._listeners){each.balanceCompleted(timEvent,data)}break;case timapi.constants.RequestType.balanceInquiry:if(c_data){p=new timapi._TimApiHelpers.TAPointer;h.assert(timapi._TimApiHelpers.prepareBalanceInquiryResponse().get_print_data(c_data,p.p()));if(p.isValid()){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),p.v()))}}data=timapi._TimApiHelpers.unwrapBalanceInquiryResponse(c_data);for(let each of timTerminal._listeners){each.balanceInquiryCompleted(timEvent,data)}break;case timapi.constants.RequestType.changeSettings:for(let each of timTerminal._listeners){each.changeSettingsCompleted(timEvent)}break;case timapi.constants.RequestType.closeDialogMode:for(let each of timTerminal._listeners){each.closeDialogModeCompleted(timEvent)}break;case timapi.constants.RequestType.closeMaintenanceWindow:for(let each of timTerminal._listeners){each.closeMaintenanceWindowCompleted(timEvent)}break;case timapi.constants.RequestType.closeReader:for(let each of timTerminal._listeners){each.closeReaderCompleted(timEvent)}break;case timapi.constants.RequestType.commit:if(c_data){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),c_data))}data=timapi._TimApiHelpers.unwrapPrintData(c_data);for(let each of timTerminal._listeners){each.commitCompleted(timEvent,data)}break;case timapi.constants.RequestType.connect:for(let each of timTerminal._listeners){each.connectCompleted(timEvent)}break;case timapi.constants.RequestType.counterRequest:data=timapi._TimApiHelpers.unwrapCounters(c_data);for(let each of timTerminal._listeners){each.counterRequestCompleted(timEvent,data)}break;case timapi.constants.RequestType.dccRates:if(c_data){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),c_data))}data=timapi._TimApiHelpers.unwrapPrintData(c_data);for(let each of timTerminal._listeners){each.dccRatesCompleted(timEvent,data)}break;case timapi.constants.RequestType.deactivate:if(c_data){p=new timapi._TimApiHelpers.TAPointer;h.assert(timapi._TimApiHelpers.prepareDeactivateResponse().get_print_data(c_data,p.p()));if(p.isValid()){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),p.v()))}}data=timapi._TimApiHelpers.unwrapDeactivateResponse(c_data);for(let each of timTerminal._listeners){each.deactivateCompleted(timEvent,data)}break;case timapi.constants.RequestType.ejectCard:for(let each of timTerminal._listeners){each.ejectCardCompleted(timEvent)}break;case timapi.constants.RequestType.finishCheckout:data=timapi._TimApiHelpers.unwrapVasCheckoutInformation(c_data);for(let each of timTerminal._listeners){each.finishCheckoutCompleted(timEvent,data)}break;case timapi.constants.RequestType.hardwareInformation:data=timapi._TimApiHelpers.unwrapHardwareInformationResponse(c_data);for(let each of timTerminal._listeners){each.hardwareInformationCompleted(timEvent,data)}break;case timapi.constants.RequestType.initTransaction:data=timapi._TimApiHelpers.unwrapCardData(c_data);for(let each of timTerminal._listeners){each.initTransactionCompleted(timEvent,data)}break;case timapi.constants.RequestType.login:for(let each of timTerminal._listeners){each.loginCompleted(timEvent)}break;case timapi.constants.RequestType.logout:for(let each of timTerminal._listeners){each.logoutCompleted(timEvent)}break;case timapi.constants.RequestType.loyaltyData:data=timapi._TimApiHelpers.unwrapCardData(c_data);for(let each of timTerminal._listeners){each.loyaltyDataCompleted(timEvent,data)}break;case timapi.constants.RequestType.openDialogMode:for(let each of timTerminal._listeners){each.openDialogModeCompleted(timEvent)}break;case timapi.constants.RequestType.openMaintenanceWindow:for(let each of timTerminal._listeners){each.openMaintenanceWindowCompleted(timEvent)}break;case timapi.constants.RequestType.openReader:for(let each of timTerminal._listeners){each.openReaderCompleted(timEvent)}break;case timapi.constants.RequestType.provideLoyaltyBasket:data=timapi._TimApiHelpers.unwrapLoyaltyItemList(c_data);for(let each of timTerminal._listeners){each.provideLoyaltyBasketCompleted(timEvent,data)}break;case timapi.constants.RequestType.provideVasResult:data=timapi._TimApiHelpers.unwrapVasResult(c_data);for(let each of timTerminal._listeners){each.provideVasResultCompleted(timEvent,data)}break;case timapi.constants.RequestType.reboot:for(let each of timTerminal._listeners){each.rebootCompleted(timEvent)}break;case timapi.constants.RequestType.receiptRequest:if(c_data){p=new timapi._TimApiHelpers.TAPointer;h.assert(timapi._TimApiHelpers.prepareReceiptRequestResponse().get_print_data(c_data,p.p()));if(p.isValid()){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),p.v()))}}data=timapi._TimApiHelpers.unwrapReceiptRequestResponse(c_data);for(let each of timTerminal._listeners){each.receiptRequestCompleted(timEvent,data)}break;case timapi.constants.RequestType.reconciliation:if(c_data){p=new timapi._TimApiHelpers.TAPointer;h.assert(timapi._TimApiHelpers.prepareReconciliationResponse().get_print_data(c_data,p.p()));if(p.isValid()){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),p.v()))}}data=timapi._TimApiHelpers.unwrapReconciliationResponse(c_data);for(let each of timTerminal._listeners){each.reconciliationCompleted(timEvent,data)}break;case timapi.constants.RequestType.reconfig:if(c_data){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),c_data))}data=timapi._TimApiHelpers.unwrapPrintData(c_data);for(let each of timTerminal._listeners){each.reconfigCompleted(timEvent,data)}break;case timapi.constants.RequestType.rollback:if(c_data){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),c_data))}data=timapi._TimApiHelpers.unwrapPrintData(c_data);for(let each of timTerminal._listeners){each.rollbackCompleted(timEvent,data)}break;case timapi.constants.RequestType.sendCardCommand:data=timapi._TimApiHelpers.unwrapCommandResponseList(c_data);for(let each of timTerminal._listeners){each.sendCardCommandCompleted(timEvent,data)}break;case timapi.constants.RequestType.showDialog:data=timapi._TimApiHelpers.unwrapShowDialogResponse(c_data);for(let each of timTerminal._listeners){each.showDialogCompleted(timEvent,data)}break;case timapi.constants.RequestType.showSignatureCapture:data=timapi._TimApiHelpers.unwrapShowSignatureCaptureResponse(c_data);for(let each of timTerminal._listeners){each.showSignatureCaptureCompleted(timEvent,data)}break;case timapi.constants.RequestType.printOnTerminal:for(let each of timTerminal._listeners){each.printOnTerminalCompleted(timEvent)}break;case timapi.constants.RequestType.softwareUpdate:let value=timapi._TimApiHelpers.unwrapInteger(c_data);data=timapi._TimApiHelpers.enumByValue(timapi.constants.UpdateStatus,value);for(let each of timTerminal._listeners){each.softwareUpdateCompleted(timEvent,data)}break;case timapi.constants.RequestType.startCheckout:for(let each of timTerminal._listeners){each.startCheckoutCompleted(timEvent)}break;case timapi.constants.RequestType.systemInformation:data=timapi._TimApiHelpers.unwrapSystemInformationResponse(c_data);for(let each of timTerminal._listeners){each.systemInformationCompleted(timEvent,data)}break;case timapi.constants.RequestType.transaction:if(c_data){p=new timapi._TimApiHelpers.TAPointer;h.assert(timapi._TimApiHelpers.prepareTransactionResponse().get_print_data(c_data,p.p()));if(p.isValid()){h.assert(timapi._TimApiHelpers.prepareReceiptFormatter().process_print_receipts(timTerminal._wao.v(),p.v()))}}data=timapi._TimApiHelpers.unwrapTransactionResponse(c_data);for(let each of timTerminal._listeners){each.transactionCompleted(timEvent,data)}break;default:for(let each of timTerminal._listeners){each.requestCompleted(timEvent,data)}break}}finally{if(p)p.dispose();timTerminal._listenersLocked=false}},vasInfo:function(terminal,vas_checkout_information){let timTerminal=timapi._TimApiHelpers.terminalMap[Number(terminal)];let vasCheckoutInformation=timapi._TimApiHelpers.unwrapVasCheckoutInformation(vas_checkout_information);timTerminal._listeners.forEach(function(each){each.vasInfo(timTerminal,vasCheckoutInformation)})},deferredAuth:function(terminal,response){let timTerminal=timapi._TimApiHelpers.terminalMap[Number(terminal)];let trxResponse=timapi._TimApiHelpers.unwrapTransactionResponse(response);timTerminal._listeners.forEach(function(each){each.deferredAuth(timTerminal,trxResponse)})},screenshot:function(terminal,info){let timTerminal=timapi._TimApiHelpers.terminalMap[Number(terminal)];let timInfo=timapi._TimApiHelpers.unwrapScreenshotInformation(info);timTerminal._listeners.forEach(function(each){each.screenshot(timTerminal,timInfo)})},licenseChanged:function(terminal){let timTerminal=timapi._TimApiHelpers.terminalMap[Number(terminal)];timTerminal._listeners.forEach(function(each){each.licenseChanged(timTerminal)})},errorNotification:function(terminal,timError){let timTerminal=timapi._TimApiHelpers.terminalMap[Number(terminal)];timError=timapi._TimApiHelpers.unwrapTimError(timError);timTerminal._listeners.forEach(function(each){each.errorNotification(timTerminal,timError)})},publishLogRecord:function(record,userPointer){if(timapi._TimApiHelpers.__six_internal_test&&Module.getValue(record,"i32")>timapi._TimApiHelpers.__six_internal_test){return}let timRecord=new timapi.LogRecord;timRecord.level=timapi._TimApiHelpers.enumByValue(timapi.LogRecord.LogLevel,Module.getValue(record,"i32"));timRecord.thread=Module.UTF8ToString(Module.getValue(record+4,"i32"));timRecord.file=Module.UTF8ToString(Module.getValue(record+8,"i32"));timRecord.method=Module.UTF8ToString(Module.getValue(record+12,"i32"));timRecord.line=Module.getValue(record+16,"i32");timRecord.message=Module.UTF8ToString(Module.getValue(record+20,"i32"));let parameters=Module.getValue(record+24,"i32");let parameterCount=Module.getValue(record+28,"i32");let i;for(i=0;i<parameterCount;i++){timRecord.parameters.push(Module.UTF8ToString(Module.getValue(parameters+4*i,"i32")))}let stackTrace=Module.getValue(record+32,"i32");let stackTraceCount=Module.getValue(record+36,"i32");for(i=0;i<stackTraceCount;i++){let base=stackTrace+12*i;let timStackTrace=new timapi.LogRecordTrace;timStackTrace.file=Module.UTF8ToString(Module.getValue(base,"i32"));timStackTrace.method=Module.UTF8ToString(Module.getValue(base+4,"i32"));timStackTrace.line=Module.getValue(base+8,"i32");timRecord.stackTrace.push(Object.freeze(timStackTrace))}timRecord.timestamp=Object.freeze(new Date(1e3*Module.getValue(record+40,"i32")));Object.freeze(timRecord);if(onTimApiPublishLogRecord){onTimApiPublishLogRecord(timRecord)}else{timapi.log(String(timRecord))}},internalError(err,file,method){try{let timRecord=new timapi.LogRecord;timRecord.level=timapi.LogRecord.LogLevel.severe;timRecord.file=file;timRecord.method=method;if(err.hasOwnProperty("stack")){timRecord.message=err.stack}else{timRecord.message=String(err)}if(onTimApiPublishLogRecord){onTimApiPublishLogRecord(timRecord)}else{timapi.log(String(timRecord))}}catch(err){timapi.log("SEVERE internal error! "+String(err))}},activateResponse:undefined,prepareActivateResponse:function(){let f=timapi._TimApiHelpers.activateResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.activateResponse={get_print_data:g("ta_activate_response_get_print_data",["number"]),get_act_seq_counter:g("ta_activate_response_get_act_seq_counter",["number"])}}return f},unwrapActivateResponse:function(c_activate_response){if(c_activate_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareActivateResponse();let activateResponse=new timapi.ActivateResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_print_data(c_activate_response,p.p()));activateResponse.printData=timapi._TimApiHelpers.unwrapPrintData(p.v());h.assert(f.get_act_seq_counter(c_activate_response,p.p()));activateResponse.actSeqCounter=p.v()}finally{if(p)p.dispose()}return Object.freeze(activateResponse)},amount:undefined,prepareAmount:function(){let f=timapi._TimApiHelpers.amount;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.amount={create:g("TAWAAmountCreate",["number","number","number","number","number"]),getValue:g("TAWAAmountGetValue",["number","number","number"]),get_currency:g("ta_amount_get_currency",["number"]),get_exponent:g("ta_amount_get_exponent",["number"])}}return f},convertAmount:function(amount){if(amount===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareAmount();let wa_amount=undefined;try{wa_amount=new timapi._TimApiHelpers.TAObject;split=timapi._TimApiHelpers.convert64BitTo32Bit(amount.value);h.assert(f.create(wa_amount.p(),split.low,split.high,split.sign,amount.currency._wav+1,amount.exponent))}catch(err){if(wa_amount)wa_amount.dispose();throw err}return wa_amount},unwrapAmount:function(c_amount){if(c_amount===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareAmount();let amount=new timapi.Amount;let p,p2,p3;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;p3=new timapi._TimApiHelpers.TAPointer;h.assert(f.getValue(c_amount,p.p(),p2.p(),p3.p()));let low=p.v();let high=p2.v();let sign=p3.v();amount.value=low+high*2147483648;if(sign===1){amount.value=-amount.value}h.assert(f.get_currency(c_amount,p.p()));amount.currency=h.cbv(timapi.constants.Currency,p.v()-1);h.assert(f.get_exponent(c_amount,p.p()));amount.exponent=p.v()}finally{if(p)p.dispose();if(p2)p2.dispose();if(p3)p3.dispose()}return amount},amountDcc:undefined,prepareAmountDcc:function(){let f=timapi._TimApiHelpers.amountDcc;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.amountDcc={getValue:g("TAWAAmountDccGetValue",["number","number","number"]),get_currency:g("ta_amount_dcc_get_currency",["number"]),get_exponent:g("ta_amount_dcc_get_exponent",["number"]),get_rate:g("ta_amount_dcc_get_rate",["number"]),get_rate_exponent:g("ta_amount_dcc_get_rate_exponent",["number"]),get_markup:g("ta_amount_dcc_get_markup",["number"]),get_markup_exponent:g("ta_amount_dcc_get_markup_exponent",["number"]),get_rate_regulated:g("ta_amount_dcc_get_rate_regulated",["number"]),get_rate_exponent_regulated:g("ta_amount_dcc_get_rate_exponent_regulated",["number"]),get_markup_regulated:g("ta_amount_dcc_get_markup_regulated",["number"]),get_markup_exponent_regulated:g("ta_amount_dcc_get_markup_exponent_regulated",["number"])}}return f},unwrapAmountDcc:function(c_amount_dcc){if(c_amount_dcc===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareAmountDcc();let amountDcc=new timapi.AmountDcc;let p,p2,p3;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;p3=new timapi._TimApiHelpers.TAPointer;h.assert(f.getValue(c_amount_dcc,p.p(),p2.p(),p3.p()));let low=p.v();let high=p2.v();let sign=p3.v();amountDcc.value=low+high*2147483648;if(sign===1){amountDcc.value=-amountDcc.value}h.assert(f.get_currency(c_amount_dcc,p.p()));amountDcc.currency=h.cbv(timapi.constants.Currency,p.v()-1);h.assert(f.get_exponent(c_amount_dcc,p.p()));amountDcc.exponent=p.v();h.assert(f.get_rate(c_amount_dcc,p.p()));amountDcc.rate=p.v();h.assert(f.get_rate_exponent(c_amount_dcc,p.p()));amountDcc.rateExponent=p.v();h.assert(f.get_markup(c_amount_dcc,p.p()));amountDcc.markup=p.v();h.assert(f.get_markup_exponent(c_amount_dcc,p.p()));amountDcc.markupExponent=p.v();h.assert(f.get_rate_regulated(c_amount_dcc,p.p()));amountDcc.rateRegulated=p.v();h.assert(f.get_rate_exponent_regulated(c_amount_dcc,p.p()));amountDcc.rateExponentRegulated=p.v();h.assert(f.get_markup_regulated(c_amount_dcc,p.p()));amountDcc.markupRegulated=p.v();h.assert(f.get_markup_exponent_regulated(c_amount_dcc,p.p()));amountDcc.markupExponentRegulated=p.v()}finally{if(p)p.dispose()}return Object.freeze(amountDcc)},amountDiscount:undefined,prepareAmountDiscount:function(){let f=timapi._TimApiHelpers.amountDiscount;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.amountDiscount={create:g("TAWAAmountDiscountCreate",["number","number","number","number","number","number"]),getValue:g("TAWAAmountDiscountGetValue",["number","number","number"]),get_currency:g("ta_amount_discount_get_currency",["number"]),get_exponent:g("ta_amount_discount_get_exponent",["number"]),get_discount_id:g("ta_amount_discount_get_discount_id",["number"])}}return f},convertAmountDiscount:function(amountDiscount){if(amountDiscount===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareAmountDiscount();let wa_amount_discount,s;try{wa_amount_discount=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;split=timapi._TimApiHelpers.convert64BitTo32Bit(amountDiscount.value);h.assert(f.create(wa_amount_discount.p(),split.low,split.high,split.sign,amountDiscount.currency._wav+1,amountDiscount.exponent,s.replaceOrUndef(amountDiscount.discountId)))}catch(err){if(wa_amount_discount)wa_amount_discount.dispose();throw err}finally{if(s)s.dispose()}return wa_amount_discount},unwrapAmountDiscount:function(c_amount_discount){if(c_amount_discount===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareAmountDiscount();let amountDiscount=new timapi.AmountDiscount;let p,p2,p3;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;p3=new timapi._TimApiHelpers.TAPointer;h.assert(f.getValue(c_amount_discount,p.p(),p2.p(),p3.p()));let low=p.v();let high=p2.v();let sign=p3.v();amountDiscount.value=low+high*2147483648;if(sign===1){amountDiscount.value=-amountDiscount.value}h.assert(f.get_currency(c_amount_discount,p.p()));amountDiscount.currency=h.cbv(timapi.constants.Currency,p.v()-1);h.assert(f.get_exponent(c_amount_discount,p.p()));amountDiscount.exponent=p.v();h.assert(f.get_discount_id(c_amount_discount,p.p()));amountDiscount.discountId=h.contentOfIfValid(p)}finally{if(p)p.dispose();if(p2)p2.dispose();if(p3)p3.dispose()}return amountDiscount},amountFinal:undefined,prepareAmountFinal:function(){let f=timapi._TimApiHelpers.amountFinal;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.amountFinal={getValue:g("TAWAAmountFinalGetValue",["number","number","number"]),get_currency:g("ta_amount_final_get_currency",["number"]),get_exponent:g("ta_amount_final_get_exponent",["number"]),get_adjustment_result:g("ta_amount_final_get_adjustment_result",["number"])}}return f},unwrapAmountFinal:function(c_amount_final){if(c_amount_final===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareAmountFinal();let amountFinal=new timapi.AmountFinal;let p,p2,p3;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;p3=new timapi._TimApiHelpers.TAPointer;h.assert(f.getValue(c_amount_final,p.p(),p2.p(),p3.p()));let low=p.v();let high=p2.v();let sign=p3.v();amountFinal.value=low+high*2147483648;if(sign===1){amountFinal.value=-amountFinal.value}h.assert(f.get_currency(c_amount_final,p.p()));amountFinal.currency=h.cbv(timapi.constants.Currency,p.v()-1);h.assert(f.get_exponent(c_amount_final,p.p()));amountFinal.exponent=p.v();h.assert(f.get_adjustment_result(c_amount_final,p.p()));amountFinal.adjustmentResult=h.ebv(timapi.constants.AdjustmentResult,p.v())}finally{if(p)p.dispose();if(p2)p2.dispose();if(p3)p3.dispose()}return amountFinal},application:undefined,prepareApplication:function(){let f=timapi._TimApiHelpers.application;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.application={get_aid:g("ta_application_get_aid",["number"]),get_label:g("ta_application_get_label",["number"])}}return f},unwrapApplication:function(c_application){if(c_application===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareApplication();let application=new timapi.Application;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_aid(c_application,p.p()));application.aid=h.contentOfIfValid(p);h.assert(f.get_label(c_application,p.p()));application.label=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return Object.freeze(application)},balanceInquiryResponse:undefined,prepareBalanceInquiryResponse:function(){let f=timapi._TimApiHelpers.balanceInquiryResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.balanceInquiryResponse={get_amount:g("ta_balance_inquiry_response_get_amount",["number"]),get_print_data:g("ta_balance_inquiry_response_get_print_data",["number"]),get_card_data:g("ta_balance_inquiry_response_get_card_data",["number"]),get_disclaimer:g("ta_balance_inquiry_response_get_disclaimer",["number"]),get_transaction_information:g("ta_balance_inquiry_response_get_transaction_information",["number"])}}return f},unwrapBalanceInquiryResponse:function(c_balance_inquiry_response){if(c_balance_inquiry_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareBalanceInquiryResponse();let balanceInquiryResponse=new timapi.BalanceInquiryResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_amount(c_balance_inquiry_response,p.p()));balanceInquiryResponse.amount=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_print_data(c_balance_inquiry_response,p.p()));balanceInquiryResponse.printData=timapi._TimApiHelpers.unwrapPrintData(p.v());h.assert(f.get_card_data(c_balance_inquiry_response,p.p()));balanceInquiryResponse.cardData=timapi._TimApiHelpers.unwrapCardData(p.v());h.assert(f.get_disclaimer(c_balance_inquiry_response,p.p()));balanceInquiryResponse.disclaimer=h.contentOfIfValid(p);h.assert(f.get_transaction_information(c_balance_inquiry_response,p.p()));balanceInquiryResponse.transactionInformation=timapi._TimApiHelpers.unwrapTransactionInformation(p.v())}finally{if(p)p.dispose()}return balanceInquiryResponse},balanceResponse:undefined,prepareBalanceResponse:function(){let f=timapi._TimApiHelpers.balanceResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.balanceResponse={get_print_data:g("ta_balance_response_get_print_data",["number"]),get_counters:g("ta_balance_response_get_counters",["number"])}}return f},unwrapBalanceResponse:function(c_balance_response){if(c_balance_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareBalanceResponse();let balanceResponse=new timapi.BalanceResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_print_data(c_balance_response,p.p()));balanceResponse.printData=timapi._TimApiHelpers.unwrapPrintData(p.v());h.assert(f.get_counters(c_balance_response,p.p()));balanceResponse.counters=timapi._TimApiHelpers.unwrapCounters(p.v())}finally{if(p)p.dispose()}return Object.freeze(balanceResponse)},basket:undefined,prepareBasket:function(){let f=timapi._TimApiHelpers.basket;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.basket={create:g("ta_basket_create",[]),get_items:g("ta_basket_get_items",["number"]),set_items:g("ta_basket_set_items",["number"]),get_loyalty_auth_result:g("ta_basket_get_loyalty_auth_result",["number"]),set_loyalty_auth_result:g("ta_basket_set_loyalty_auth_result",["number"])}}return f},convertBasket:function(basket){if(basket===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareBasket();let f_list=timapi._TimApiHelpers.prepareList();let wa_basket,wa_list,wa_obj,i;try{wa_basket=new timapi._TimApiHelpers.TAObject;wa_list=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;i=new timapi._TimApiHelpers.TAInteger;h.assert(f.create(wa_basket.p()));h.assert(f_list.create(wa_list.p()));for(let basketItem of basket.items){wa_obj.takeover(timapi._TimApiHelpers.convertBasketItem(basketItem));h.assert(f_list.add(wa_list.v(),wa_obj.v()))}h.assert(f.set_items(wa_basket.v(),wa_list.v()));h.assert(f.set_loyalty_auth_result(wa_basket.v(),i.replaceOrUndef(basket.loyaltyAuthResult)))}catch(err){if(wa_basket)wa_basket.dispose();throw err}finally{if(wa_list)wa_list.dispose();if(wa_obj)wa_obj.dispose();if(i)i.dispose()}return wa_basket},unwrapBasket:function(c_basket){if(c_basket===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareBasket();let basket=new timapi.Basket;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_items(c_basket,p.p()));let items=[];h.lit(p.v(),function(c_basket_item){items.push(timapi._TimApiHelpers.unwrapBasketItem(c_basket_item))});basket.items=items;h.assert(f.get_loyalty_auth_result(c_basket,p.p()));basket.loyaltyAuthResult=timapi._TimApiHelpers.unwrapInteger(p.v())}finally{if(p)p.dispose()}return basket},basketItem:undefined,prepareBasketItem:function(){let f=timapi._TimApiHelpers.basketItem;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.basketItem={create:g("ta_basket_item_create",[]),get_auth_result:g("ta_basket_item_get_auth_result",["number"]),set_auth_result:g("ta_basket_item_set_auth_result",["number"]),get_item_id:g("ta_basket_item_get_item_id",["number"]),set_item_id:g("ta_basket_item_set_item_id",["number"]),get_loyalty_id:g("ta_basket_item_get_loyalty_id",["number"]),set_loyalty_id:g("ta_basket_item_set_loyalty_id",["number"]),get_amount:g("ta_basket_item_get_amount",["number"]),set_amount:g("ta_basket_item_set_amount",["number"]),get_amount_total:g("ta_basket_item_get_amount_total",["number"]),set_amount_total:g("ta_basket_item_set_amount_total",["number"]),get_amount_discount:g("ta_basket_item_get_amount_discount",["number"]),set_amount_discount:g("ta_basket_item_set_amount_discount",["number"]),get_amount_tax:g("ta_basket_item_get_amount_tax",["number"]),set_amount_tax:g("ta_basket_item_set_amount_tax",["number"]),get_amount_gross:g("ta_basket_item_get_amount_gross",["number"]),set_amount_gross:g("ta_basket_item_set_amount_gross",["number"]),get_unit_amount_discount:g("ta_basket_item_get_unit_amount_discount",["number"]),set_unit_amount_discount:g("ta_basket_item_set_unit_amount_discount",["number"]),get_unit_amount_gross:g("ta_basket_item_get_unit_amount_gross",["number"]),set_unit_amount_gross:g("ta_basket_item_set_unit_amount_gross",["number"]),get_item_quantity:g("ta_basket_item_get_item_quantity",["number"]),set_item_quantity:g("ta_basket_item_set_item_quantity",["number"]),get_prod_description:g("ta_basket_item_get_prod_description",["number"]),set_prod_description:g("ta_basket_item_set_prod_description",["number"])}}return f},convertBasketItem:function(basketItem){if(basketItem===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareBasketItem();let wa_basket_item,wa_obj,s,i;try{wa_basket_item=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;i=new timapi._TimApiHelpers.TAInteger;h.assert(f.create(wa_basket_item.p()));h.assert(f.set_auth_result(wa_basket_item.v(),i.replaceOrUndef(basketItem.authResult)));h.assert(f.set_item_id(wa_basket_item.v(),s.replaceOrUndef(basketItem.itemId)));h.assert(f.set_loyalty_id(wa_basket_item.v(),s.replaceOrUndef(basketItem.loyaltyId)));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(basketItem.amount));h.assert(f.set_amount(wa_basket_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(basketItem.amountTotal));h.assert(f.set_amount_total(wa_basket_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmountDiscount(basketItem.amountDiscount));h.assert(f.set_amount_discount(wa_basket_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(basketItem.amountTax));h.assert(f.set_amount_tax(wa_basket_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(basketItem.amountGross));h.assert(f.set_amount_gross(wa_basket_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(basketItem.unitAmountDiscount));h.assert(f.set_unit_amount_discount(wa_basket_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(basketItem.unitAmountGross));h.assert(f.set_unit_amount_gross(wa_basket_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertItemQuantity(basketItem.itemQuantity));h.assert(f.set_item_quantity(wa_basket_item.v(),wa_obj.v()));h.assert(f.set_prod_description(wa_basket_item.v(),s.replaceOrUndef(basketItem.prodDescription)))}catch(err){if(wa_basket_item)wa_basket_item.dispose();throw err}finally{if(wa_obj)wa_obj.dispose();if(s)s.dispose();if(i)i.dispose()}return wa_basket_item},unwrapBasketItem:function(c_basket_item){if(c_basket_item===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareBasketItem();let basketItem=new timapi.BasketItem;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_auth_result(c_basket_item,p.p()));basketItem.authResult=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_item_id(c_basket_item,p.p()));basketItem.itemId=h.contentOfIfValid(p);h.assert(f.get_loyalty_id(c_basket_item,p.p()));basketItem.loyaltyId=h.contentOfIfValid(p);h.assert(f.get_amount(c_basket_item,p.p()));basketItem.amount=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_total(c_basket_item,p.p()));basketItem.amountTotal=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_discount(c_basket_item,p.p()));basketItem.amountDiscount=timapi._TimApiHelpers.unwrapAmountDiscount(p.v());h.assert(f.get_amount_tax(c_basket_item,p.p()));basketItem.amountTax=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_gross(c_basket_item,p.p()));basketItem.amountGross=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_unit_amount_discount(c_basket_item,p.p()));basketItem.unitAmountDiscount=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_unit_amount_gross(c_basket_item,p.p()));basketItem.unitAmountGross=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_item_quantity(c_basket_item,p.p()));basketItem.itemQuantity=timapi._TimApiHelpers.unwrapItemQuantity(p.v());h.assert(f.get_prod_description(c_basket_item,p.p()));basketItem.prodDescription=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return basketItem},brand:undefined,prepareBrand:function(){let f=timapi._TimApiHelpers.brand;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.brand={get_name:g("ta_brand_get_name",["number"]),get_dcc_available:g("ta_brand_get_dcc_available",["number"]),get_payment_protocol:g("ta_brand_get_payment_protocol",["number"]),get_acq_id:g("ta_brand_get_acq_id",["number"]),get_last_init_date:g("ta_brand_get_last_init_date",["number"]),get_applications:g("ta_brand_get_applications",["number"]),get_currencies:g("ta_brand_get_currencies",["number"])}}return f},unwrapBrand:function(c_brand){if(c_brand===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareBrand();let brand=new timapi.Brand;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_name(c_brand,p.p()));brand.name=h.contentOfIfValid(p);h.assert(f.get_dcc_available(c_brand,p.p()));brand.dccAvailable=h.bc_c_t_js(p.v());h.assert(f.get_payment_protocol(c_brand,p.p()));brand.paymentProtocol=h.ebv(timapi.constants.PaymentProtocol,p.v());h.assert(f.get_acq_id(c_brand,p.p()));brand.acqId=p.v();h.assert(f.get_last_init_date(c_brand,p.p()));brand.lastInitDate=timapi._TimApiHelpers.unwrapTimeDate(p.v());h.assert(f.get_applications(c_brand,p.p()));let applications=[];h.lit(p.v(),function(c_application){applications.push(timapi._TimApiHelpers.unwrapApplication(c_application))});brand.applications=applications;h.assert(f.get_currencies(c_brand,p.p()));let currencies=[];h.lit(p.v(),function(c_currency_item){currencies.push(timapi._TimApiHelpers.unwrapCurrencyItem(c_currency_item))});brand.currencies=currencies}finally{if(p)p.dispose()}return Object.freeze(brand)},cardData:undefined,prepareCardData:function(){let f=timapi._TimApiHelpers.cardData;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.cardData={get_pos_entry_mode:g("ta_card_data_get_pos_entry_mode",["number"]),get_aid:g("ta_card_data_get_aid",["number"]),get_acc:g("ta_card_data_get_acc",["number"]),get_card_number:g("ta_card_data_get_card_number",["number"]),get_card_number_printable:g("ta_card_data_get_card_number_printable",["number"]),get_card_number_printable_cardholder:g("ta_card_data_get_card_number_printable_cardholder",["number"]),get_card_number_enc:g("ta_card_data_get_card_number_enc",["number"]),get_card_number_enc_key_index:g("ta_card_data_get_card_number_enc_key_index",["number"]),get_card_expiry_date:g("ta_card_data_get_card_expiry_date",["number"]),get_brand_name:g("ta_card_data_get_brand_name",["number"]),get_tender_name:g("ta_card_data_get_tender_name",["number"]),get_card_track_datas:g("ta_card_data_get_card_track_datas",["number"]),get_loyalty_information:g("ta_card_data_get_loyalty_information",["number"]),get_card_ref:g("ta_card_data_get_card_ref",["number"]),get_processing_disposition:g("ta_card_data_get_processing_disposition",["number"]),get_language:g("ta_card_data_get_language",["number"]),get_card_country_code:g("ta_card_data_get_card_country_code",["number"]),get_terminal_country_code:g("ta_card_data_get_terminal_country_code",["number"]),get_uid:g("ta_card_data_get_uid",["number"]),get_asrpd:g("ta_card_data_get_asrpd",["number"]),get_card_product_type:g("ta_card_data_get_card_product_type",["number"]),get_card_type:g("ta_card_data_get_card_type",["number"]),get_cardholder:g("ta_card_data_get_cardholder",["number"])}}return f},unwrapCardData:function(c_card_data){if(c_card_data===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareCardData();let cardData=new timapi.CardData;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_pos_entry_mode(c_card_data,p.p()));cardData.posEntryMode=h.ebv(timapi.constants.PosEntryMode,p.v());h.assert(f.get_aid(c_card_data,p.p()));cardData.aid=h.contentOfIfValid(p);h.assert(f.get_acc(c_card_data,p.p()));cardData.acc=h.contentOfIfValid(p);h.assert(f.get_card_number(c_card_data,p.p()));cardData.cardNumber=h.contentOfIfValid(p);h.assert(f.get_card_number_printable(c_card_data,p.p()));cardData.cardNumberPrintable=h.contentOfIfValid(p);h.assert(f.get_card_number_printable_cardholder(c_card_data,p.p()));cardData.cardNumberPrintableCardholder=h.contentOfIfValid(p);h.assert(f.get_card_number_enc(c_card_data,p.p()));cardData.cardNumberEnc=timapi._TimApiHelpers.getBinaryContent(p);h.assert(f.get_card_number_enc_key_index(c_card_data,p.p()));cardData.cardNumberEncKeyIndex=p.v();h.assert(f.get_card_expiry_date(c_card_data,p.p()));cardData.cardExpiryDate=timapi._TimApiHelpers.unwrapTimeDate(p.v());h.assert(f.get_brand_name(c_card_data,p.p()));cardData.brandName=h.contentOfIfValid(p);h.assert(f.get_tender_name(c_card_data,p.p()));cardData.tenderName=h.contentOfIfValid(p);h.assert(f.get_card_track_datas(c_card_data,p.p()));let cardTrackDatas=[];h.lit(p.v(),function(c_card_track_data){cardTrackDatas.push(timapi._TimApiHelpers.unwrapCardTrackData(c_card_track_data))});cardData.cardTrackDatas=cardTrackDatas;h.assert(f.get_loyalty_information(c_card_data,p.p()));cardData.loyaltyInformation=timapi._TimApiHelpers.unwrapSBMap(p.v());h.assert(f.get_card_ref(c_card_data,p.p()));cardData.cardRef=h.contentOfIfValid(p);h.assert(f.get_processing_disposition(c_card_data,p.p()));cardData.processingDisposition=h.ebv(timapi.constants.ProcessingDisposition,p.v());h.assert(f.get_language(c_card_data,p.p()));cardData.language=h.contentOfIfValid(p);h.assert(f.get_card_country_code(c_card_data,p.p()));cardData.cardCountryCode=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_terminal_country_code(c_card_data,p.p()));cardData.terminalCountryCode=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_uid(c_card_data,p.p()));cardData.uid=h.contentOfIfValid(p);h.assert(f.get_asrpd(c_card_data,p.p()));cardData.asrpd=timapi._TimApiHelpers.getBinaryContent(p);h.assert(f.get_card_product_type(c_card_data,p.p()));cardData.cardProductType=h.ebv(timapi.constants.CardProductType,p.v());h.assert(f.get_card_type(c_card_data,p.p()));cardData.cardType=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_cardholder(c_card_data,p.p()));cardData.cardholder=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return Object.freeze(cardData)},cardTrackData:undefined,prepareCardTrackData:function(){let f=timapi._TimApiHelpers.cardTrackData;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.cardTrackData={get_track_number:g("ta_card_track_data_get_track_number",["number"]),get_data:g("ta_card_track_data_get_data",["number"])}}return f},unwrapCardTrackData:function(c_card_track_data){if(c_card_track_data===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareCardTrackData();let cardTrackData=new timapi.CardTrackData;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_track_number(c_card_track_data,p.p()));cardTrackData.trackNum=p.v();h.assert(f.get_data(c_card_track_data,p.p()));cardTrackData.data=timapi._TimApiHelpers.getBinaryContent(p)}finally{if(p)p.dispose()}return Object.freeze(cardTrackData)},commandRequest:undefined,prepareCommandRequest:function(){let f=timapi._TimApiHelpers.commandRequest;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.commandRequest={create:g("ta_command_request_create",[]),set_card_reader:g("ta_command_request_set_card_reader",["number"]),set_order:g("ta_command_request_set_order",["number"]),set_card_command:g("ta_command_request_set_card_command",["number"]),set_positive_resource:g("ta_command_request_set_positive_resource",["number"]),set_negative_resource:g("ta_command_request_set_negative_resource",["number"]),set_execution_resource:g("ta_command_request_set_execution_resource",["number"]),set_pre_resource:g("ta_command_request_set_pre_resource",["number"]),set_positive_answers:g("ta_command_request_set_positive_answers",["number"])}}return f},convertCommandRequest:function(commandRequest){if(commandRequest===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareCommandRequest();let f_list=timapi._TimApiHelpers.prepareList();let wa_command_request,wa_list,s;try{wa_command_request=new timapi._TimApiHelpers.TAObject;wa_list=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_command_request.p()));h.assert(f.set_card_reader(wa_command_request.v(),commandRequest.cardReader._value));h.assert(f.set_order(wa_command_request.v(),commandRequest.order));h.assert(f.set_card_command(wa_command_request.v(),s.replaceOrUndef(commandRequest.cardCommand)));h.assert(f.set_positive_resource(wa_command_request.v(),commandRequest.positiveResource._value));h.assert(f.set_negative_resource(wa_command_request.v(),commandRequest.negativeResource._value));h.assert(f.set_execution_resource(wa_command_request.v(),commandRequest.executionResource._value));h.assert(f.set_pre_resource(wa_command_request.v(),commandRequest.preResource._value));h.assert(f_list.create(wa_list.p()));for(let positiveAnswer of commandRequest.positiveAnswers){h.assert(f_list.add(wa_list.v(),s.replaceOrUndef(positiveAnswer)))}h.assert(f.set_positive_answers(wa_command_request.v(),wa_list.v()))}catch(err){if(wa_command_request)wa_command_request.dispose();throw err}finally{if(s)s.dispose();if(wa_list)wa_list.dispose()}return wa_command_request},commandResponse:undefined,prepareCommandResponse:function(){let f=timapi._TimApiHelpers.commandResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.commandResponse={get_order:g("ta_command_response_get_order",["number"]),get_response_type:g("ta_command_response_get_response_type",["number"]),get_card_response:g("ta_command_response_get_card_response",["number"]),get_uid:g("ta_command_response_get_uid",["number"]),get_atr:g("ta_command_response_get_atr",["number"])}}return f},unwrapCommandResponse:function(c_command_response){if(c_command_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareCommandResponse();let commandResponse=new timapi.CommandResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_order(c_command_response,p.p()));commandResponse.order=p.v();h.assert(f.get_response_type(c_command_response,p.p()));commandResponse.responseType=h.ebv(timapi.constants.ResponseType,p.v());h.assert(f.get_card_response(c_command_response,p.p()));commandResponse.cardResponse=h.contentOfIfValid(p);h.assert(f.get_uid(c_command_response,p.p()));commandResponse.uid=h.contentOfIfValid(p);h.assert(f.get_atr(c_command_response,p.p()));commandResponse.atr=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return commandResponse},unwrapCommandResponseList:function(c_command_response_list){if(c_command_response_list===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let list=[];try{h.lit(c_command_response_list,function(c_command_response){list.push(timapi._TimApiHelpers.unwrapCommandResponse(c_command_response))})}finally{}return list},configData:undefined,prepareConfigData:function(){let f=timapi._TimApiHelpers.configData;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.configData={get_receipt_header:g("ta_config_data_get_receipt_header",["number"]),get_language:g("ta_config_data_get_language",["number"])}}return f},unwrapConfigData:function(c_config_data){if(c_config_data===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareConfigData();let configData=new timapi.ConfigData;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_receipt_header(c_config_data,p.p()));let receiptHeader=[];h.lit(p.v(),function(c_receipt_line){receiptHeader.push(timapi._TimApiHelpers.TAString.unwrapString(c_receipt_line))});configData.receiptHeader=receiptHeader;h.assert(f.get_language(c_config_data,p.p()));configData.language=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return Object.freeze(configData)},counter:undefined,prepareCounter:function(){let f=timapi._TimApiHelpers.counter;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.counter={get_brand_name:g("ta_counter_get_brand_name",["number"]),get_payment_protocol:g("ta_counter_get_payment_protocol",["number"]),get_acq_id:g("ta_counter_get_acq_id",["number"]),get_count:g("ta_counter_get_count",["number"]),get_count_dcc:g("ta_counter_get_count_dcc",["number"]),get_count_foreign:g("ta_counter_get_count_foreign",["number"]),get_totals:g("ta_counter_get_totals",["number"])}}return f},unwrapCounter:function(c_counter){if(c_counter===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareCounter();let counter=new timapi.Counter;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_brand_name(c_counter,p.p()));counter.brandName=h.contentOfIfValid(p);h.assert(f.get_payment_protocol(c_counter,p.p()));counter.paymentProtocol=h.ebv(timapi.constants.PaymentProtocol,p.v());h.assert(f.get_acq_id(c_counter,p.p()));counter.acqId=p.v();h.assert(f.get_count(c_counter,p.p()));counter.count=p.v();h.assert(f.get_count_dcc(c_counter,p.p()));counter.countDcc=p.v();h.assert(f.get_count_foreign(c_counter,p.p()));counter.countForeign=p.v();h.assert(f.get_totals(c_counter,p.p()));let totals=[];h.lit(p.v(),function(c_total){totals.push(timapi._TimApiHelpers.unwrapTotal(c_total))});counter.totals=totals}finally{if(p)p.dispose()}return Object.freeze(counter)},counters:undefined,prepareCounters:function(){let f=timapi._TimApiHelpers.counters;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.counters={get_counter_type:g("ta_counters_get_counter_type",["number"]),get_seq_counter:g("ta_counters_get_seq_counter",["number"]),get_counters:g("ta_counters_get_counters",["number"])}}return f},unwrapCounters:function(c_counters){if(c_counters===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareCounters();let counters=new timapi.Counters;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_counter_type(c_counters,p.p()));counters.counterType=h.ebv(timapi.constants.CounterType,p.v());h.assert(f.get_seq_counter(c_counters,p.p()));counters.seqCounter=p.v();h.assert(f.get_counters(c_counters,p.p()));let counterList=[];h.lit(p.v(),function(c_counters){counterList.push(timapi._TimApiHelpers.unwrapCounter(c_counters))});counters.counters=counterList}finally{if(p)p.dispose()}return Object.freeze(counters)},currencyItem:undefined,prepareCurrencyItem:function(){let f=timapi._TimApiHelpers.currencyItem;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.currencyItem={get_currency:g("ta_currency_item_get_currency",["number"]),get_currency_type:g("ta_currency_item_get_currency_type",["number"])}}return f},unwrapCurrencyItem:function(c_currency_item){if(c_currency_item===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareCurrencyItem();let currencyItem=new timapi.CurrencyItem;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_currency(c_currency_item,p.p()));currencyItem.currency=h.cbv(timapi.constants.Currency,p.v()-1);h.assert(f.get_currency_type(c_currency_item,p.p()));currencyItem.type=h.ebv(timapi.constants.CurrencyType,p.v())}finally{if(p)p.dispose()}return Object.freeze(currencyItem)},deactivateResponse:undefined,prepareDeactivateResponse:function(){let f=timapi._TimApiHelpers.deactivateResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.deactivateResponse={get_print_data:g("ta_deactivate_response_get_print_data",["number"]),get_counters:g("ta_deactivate_response_get_counters",["number"])}}return f},unwrapDeactivateResponse:function(c_deactivate_response){if(c_deactivate_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareDeactivateResponse();let deactivateResponse=new timapi.DeactivateResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_print_data(c_deactivate_response,p.p()));deactivateResponse.printData=timapi._TimApiHelpers.unwrapPrintData(p.v());h.assert(f.get_counters(c_deactivate_response,p.p()));deactivateResponse.counters=timapi._TimApiHelpers.unwrapCounters(p.v())}finally{if(p)p.dispose()}return Object.freeze(deactivateResponse)},ecrInfo:undefined,prepareEcrInfo:function(){let f=timapi._TimApiHelpers.ecrInfo;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.ecrInfo={create:g("ta_ecr_info_create",[]),get_type:g("ta_ecr_info_get_type",["number"]),set_type:g("ta_ecr_info_set_type",["number"]),get_name:g("ta_ecr_info_get_name",["number"]),set_name:g("ta_ecr_info_set_name",["number"]),get_manufacturer_name:g("ta_ecr_info_get_manufacturer_name",["number"]),set_manufacturer_name:g("ta_ecr_info_set_manufacturer_name",["number"]),get_version:g("ta_ecr_info_get_version",["number"]),set_version:g("ta_ecr_info_set_version",["number"]),get_serial_number:g("ta_ecr_info_get_serial_number",["number"]),set_serial_number:g("ta_ecr_info_set_serial_number",["number"]),get_architecture:g("ta_ecr_info_get_architecture",["number"]),set_architecture:g("ta_ecr_info_set_architecture",["number"]),get_integrator_solution:g("ta_ecr_info_get_integrator_solution",["number"]),set_integrator_solution:g("ta_ecr_info_set_integrator_solution",["number"]),get_remote_ip:g("ta_ecr_info_get_remote_ip",["number"]),set_remote_ip:g("ta_ecr_info_set_remote_ip",["number"])}}return f},hardware:undefined,prepareHardware:function(){let f=timapi._TimApiHelpers.hardware;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.hardware={get_hardware_type:g("ta_hardware_get_hardware_type",["number"]),get_serial_number:g("ta_hardware_get_serial_number",["number"]),get_production_date:g("ta_hardware_get_production_date",["number"]),get_product_version:g("ta_hardware_get_product_version",["number"]),get_firmware_version:g("ta_hardware_get_firmware_version",["number"]),get_security_status:g("ta_hardware_get_security_status",["number"]),get_last_cleaning_date:g("ta_hardware_get_last_cleaning_date",["number"]),get_imsi:g("ta_hardware_get_imsi",["number"]),get_imei:g("ta_hardware_get_imei",["number"]),get_iccid:g("ta_hardware_get_iccid",["number"]),get_hardware_address:g("ta_hardware_get_hardware_address",["number"]),get_hardware_description:g("ta_hardware_get_hardware_description",["number"])}}return f},unwrapHardware:function(c_hardware){if(c_hardware===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareHardware();let hardware=new timapi.Hardware;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_hardware_type(c_hardware,p.p()));hardware.hardwareType=h.ebv(timapi.constants.HardwareType,p.v());h.assert(f.get_serial_number(c_hardware,p.p()));hardware.serialNumber=h.contentOfIfValid(p);h.assert(f.get_production_date(c_hardware,p.p()));hardware.productionDate=timapi._TimApiHelpers.unwrapTimeDate(p.v());h.assert(f.get_product_version(c_hardware,p.p()));hardware.productVersion=h.contentOfIfValid(p);h.assert(f.get_firmware_version(c_hardware,p.p()));hardware.firmwareVersion=h.contentOfIfValid(p);h.assert(f.get_security_status(c_hardware,p.p()));hardware.securityStatus=h.ebv(timapi.constants.SecurityStatus,p.v());h.assert(f.get_last_cleaning_date(c_hardware,p.p()));hardware.lastCleaningDate=timapi._TimApiHelpers.unwrapTimeDate(p.v());h.assert(f.get_imsi(c_hardware,p.p()));hardware.imsi=h.contentOfIfValid(p);h.assert(f.get_imei(c_hardware,p.p()));hardware.imei=h.contentOfIfValid(p);h.assert(f.get_iccid(c_hardware,p.p()));hardware.iccid=h.contentOfIfValid(p);h.assert(f.get_hardware_address(c_hardware,p.p()));hardware.hardwareAddress=h.contentOfIfValid(p);h.assert(f.get_hardware_description(c_hardware,p.p()));hardware.hardwareDescription=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return hardware},hardwareInformationResponse:undefined,prepareHardwareInformationResponse:function(){let f=timapi._TimApiHelpers.hardwareInformationResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.hardwareInformationResponse={get_hardwares:g("ta_hardware_information_response_get_hardwares",["number"]),get_kernel_versions:g("ta_hardware_information_response_get_kernel_versions",["number"]),get_settings:g("ta_hardware_information_response_get_settings",["number"]),get_statistics:g("ta_hardware_information_response_get_statistics",["number"]),get_battery_level:g("ta_hardware_information_response_get_battery_level",["number"]),get_battery_charging:g("ta_hardware_information_response_get_battery_charging",["number"])}}return f},unwrapHardwareInformationResponse:function(c_hardware_information_response){if(c_hardware_information_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareHardwareInformationResponse();let hardwareInformationResponse=new timapi.HardwareInformationResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_hardwares(c_hardware_information_response,p.p()));let hardwares=[];h.lit(p.v(),function(c_hardware){hardwares.push(timapi._TimApiHelpers.unwrapHardware(c_hardware))});hardwareInformationResponse.hardwares=hardwares;h.assert(f.get_kernel_versions(c_hardware_information_response,p.p()));hardwareInformationResponse.kernelVersions=timapi._TimApiHelpers.unwrapESMap(timapi.constants.KernelType,p.v());h.assert(f.get_settings(c_hardware_information_response,p.p()));hardwareInformationResponse.settings=timapi._TimApiHelpers.unwrapESMap(timapi.constants.SettingType,p.v());h.assert(f.get_statistics(c_hardware_information_response,p.p()));hardwareInformationResponse.statistics=timapi._TimApiHelpers.unwrapSSMap(p.v());h.assert(f.get_battery_level(c_hardware_information_response,p.p()));hardwareInformationResponse.batteryLevel=p.v();h.assert(f.get_battery_charging(c_hardware_information_response,p.p()));hardwareInformationResponse.batteryCharging=timapi._TimApiHelpers.boolConvertCToJs(p.v())}finally{if(p)p.dispose()}return Object.freeze(hardwareInformationResponse)},integer:undefined,prepareInteger:function(){let f=timapi._TimApiHelpers.integer;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.integer={create:g("TAWAIntegerCreate",["number","number","number"]),getValue:g("TAWAIntegerGetValue",["number","number","number"])}}return f},unwrapInteger:function(c_integer){if(c_integer===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareInteger();let integer=undefined;let p,p2,p3;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;p3=new timapi._TimApiHelpers.TAPointer;h.assert(f.getValue(c_integer,p.p(),p2.p(),p3.p()));let low=p.v();let high=p2.v();let sign=p3.v();integer=low+high*2147483648;if(sign===1){integer=-integer}}finally{if(p)p.dispose();if(p2)p2.dispose();if(p3)p3.dispose()}return integer},itemQuantity:undefined,prepareItemQuantity:function(){let f=timapi._TimApiHelpers.itemQuantity;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.itemQuantity={create:g("ta_item_quantity_create",[]),get_quantity:g("ta_item_quantity_get_quantity",["number"]),set_quantity:g("ta_item_quantity_set_quantity",["number"]),get_exponent:g("ta_item_quantity_get_exponent",["number"]),set_exponent:g("ta_item_quantity_set_exponent",["number"]),get_quantity_type:g("ta_item_quantity_get_quantity_type",["number"]),set_quantity_type:g("ta_item_quantity_set_type",["number"])}}return f},convertItemQuantity:function(itemQuantity){if(itemQuantity===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareItemQuantity();let wa_item_quantity,s;try{wa_item_quantity=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_item_quantity.p()));h.assert(f.set_quantity(wa_item_quantity.v(),itemQuantity.quantity));h.assert(f.set_exponent(wa_item_quantity.v(),itemQuantity.exponent));h.assert(f.set_quantity_type(wa_item_quantity.v(),s.replaceOrUndef(itemQuantity.quantityType)))}catch(err){if(wa_item_quantity)wa_item_quantity.dispose();throw err}finally{}return wa_item_quantity},unwrapItemQuantity:function(c_item_quantity){if(c_item_quantity===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareItemQuantity();let itemQuantity=new timapi.ItemQuantity;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_quantity(c_item_quantity,p.p()));itemQuantity.quantity=p.v();h.assert(f.get_exponent(c_item_quantity,p.p()));itemQuantity.exponent=p.v();h.assert(f.get_quantity_type(c_item_quantity,p.p()));itemQuantity.quantityType=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return itemQuantity},list:undefined,prepareList:function(){let f=timapi._TimApiHelpers.list;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.list={create:g("ta_list_create"),add:g("ta_list_add",["number"]),get_count:g("ta_list_get_count",["number"]),get_at:g("ta_list_get_at",["number","number"])}}return f},iterateList:function(list,iter_func){let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareList();let p;let count=0;if(list===0){return}try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_count(list,p.p()));count=p.v();for(let i=0;i<count;i++){h.assert(f.get_at(list,i,p.p()));iter_func(p.v())}}finally{if(p)p.dispose()}},loyaltyCoupon:undefined,prepareLoyaltyCoupon:function(){let f=timapi._TimApiHelpers.loyaltyCoupon;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.loyaltyCoupon={create:g("ta_loyalty_coupon_create",["number","number","number","number","number"]),get_amount:g("ta_loyalty_coupon_get_amount",["number"]),get_currency:g("ta_loyalty_coupon_get_currency",["number"]),get_exponent:g("ta_loyalty_coupon_get_exponent",["number"]),get_id:g("ta_loyalty_coupon_get_id",["number"]),get_rejection_reason:g("ta_loyalty_coupon_get_rejection_reason",["number"])}}return f},convertLoyaltyCoupon:function(loyaltyCoupon){if(loyaltyCoupon===undefined){return new timapi._TimApiHelpers.TAObject}if(loyaltyCoupon.amount===undefined){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,"LoyaltyCoupon needs valid amount")}if(loyaltyCoupon.currency===undefined){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,"LoyaltyCoupon needs valid currency")}if(loyaltyCoupon.couponId===undefined){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,"LoyaltyCoupon needs valid couponId")}if(loyaltyCoupon.exponent===undefined){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,"LoyaltyCoupon needs valid exponent")}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareLoyaltyCoupon();let wa_loyalty_coupon,s_id,i_amount,i_exponent;try{wa_loyalty_coupon=new timapi._TimApiHelpers.TAObject;i_amount=timapi._TimApiHelpers.TAInteger.create(loyaltyCoupon.amount);s_id=timapi._TimApiHelpers.TAString.create(loyaltyCoupon.couponId);i_exponent=timapi._TimApiHelpers.TAInteger.create(loyaltyCoupon.exponent);h.assert(f.create(wa_loyalty_coupon.p(),i_amount.v(),loyaltyCoupon.currency._wav+1,i_exponent.v(),s_id.v(),loyaltyCoupon.couponRejectionReason!==undefined?loyaltyCoupon.couponRejectionReason._value:0))}catch(err){if(wa_loyalty_coupon)wa_loyalty_coupon.dispose();throw err}finally{if(s_id)s_id.dispose()}return wa_loyalty_coupon},unwrapLoyaltyCoupon:function(c_loyalty_coupon){if(c_loyalty_coupon===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareLoyaltyCoupon();let loyaltyCoupon=new timapi.LoyaltyCoupon;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_amount(c_loyalty_coupon,p.p()));loyaltyCoupon.amount=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_currency(c_loyalty_coupon,p.p()));loyaltyCoupon.currency=h.cbv(timapi.constants.Currency,p.v()-1);h.assert(f.get_exponent(c_loyalty_coupon,p.p()));loyaltyCoupon.exponent=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_id(c_loyalty_coupon,p.p()));loyaltyCoupon.couponId=h.contentOfIfValid(p);h.assert(f.get_rejection_reason(c_loyalty_coupon,p.p()));loyaltyCoupon.couponRejectionReason=h.ebv(timapi.constants.CouponRejectionReason,p.v())}finally{if(p)p.dispose()}return loyaltyCoupon},loyaltyDiscount:undefined,prepareLoyaltyDiscount:function(){let f=timapi._TimApiHelpers.loyaltyDiscount;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.loyaltyDiscount={getValue:g("TAWALoyaltyDiscountGetValue",["number","number","number"]),get_currency:g("ta_loyalty_discount_get_currency",["number"]),get_exponent:g("ta_loyalty_discount_get_exponent",["number"]),get_discount_description:g("ta_loyalty_discount_get_discount_description",["number"])}}return f},unwrapLoyaltyDiscount:function(c_loyalty_discount){if(c_loyalty_discount===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareLoyaltyDiscount();let loyaltyDiscount=new timapi.LoyaltyDiscount;let p,p2,p3;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;p3=new timapi._TimApiHelpers.TAPointer;h.assert(f.getValue(c_loyalty_discount,p.p(),p2.p(),p3.p()));let low=p.v();let high=p2.v();let sign=p3.v();loyaltyDiscount.value=low+high*2147483648;if(sign===1){loyaltyDiscount.value=-loyaltyDiscount.value}h.assert(f.get_currency(c_loyalty_discount,p.p()));loyaltyDiscount.currency=h.cbv(timapi.constants.Currency,p.v()-1);h.assert(f.get_exponent(c_loyalty_discount,p.p()));loyaltyDiscount.exponent=p.v();h.assert(f.get_discount_description(c_loyalty_discount,p.p()));loyaltyDiscount.discountDescription=h.contentOfIfValid(p)}finally{if(p)p.dispose();if(p2)p2.dispose();if(p3)p3.dispose()}return loyaltyDiscount},loyaltyInformation:undefined,prepareLoyaltyInformation:function(){let f=timapi._TimApiHelpers.loyaltyInformation;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.loyaltyInformation={create:g("ta_loyalty_information_create",["number","number","number","number"]),get_value:g("ta_loyalty_information_get_value",["number"]),get_loyalty_info_type:g("ta_loyalty_information_get_loyalty_info_type",["number"]),get_loyalty_function_type:g("ta_loyalty_information_get_loyalty_function_type",["number"]),get_loyalty_number:g("ta_loyalty_information_get_loyalty_number",["number"])}}return f},convertLoyaltyInformation:function(loyaltyInformation){if(loyaltyInformation===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareLoyaltyInformation();let wa_loyalty_information,s_value,s_loyalty_info_type,s_loyalty_number;try{wa_loyalty_information=new timapi._TimApiHelpers.TAObject;if(loyaltyInformation.value!==undefined){s_value=timapi._TimApiHelpers.TAString.createFromUint8Array(loyaltyInformation.value)}else{s_value=new timapi._TimApiHelpers.TAObject}if(loyaltyInformation.loyaltyInfoType!==undefined){s_loyalty_info_type=timapi._TimApiHelpers.TAString.create(loyaltyInformation.loyaltyInfoType)}else{s_loyalty_info_type=new timapi._TimApiHelpers.TAObject}if(loyaltyInformation.loyaltyNumber!==undefined){s_loyalty_number=timapi._TimApiHelpers.TAString.createFromUint8Array(loyaltyInformation.loyaltyNumber)}else{s_loyalty_number=new timapi._TimApiHelpers.TAString}h.assert(f.create(wa_loyalty_information.p(),s_value.v(),s_loyalty_info_type.v(),loyaltyInformation.loyaltyFunctionType!==undefined?loyaltyInformation.loyaltyFunctionType._value:0,s_loyalty_number.v()))}catch(err){if(wa_loyalty_information)wa_loyalty_information.dispose();throw err}finally{if(s_value)s_value.dispose();if(s_loyalty_info_type)s_loyalty_info_type.dispose();if(s_loyalty_number)s_loyalty_number.dispose()}return wa_loyalty_information},unwrapLoyaltyInformation:function(c_loyalty_information){if(c_loyalty_information===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareLoyaltyInformation();let loyaltyInformation=new timapi.LoyaltyInformation;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_value(c_loyalty_information,p.p()));loyaltyInformation.value=timapi._TimApiHelpers.getBinaryContent(p);h.assert(f.get_loyalty_info_type(c_loyalty_information,p.p()));loyaltyInformation.loyaltyInfoType=h.contentOfIfValid(p);h.assert(f.get_loyalty_function_type(c_loyalty_information,p.p()));loyaltyInformation.loyaltyFunctionType=h.ebv(timapi.constants.LoyaltyFunctionType,p.v());h.assert(f.get_loyalty_number(c_loyalty_information,p.p()));loyaltyInformation.loyaltyNumber=timapi._TimApiHelpers.getBinaryContent(p)}finally{if(p)p.dispose()}return loyaltyInformation},displayProductInfo:undefined,prepareDisplayProductInfo:function(){let f=timapi._TimApiHelpers.displayProductInfo;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.displayProductInfo={create:g("ta_display_product_info_create",[]),get_image_data:g("ta_display_product_info_get_image_data",["number"]),set_image_data:g("ta_display_product_info_set_image_data",["number"]),get_image_file_width:g("ta_display_product_info_get_image_file_width",["number"]),set_image_file_width:g("ta_display_product_info_set_image_file_width",["number"]),get_image_file_height:g("ta_display_product_info_get_image_file_height",["number"]),set_image_file_height:g("ta_display_product_info_set_image_file_height",["number"]),get_image_file_format:g("ta_display_product_info_get_image_file_format",["number"]),set_image_file_format:g("ta_display_product_info_set_image_file_format",["number"]),get_product_display_name:g("ta_display_product_info_get_product_display_name",["number"]),set_product_display_name:g("ta_display_product_info_set_product_display_name",["number"]),get_background_color:g("ta_display_product_info_get_background_color",["number"]),set_background_color:g("ta_display_product_info_set_background_color",["number"])}}return f},convertDisplayProductInfo:function(displayProductInfo){if(displayProductInfo===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareDisplayProductInfo();let wa_display_product_info,wa_obj,s,b;let mem_color;try{wa_display_product_info=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_display_product_info.p()));if(displayProductInfo.imageData!==undefined){b=timapi._TimApiHelpers.TAString.createFromUint8Array(displayProductInfo.imageData)}else{b=new timapi._TimApiHelpers.TAObject}h.assert(f.set_image_data(wa_display_product_info.v(),b.v()));h.assert(f.set_product_display_name(wa_display_product_info.v(),s.replaceOrUndef(displayProductInfo.productDisplayName)));h.assert(f.set_image_file_width(wa_display_product_info.v(),displayProductInfo.imageFileWidth));h.assert(f.set_image_file_height(wa_display_product_info.v(),displayProductInfo.imageFileHeight));h.assert(f.set_image_file_format(wa_display_product_info.v(),displayProductInfo.imageFileFormat._value));mem_color=h.col(displayProductInfo.backgroundColor);if(mem_color!==0){h.assert(f.set_background_color(wa_display_product_info.v(),mem_color));Module._free(mem_color);mem_color=0}}catch(err){if(wa_display_product_info)wa_display_product_info.dispose();throw err}finally{if(mem_color!==0)Module._free(mem_color);if(wa_obj)wa_obj.dispose();if(b)b.dispose();if(s)s.dispose()}return wa_display_product_info},unwrapDisplayProductInfo:function(c_display_product_info){if(c_display_product_info===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareDisplayProductInfo();let displayProductInfo=new timapi.displayProductInfo;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_item_id(c_display_product_info,p.p()));displayProductInfo.itemId=h.contentOfIfValid(p);h.assert(f.get_prod_description(c_display_product_info,p.p()));displayProductInfo.prodDescription=h.contentOfIfValid(p);h.assert(f.get_amount(c_display_product_info,p.p()));displayProductInfo.amount=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_item_quantity(c_display_product_info,p.p()));displayProductInfo.itemQuantity=timapi._TimApiHelpers.unwrapItemQuantity(p.v());h.assert(f.get_amount_total(c_display_product_info,p.p()));displayProductInfo.amountTotal=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_loyalty_discounts(c_display_product_info,p.p()));let loyaltyDiscounts=[];h.lit(p.v(),function(c_loyalty_discount){loyaltyDiscounts.push(timapi._TimApiHelpers.unwrapLoyaltyDiscount(c_loyalty_discount))});displayProductInfo.loyaltyDiscounts=loyaltyDiscounts;h.assert(f.get_loyalty_coupons(c_display_product_info,p.p()));let loyaltyCoupons=[];h.lit(p.v(),function(c_loyalty_coupon){loyaltyCoupons.push(timapi._TimApiHelpers.unwrapLoyaltyCoupon(c_loyalty_coupon))});displayProductInfo.loyaltyCoupons=loyaltyCoupons}finally{if(p)p.dispose()}return displayProductInfo},loyaltyItem:undefined,prepareLoyaltyItem:function(){let f=timapi._TimApiHelpers.loyaltyItem;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.loyaltyItem={create:g("ta_loyalty_item_create",[]),get_item_id:g("ta_loyalty_item_get_item_id",["number"]),set_item_id:g("ta_loyalty_item_set_item_id",["number"]),get_prod_description:g("ta_loyalty_item_get_prod_description",["number"]),set_prod_description:g("ta_loyalty_item_set_prod_description",["number"]),get_amount:g("ta_loyalty_item_get_amount",["number"]),set_amount:g("ta_loyalty_item_set_amount",["number"]),get_item_quantity:g("ta_loyalty_item_get_item_quantity",["number"]),set_item_quantity:g("ta_loyalty_item_set_item_quantity",["number"]),get_amount_total:g("ta_loyalty_item_get_amount_total",["number"]),set_amount_total:g("ta_loyalty_item_set_amount_total",["number"]),get_loyalty_discounts:g("ta_loyalty_item_get_loyalty_discounts",["number"]),get_loyalty_coupons:g("ta_loyalty_item_get_loyalty_coupons",["number"]),get_display_product_info:g("ta_loyalty_item_get_display_product_info",["number"]),set_display_product_info:g("ta_loyalty_item_set_display_product_info",["number"])}}return f},convertLoyaltyItem:function(loyaltyItem){if(loyaltyItem===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareLoyaltyItem();let f_list=timapi._TimApiHelpers.prepareList();let wa_loyalty_item,wa_loyalty_discounts,wa_loyalty_coupons,wa_obj,s;try{wa_loyalty_item=new timapi._TimApiHelpers.TAObject;wa_loyalty_discounts=new timapi._TimApiHelpers.TAObject;wa_loyalty_coupons=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_loyalty_item.p()));h.assert(f.set_item_id(wa_loyalty_item.v(),s.replaceOrUndef(loyaltyItem.itemId)));h.assert(f.set_prod_description(wa_loyalty_item.v(),s.replaceOrUndef(loyaltyItem.prodDescription)));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(loyaltyItem.amount));h.assert(f.set_amount(wa_loyalty_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertItemQuantity(loyaltyItem.itemQuantity));h.assert(f.set_item_quantity(wa_loyalty_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(loyaltyItem.amountTotal));h.assert(f.set_amount_total(wa_loyalty_item.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertDisplayProductInfo(loyaltyItem.displayProductInfo));h.assert(f.set_display_product_info(wa_loyalty_item.v(),wa_obj.v()))}catch(err){if(wa_loyalty_item)wa_loyalty_item.dispose();throw err}finally{if(wa_loyalty_discounts)wa_loyalty_discounts.dispose();if(wa_loyalty_coupons)wa_loyalty_coupons.dispose();if(wa_obj)wa_obj.dispose();if(s)s.dispose()}return wa_loyalty_item},unwrapLoyaltyItem:function(c_loyalty_item){if(c_loyalty_item===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareLoyaltyItem();let loyaltyItem=new timapi.LoyaltyItem;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_item_id(c_loyalty_item,p.p()));loyaltyItem.itemId=h.contentOfIfValid(p);h.assert(f.get_prod_description(c_loyalty_item,p.p()));loyaltyItem.prodDescription=h.contentOfIfValid(p);h.assert(f.get_amount(c_loyalty_item,p.p()));loyaltyItem.amount=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_item_quantity(c_loyalty_item,p.p()));loyaltyItem.itemQuantity=timapi._TimApiHelpers.unwrapItemQuantity(p.v());h.assert(f.get_amount_total(c_loyalty_item,p.p()));loyaltyItem.amountTotal=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_loyalty_discounts(c_loyalty_item,p.p()));let loyaltyDiscounts=[];h.lit(p.v(),function(c_loyalty_discount){loyaltyDiscounts.push(timapi._TimApiHelpers.unwrapLoyaltyDiscount(c_loyalty_discount))});loyaltyItem.loyaltyDiscounts=loyaltyDiscounts;h.assert(f.get_loyalty_coupons(c_loyalty_item,p.p()));let loyaltyCoupons=[];h.lit(p.v(),function(c_loyalty_coupon){loyaltyCoupons.push(timapi._TimApiHelpers.unwrapLoyaltyCoupon(c_loyalty_coupon))});loyaltyItem.loyaltyCoupons=loyaltyCoupons}finally{if(p)p.dispose()}return loyaltyItem},unwrapLoyaltyItemList:function(c_loyalty_item_list){if(c_loyalty_item_list===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let list=[];h.lit(c_loyalty_item_list,function(c_loyalty_item){list.push(timapi._TimApiHelpers.unwrapLoyaltyItem(c_loyalty_item))});return list},map:undefined,prepareMap:function(){let f=timapi._TimApiHelpers.map;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.map={create:g("ta_map_create"),set:g("ta_map_set",["number","number"]),remove:g("ta_map_remove",["number"]),remove_all:g("ta_map_remove_all"),has:g("ta_map_has",["number","number"]),get_count:g("ta_map_get_count",["number"]),get:g("ta_map_get",["number","number"]),get_default:g("ta_map_get_default",["number","number","number"]),get_at:g("ta_map_get_at",["number","number","number"])}}return f},convertNSMap:function(nsMap){if(nsMap===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMap();let wa_map,i,s;try{wa_map=new timapi._TimApiHelpers.TAObject;i=new timapi._TimApiHelpers.TAInteger;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_map.p()));for(let[key,value]of nsMap){h.assert(f.set(wa_map.v(),i.replace(key),s.replace(value)))}}catch(err){if(wa_map)wa_map.dispose();throw err}finally{if(i)i.dispose();if(s)s.dispose()}return wa_map},convertESMap:function(esMap){if(esMap===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMap();let wa_map,i,s;try{wa_map=new timapi._TimApiHelpers.TAObject;i=new timapi._TimApiHelpers.TAInteger;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_map.p()));for(let[key,value]of esMap){if(key!==undefined){h.assert(f.set(wa_map.v(),i.replace(key._value),s.replace(value)))}}}catch(err){if(wa_map)wa_map.dispose();throw err}finally{if(i)i.dispose();if(s)s.dispose()}return wa_map},convertEBMap:function(ebMap){if(ebMap===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMap();let wa_map,i,b;try{wa_map=new timapi._TimApiHelpers.TAObject;i=new timapi._TimApiHelpers.TAInteger;b=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_map.p()));for(let[key,value]of ebMap){if(key!==undefined){h.assert(f.set(wa_map.v(),i.replace(key._value),b.replaceUint8Array(value)))}}}catch(err){if(wa_map)wa_map.dispose();throw err}finally{if(i)i.dispose();if(b)b.dispose()}return wa_map},unwrapNSMap:function(c_ns_map){if(c_ns_map===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMap();let nsMap=new Map;let p,p2;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_count(c_ns_map,p.p()));let count=p.v();for(let i=0;i<count;i++){h.assert(f.get_at(c_ns_map,i,p.p(),p2.p()));if(p.isValid()){let key=timapi._TimApiHelpers.unwrapInteger(p.v());nsMap.set(key,h.contentOfIfValid(p2))}}}finally{if(p)p.dispose();if(p2)p2.dispose()}return nsMap},unwrapESMap:function(enumeration,c_es_map){if(c_es_map===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMap();let esMap=new Map;let p,p2;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_count(c_es_map,p.p()));let count=p.v();for(let i=0;i<count;i++){h.assert(f.get_at(c_es_map,i,p.p(),p2.p()));if(p.isValid()){let key=timapi._TimApiHelpers.unwrapInteger(p.v());esMap.set(h.ebv(enumeration,key),h.contentOfIfValid(p2))}}}finally{if(p)p.dispose();if(p2)p2.dispose()}return esMap},unwrapEBMap:function(enumeration,c_eb_map){if(c_eb_map===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMap();let ebMap=new Map;let p,p2;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_count(c_eb_map,p.p()));let count=p.v();for(let i=0;i<count;i++){h.assert(f.get_at(c_eb_map,i,p.p(),p2.p()));if(p.isValid()){let key=timapi._TimApiHelpers.unwrapInteger(p.v());ebMap.set(h.ebv(enumeration,key),h.contentOfUint8ArrayIfValid(p2))}}}finally{if(p)p.dispose();if(p2)p2.dispose()}return ebMap},unwrapSSMap:function(c_ss_map){if(c_ss_map===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMap();let ssMap=new Map;let p,p2;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_count(c_ss_map,p.p()));let count=p.v();for(let i=0;i<count;i++){h.assert(f.get_at(c_ss_map,i,p.p(),p2.p()));if(p.isValid()){ssMap.set(h.contentOfIfValid(p),h.contentOfIfValid(p2))}}}finally{if(p)p.dispose();if(p2)p2.dispose()}return ssMap},unwrapSBMap:function(c_sb_map){if(c_sb_map===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMap();let sbMap=new Map;let p,p2;try{p=new timapi._TimApiHelpers.TAPointer;p2=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_count(c_sb_map,p.p()));let count=p.v();for(let i=0;i<count;i++){h.assert(f.get_at(c_sb_map,i,p.p(),p2.p()));if(p.isValid()){sbMap.set(h.contentOfIfValid(p),timapi._TimApiHelpers.getBinaryContent(p2))}}}finally{if(p)p.dispose();if(p2)p2.dispose()}return sbMap},merchantOption:undefined,prepareMerchantOption:function(){let f=timapi._TimApiHelpers.merchantOption;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.merchantOption={create:g("ta_merchant_option_create",["number"],["number"]),get_type:g("ta_merchant_option_get_type",["number"]),get_value:g("ta_merchant_option_get_value",["number"])}}return f},convertMerchantOption:function(merchantOption){if(merchantOption===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMerchantOption();let wa_merchant_option,s;try{wa_merchant_option=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_merchant_option.p(),merchantOption.type._value,s.replace(merchantOption.value)))}catch(err){if(wa_merchant_option)wa_merchant_option.dispose();throw err}finally{if(s)s.dispose()}return wa_merchant_option},unwrapMerchantOption:function(c_merchant_option){if(c_merchant_option===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareMerchantOption();let merchantOption=undefined;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_type(c_merchant_option,p.p()));let type=h.ebv(timapi.constants.MerchantOptionType,p.v());h.assert(f.get_value(c_merchant_option,p.p()));let value=h.contentOfIfValid(p);merchantOption=new timapi.MerchantOption(type,value)}finally{if(p)p.dispose()}return merchantOption},nativeError:undefined,prepareNativeError:function(){let f=timapi._TimApiHelpers.nativeError;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.nativeError={get_code:g("ta_native_error_get_code",["number"]),get_message:g("ta_native_error_get_message",["number"]),get_source:g("ta_native_error_get_source",["number"])}}return f},unwrapNativeError:function(c_native_error){if(c_native_error===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareNativeError();let nativeError=new timapi.NativeError;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_code(c_native_error,p.p()));nativeError.code=p.v();h.assert(f.get_message(c_native_error,p.p()));nativeError.message=h.contentOfIfValid(p);h.assert(f.get_source(c_native_error,p.p()));nativeError.source=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return nativeError},networkInformation:undefined,prepareNetworkInformation:function(){let f=timapi._TimApiHelpers.networkInformation;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.networkInformation={get_terminal_ip:g("ta_network_information_get_terminal_ip",["number"]),get_terminal_ip_mask:g("ta_network_information_get_terminal_ip_mask",["number"]),get_terminal_ip_gw:g("ta_network_information_get_terminal_ip_gw",["number"]),get_terminal_ip_dns:g("ta_network_information_get_terminal_ip_dns",["number"])}}return f},unwrapNetworkInformation:function(c_network_information){if(c_network_information===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareNetworkInformation();let networkInformation=new timapi.NetworkInformation;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_terminal_ip(c_network_information,p.p()));networkInformation.terminalIp=h.contentOfIfValid(p);h.assert(f.get_terminal_ip_mask(c_network_information,p.p()));networkInformation.terminalIpMask=h.contentOfIfValid(p);h.assert(f.get_terminal_ip_gw(c_network_information,p.p()));networkInformation.terminalIpGw=h.contentOfIfValid(p);h.assert(f.get_terminal_ip_dns(c_network_information,p.p()));networkInformation.terminalIpDns=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return Object.freeze(networkInformation)},ppInfo:undefined,preparePpInfo:function(){let f=timapi._TimApiHelpers.ppInfo;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.ppInfo={get_payment_protocol:g("ta_ppinfo_get_payment_protocol",["number"]),get_pp_ep2_trans_seq_cnt:g("ta_ppinfo_get_pp_ep2_trans_seq_cnt",["number"]),get_pp_ep2_trans_seq_cnt_orig:g("ta_ppinfo_get_pp_ep2_trans_seq_cnt_orig",["number"]),get_pp_ep2_auth_reslt:g("ta_ppinfo_get_pp_ep2_auth_reslt",["number"]),get_pp_ep2_auth_resp_c:g("ta_ppinfo_get_pp_ep2_auth_resp_c",["number"])}}return f},unwrapPpInfo:function(c_ppinfo){if(c_ppinfo===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.preparePpInfo();let ppInfo=new timapi.PpInfo;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_payment_protocol(c_ppinfo,p.p()));ppInfo.paymentProtocol=h.ebv(timapi.constants.PaymentProtocol,p.v());h.assert(f.get_pp_ep2_trans_seq_cnt(c_ppinfo,p.p()));ppInfo.ppEp2TransSeqCnt=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_pp_ep2_trans_seq_cnt_orig(c_ppinfo,p.p()));ppInfo.ppEp2TransSeqCntOrig=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_pp_ep2_auth_reslt(c_ppinfo,p.p()));ppInfo.ppEp2AuthReslt=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_pp_ep2_auth_resp_c(c_ppinfo,p.p()));ppInfo.ppEp2AuthRespC=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return ppInfo},printData:undefined,preparePrintData:function(){let f=timapi._TimApiHelpers.printData;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.printData={get_receipts:g("ta_print_data_get_receipts",["number"]),get_receipt_items:g("ta_print_data_get_receipt_items",["number"])}}return f},unwrapPrintData:function(c_print_data){if(c_print_data===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.preparePrintData();let printData=new timapi.PrintData;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_receipts(c_print_data,p.p()));let receipts=[];h.lit(p.v(),function(c_receipt){receipts.push(timapi._TimApiHelpers.unwrapReceipt(c_receipt))});printData.receipts=receipts;h.assert(f.get_receipt_items(c_print_data,p.p()));let receiptItems=[];h.lit(p.v(),function(receipt_items){receiptItems.push(timapi._TimApiHelpers.unwrapReceiptItems(receipt_items))});printData.receiptItems=receiptItems}finally{if(p)p.dispose()}return Object.freeze(printData)},printOption:undefined,preparePrintOption:function(){let f=timapi._TimApiHelpers.printOption;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.printOption={create:g("ta_print_option_create",["number","number","number","number"]),get_recipient:g("ta_print_option_get_recipient",["number"]),get_print_format:g("ta_print_option_get_print_format",["number"]),get_print_width:g("ta_print_option_get_print_width",["number"]),get_print_flags:g("ta_print_option_get_print_flags",["number"])}}return f},convertPrintOption:function(printOption){if(printOption===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.preparePrintOption();let wa_print_option;try{wa_print_option=new timapi._TimApiHelpers.TAObject;h.assert(f.create(wa_print_option.p(),printOption.recipient._value,printOption.printFormat._value,printOption.printWidth,h.eSet_t_eBit(printOption.printFlags)))}catch(err){if(wa_print_option)wa_print_option.dispose();throw err}finally{}return wa_print_option},unwrapPrintOption:function(c_print_option){if(c_print_option===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.preparePrintOption();let printOption;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_recipient(c_print_option,p.p()));let recipient=h.ebv(timapi.constants.Recipient,p.v());h.assert(f.get_print_format(c_print_option,p.p()));let printFormat=h.ebv(timapi.constants.PrintFormat,p.v());h.assert(f.get_print_width(c_print_option,p.p()));let printWidth=p.v();h.assert(f.get_print_flags(c_print_option,p.p()));let printFlags=h.eBit_t_eSet(timapi.constants.PrintFlag,p.v());printOption=new timapi.PrintOption(recipient,printFormat,printWidth,printFlags)}finally{if(p)p.dispose()}return printOption},receipt:undefined,prepareReceipt:function(){let f=timapi._TimApiHelpers.receipt;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.receipt={get_recipient:g("ta_receipt_get_recipient",["number"]),get_value:g("ta_receipt_get_value",["number"])}}return f},unwrapReceipt:function(c_receipt){if(c_receipt===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareReceipt();let receipt=new timapi.Receipt;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_recipient(c_receipt,p.p()));receipt.recipient=h.ebv(timapi.constants.Recipient,p.v());h.assert(f.get_value(c_receipt,p.p()));receipt.value=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return Object.freeze(receipt)},receiptItem:undefined,prepareReceiptItem:function(){let f=timapi._TimApiHelpers.receiptItem;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.receiptItem={get_receipt_item_type:g("ta_receipt_item_get_receipt_item_type",["number"]),get_recipient:g("ta_receipt_item_get_recipient",["number"]),get_value:g("ta_receipt_item_get_value",["number"])}}return f},unwrapReceiptItem:function(c_receipt_item){if(c_receipt_item===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareReceiptItem();let receiptItem=new timapi.ReceiptItem;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_receipt_item_type(c_receipt_item,p.p()));receiptItem.receiptItemType=h.ebv(timapi.constants.ReceiptItemType,p.v());h.assert(f.get_recipient(c_receipt_item,p.p()));receiptItem.recipient=h.ebv(timapi.constants.Recipient,p.v());h.assert(f.get_value(c_receipt_item,p.p()));receiptItem.value=h.contentOfIfValid(p)}finally{if(p)p.dispose()}return Object.freeze(receiptItem)},receiptItems:undefined,prepareReceiptItems:function(){let f=timapi._TimApiHelpers.receiptItems;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.receiptItems={get_type:g("ta_receipt_items_get_receipt_type",["number"]),get_receipt_item:g("ta_receipt_items_get_receipt_item",["number"])}}return f},unwrapReceiptItems:function(c_receipt_items){if(c_receipt_items===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareReceiptItems();let receiptItems=new timapi.ReceiptItems;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_type(c_receipt_items,p.p()));receiptItems.receiptType=h.ebv(timapi.constants.ReceiptType,p.v());h.assert(f.get_receipt_item(c_receipt_items,p.p()));let receiptItem=[];h.lit(p.v(),function(c_receipt_item){receiptItem.push(timapi._TimApiHelpers.unwrapReceiptItem(c_receipt_item))});receiptItems.receiptItem=receiptItem}finally{if(p)p.dispose()}return Object.freeze(receiptItems)},receiptRequestResponse:undefined,prepareReceiptRequestResponse:function(){let f=timapi._TimApiHelpers.receiptRequestResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.receiptRequestResponse={get_print_data:g("ta_receipt_request_response_get_print_data",["number"]),get_has_more_receipts:g("ta_receipt_request_response_get_has_more_receipts",["number"])}}return f},unwrapReceiptRequestResponse:function(c_receipt_request_response){if(c_receipt_request_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareReceiptRequestResponse();let receiptRequestResponse=new timapi.ReceiptRequestResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_print_data(c_receipt_request_response,p.p()));receiptRequestResponse.printData=timapi._TimApiHelpers.unwrapPrintData(p.v());h.assert(f.get_has_more_receipts(c_receipt_request_response,p.p()));receiptRequestResponse.hasMoreReceipts=h.bc_c_t_js(p.v())}finally{if(p)p.dispose()}return Object.freeze(receiptRequestResponse)},reconciliationResponse:undefined,prepareReconciliationResponse:function(){let f=timapi._TimApiHelpers.reconciliationResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.reconciliationResponse={get_counters:g("ta_reconciliation_response_get_counters",["number"]),get_print_data:g("ta_reconciliation_response_get_print_data",["number"])}}return f},unwrapReconciliationResponse:function(c_reconciliation_response){if(c_reconciliation_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareReconciliationResponse();let reconciliationResponse=new timapi.ReconciliationResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_counters(c_reconciliation_response,p.p()));reconciliationResponse.counters=timapi._TimApiHelpers.unwrapCounters(p.v());h.assert(f.get_print_data(c_reconciliation_response,p.p()));reconciliationResponse.printData=timapi._TimApiHelpers.unwrapPrintData(p.v())}finally{if(p)p.dispose()}return Object.freeze(reconciliationResponse)},showDialogRequest:undefined,prepareShowDialogRequest:function(){let f=timapi._TimApiHelpers.showDialogRequest;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.showDialogRequest={create:g("ta_show_dialog_request_create",["number"]),set_brand_bar:g("ta_show_dialog_request_set_brand_bar",["number"]),set_brand_mode:g("ta_show_dialog_request_set_brand_mode",["number"]),set_resource_id:g("ta_show_dialog_request_set_resource_id",["number"]),set_theme:g("ta_show_dialog_request_set_theme",["number"]),set_timeout:g("ta_show_dialog_request_set_timeout",["number"]),set_language:g("ta_show_dialog_request_set_language",["number"]),set_placeholder_items:g("ta_show_dialog_request_set_placeholder_items",["number"]),set_resource_parameters:g("ta_show_dialog_request_set_resource_parameters",["number"])}}return f},convertShowDialogRequest:function(showDialogRequest){if(showDialogRequest===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareShowDialogRequest();let f_list=timapi._TimApiHelpers.prepareList();let wa_show_dialog_request,wa_obj,wa_list,s,i;try{wa_show_dialog_request=new timapi._TimApiHelpers.TAObject;wa_list=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;i=new timapi._TimApiHelpers.TAInteger;h.assert(f.create(wa_show_dialog_request.p()));h.assert(f_list.create(wa_list.p()));for(let brandBar of showDialogRequest.brandBar){h.assert(f_list.add(wa_list.v(),i.replaceOrUndef(brandBar._value)))}h.assert(f.set_brand_bar(wa_show_dialog_request.v(),wa_list.v()));h.assert(f.set_brand_mode(wa_show_dialog_request.v(),showDialogRequest.brandMode._value));h.assert(f.set_resource_id(wa_show_dialog_request.v(),showDialogRequest.resourceId._value));h.assert(f.set_theme(wa_show_dialog_request.v(),showDialogRequest.theme._value));h.assert(f.set_timeout(wa_show_dialog_request.v(),showDialogRequest.timeout));h.assert(f.set_language(wa_show_dialog_request.v(),s.replaceOrUndef(showDialogRequest.language)));wa_obj.takeover(timapi._TimApiHelpers.convertNSMap(showDialogRequest.placeholderItems));h.assert(f.set_placeholder_items(wa_show_dialog_request.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertESMap(showDialogRequest.resourceParameters));h.assert(f.set_resource_parameters(wa_show_dialog_request.v(),wa_obj.v()))}catch(err){if(wa_show_dialog_request)wa_show_dialog_request.dispose();throw err}finally{if(s)s.dispose();if(i)i.dispose();if(wa_obj)wa_obj.dispose();if(wa_list)wa_list.dispose()}return wa_show_dialog_request},showDialogResponse:undefined,prepareShowDialogResponse:function(){let f=timapi._TimApiHelpers.showDialogResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.showDialogResponse={get_reason:g("ta_show_dialog_response_get_reason",["number"]),get_user_input:g("ta_show_dialog_response_get_user_input",["number"]),get_card_data:g("ta_show_dialog_response_get_card_data",["number"])}}return f},unwrapShowDialogResponse:function(c_show_dialog_response){if(c_show_dialog_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareShowDialogResponse();let showDialogResponse=new timapi.ShowDialogResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_reason(c_show_dialog_response,p.p()));showDialogResponse.reason=h.ebv(timapi.constants.Reason,p.v());h.assert(f.get_user_input(c_show_dialog_response,p.p()));showDialogResponse.userInput=h.contentOfIfValid(p);h.assert(f.get_card_data(c_show_dialog_response,p.p()));showDialogResponse.cardData=timapi._TimApiHelpers.unwrapCardData(p.v())}finally{if(p)p.dispose()}return showDialogResponse},showSignatureCaptureRequest:undefined,prepareShowSignatureCaptureRequest:function(){let f=timapi._TimApiHelpers.showSignatureCaptureRequest;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.showSignatureCaptureRequest={create:g("ta_show_signature_capture_request_create",["number"]),set_brand_bar:g("ta_show_signature_capture_request_set_brand_bar",["number"]),set_brand_mode:g("ta_show_signature_capture_request_set_brand_mode",["number"]),set_background_color:g("ta_show_signature_capture_request_set_background_color",["number"]),set_image_file_format:g("ta_show_signature_capture_request_set_image_file_format",["number"]),set_image_file_width:g("ta_show_signature_capture_request_set_image_file_width",["number"]),set_image_file_height:g("ta_show_signature_capture_request_set_image_file_height",["number"]),set_resource_id:g("ta_show_signature_capture_request_set_resource_id",["number"]),set_signature_color:g("ta_show_signature_capture_request_set_signature_color",["number"]),set_theme:g("ta_show_signature_capture_request_set_theme",["number"]),set_timeout:g("ta_show_signature_capture_request_set_timeout",["number"]),set_language:g("ta_show_signature_capture_request_set_language",["number"]),set_watermark_color:g("ta_show_signature_capture_request_set_watermark_color",["number"]),set_watermark_items:g("ta_show_signature_capture_request_set_watermark_items",["number"])}}return f},convertShowSignatureCaptureRequest:function(showSignatureCaptureRequest){if(showSignatureCaptureRequest===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareShowSignatureCaptureRequest();let f_list=timapi._TimApiHelpers.prepareList();let wa_show_signature_capture_request,wa_obj,wa_brand_bar,wa_watermark_items,s,i;let mem_color;try{wa_show_signature_capture_request=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;wa_brand_bar=new timapi._TimApiHelpers.TAObject;wa_watermark_items=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;i=new timapi._TimApiHelpers.TAInteger;h.assert(f.create(wa_show_signature_capture_request.p()));h.assert(f_list.create(wa_brand_bar.p()));for(let brandBar of showSignatureCaptureRequest.brandBar){h.assert(f_list.add(wa_brand_bar.v(),i.replaceOrUndef(brandBar._value)))}h.assert(f.set_brand_bar(wa_show_signature_capture_request.v(),wa_brand_bar.v()));h.assert(f.set_brand_mode(wa_show_signature_capture_request.v(),showSignatureCaptureRequest.brandMode._value));mem_color=h.col(showSignatureCaptureRequest.backgroundColor);if(mem_color!==0){h.assert(f.set_background_color(wa_show_signature_capture_request.v(),mem_color));Module._free(mem_color);mem_color=0}h.assert(f.set_image_file_format(wa_show_signature_capture_request.v(),showSignatureCaptureRequest.imageFileFormat._value));h.assert(f.set_image_file_width(wa_show_signature_capture_request.v(),showSignatureCaptureRequest.imageFileWidth));h.assert(f.set_image_file_height(wa_show_signature_capture_request.v(),showSignatureCaptureRequest.imageFileHeight));h.assert(f.set_resource_id(wa_show_signature_capture_request.v(),showSignatureCaptureRequest.resourceId._value));mem_color=h.col(showSignatureCaptureRequest.signatureColor);if(mem_color!==0){h.assert(f.set_signature_color(wa_show_signature_capture_request.v(),mem_color));Module._free(mem_color);mem_color=0}h.assert(f.set_theme(wa_show_signature_capture_request.v(),showSignatureCaptureRequest.theme._value));h.assert(f.set_timeout(wa_show_signature_capture_request.v(),showSignatureCaptureRequest.timeout));h.assert(f.set_language(wa_show_signature_capture_request.v(),s.replaceOrUndef(showSignatureCaptureRequest.language)));mem_color=h.col(showSignatureCaptureRequest.watermarkColor);if(mem_color!==0){h.assert(f.set_watermark_color(wa_show_signature_capture_request.v(),mem_color));Module._free(mem_color);mem_color=0}h.assert(f_list.create(wa_watermark_items.p()));for(let watermarkItem of showSignatureCaptureRequest.watermarkItems){h.assert(f_list.add(wa_watermark_items.v(),s.replaceOrUndef(watermarkItem)))}h.assert(f.set_watermark_items(wa_show_signature_capture_request.v(),wa_watermark_items.v()))}catch(err){if(wa_show_signature_capture_request)wa_show_signature_capture_request.dispose();throw err}finally{if(mem_color!==0)Module._free(mem_color);if(wa_obj)wa_obj.dispose();if(wa_brand_bar)wa_brand_bar.dispose();if(wa_watermark_items)wa_watermark_items.dispose();if(s)s.dispose();if(i)i.dispose()}return wa_show_signature_capture_request},showSignatureCaptureResponse:undefined,prepareShowSignatureCaptureResponse:function(){let f=timapi._TimApiHelpers.showSignatureCaptureResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.showSignatureCaptureResponse={get_reason:g("ta_show_signature_capture_response_get_reason",["number"]),get_image_file_format:g("ta_show_signature_capture_response_get_image_file_format",["number"]),get_image_width:g("ta_show_signature_capture_response_get_image_width",["number"]),get_image_height:g("ta_show_signature_capture_response_get_image_height",["number"]),get_image_data:g("ta_show_signature_capture_response_get_image_data",["number"])}}return f},unwrapShowSignatureCaptureResponse:function(c_show_signature_capture_response){if(c_show_signature_capture_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareShowSignatureCaptureResponse();let showSignatureCaptureResponse=new timapi.ShowSignatureCaptureResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_reason(c_show_signature_capture_response,p.p()));showSignatureCaptureResponse.reason=h.ebv(timapi.constants.Reason,p.v());h.assert(f.get_image_file_format(c_show_signature_capture_response,p.p()));showSignatureCaptureResponse.imageFileFormat=h.ebv(timapi.constants.ImageFileFormat,p.v());h.assert(f.get_image_width(c_show_signature_capture_response,p.p()));showSignatureCaptureResponse.imageWidth=p.v();h.assert(f.get_image_height(c_show_signature_capture_response,p.p()));showSignatureCaptureResponse.imageHeight=p.v();h.assert(f.get_image_data(c_show_signature_capture_response,p.p()));showSignatureCaptureResponse.imageData=timapi._TimApiHelpers.getBinaryContent(p)}finally{if(p)p.dispose()}return showSignatureCaptureResponse},signatureInformation:undefined,prepareSignatureInformation:function(){let f=timapi._TimApiHelpers.signatureInformation;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.signatureInformation={get_image_file_format:g("ta_signature_information_get_image_file_format",["number"]),get_image_width:g("ta_signature_information_get_image_width",["number"]),get_image_height:g("ta_signature_information_get_image_height",["number"]),get_image_data:g("ta_signature_information_get_image_data",["number"])}}return f},unwrapSignatureInformation:function(c_signature_information){if(c_signature_information===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareSignatureInformation();let signatureInformation=new timapi.SignatureInformation;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_image_file_format(c_signature_information,p.p()));signatureInformation.imageFileFormat=h.ebv(timapi.constants.ImageFileFormat,p.v());h.assert(f.get_image_width(c_signature_information,p.p()));signatureInformation.imageWidth=p.v();h.assert(f.get_image_height(c_signature_information,p.p()));signatureInformation.imageHeight=p.v();h.assert(f.get_image_data(c_signature_information,p.p()));signatureInformation.imageData=timapi._TimApiHelpers.getBinaryContent(p)}finally{if(p)p.dispose()}return Object.freeze(signatureInformation)},systemInformationResponse:undefined,prepareSystemInformationResponse:function(){let f=timapi._TimApiHelpers.systemInformationResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.systemInformationResponse={get_network_information:g("ta_system_information_response_get_network_information",["number"])}}return f},unwrapSystemInformationResponse:function(c_system_information){if(c_system_information===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareSystemInformationResponse();let systemInformationResponse=new timapi.SystemInformationResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_network_information(c_system_information,p.p()));systemInformationResponse.networkInformation=timapi._TimApiHelpers.unwrapNetworkInformation(p.v())}finally{if(p)p.dispose()}return Object.freeze(systemInformationResponse)},vasResult:undefined,prepareVasResult:function(){let f=timapi._TimApiHelpers.vasResult;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.vasResult={create:g("ta_vas_result_create",["number","number"]),get_vas_information_list_type:g("ta_vas_result_get_vas_information_list_type",["number"]),get_vas_information:g("ta_vas_result_get_vas_information",["number"])}}return f},convertVasResult:function(vasResult){if(vasResult===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareVasResult();let wa_vasresult,wa_map;try{wa_vasresult=new timapi._TimApiHelpers.TAObject;wa_map=timapi._TimApiHelpers.convertEBMap(vasResult.vasInformation);h.assert(f.create(wa_vasresult.p(),vasResult.vasInfoListType._value,wa_map.v()))}catch(err){if(wa_vasresult)wa_vasresult.dispose();throw err}finally{if(wa_map)wa_map.dispose()}return wa_vasresult},unwrapVasResult:function(c_vasresult){if(c_vasresult===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareVasResult();let vasResult=new timapi.VasResult;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_vas_information_list_type(c_vasresult,p.p()));vasResult.vasInfoListType=h.ebv(timapi.constants.VasInfoListType,p.v());h.assert(f.get_vas_information(c_vasresult,p.p()));vasResult.vasInformation=timapi._TimApiHelpers.unwrapEBMap(timapi.constants.VasInfoType,p.v())}finally{if(p)p.dispose()}return vasResult},terminal:undefined,terminalMap:{},prepareTerminal:function(){let t=timapi._TimApiHelpers.terminal;if(!t){let g=timapi._TimApiHelpers.getFunction;t=timapi._TimApiHelpers.terminal={activate_async:g("ta_terminal_activate_async"),activate_service_menu_async:g("ta_terminal_activate_service_menu_async"),amt_adjustment:g("ta_terminal_amt_adjustment",["number"]),amt_adjustment_2:g("ta_terminal_amt_adjustment_2",["number","number","number","number"]),application_information_async:g("ta_terminal_application_information_async"),balance_async:g("ta_terminal_balance_async"),balance_inquiry_async:g("ta_terminal_balance_inquiry_async"),cancel:g("ta_terminal_cancel"),cancel2:g("ta_terminal_cancel2",["number","number"]),change_settings_async:g("ta_terminal_change_settings_async",["number"]),close_dialog_mode_async:g("ta_terminal_close_dialog_mode_async"),close_maintenance_window_async:g("ta_terminal_close_maintenance_window_async"),close_reader_async:g("ta_terminal_close_reader_async"),commit_async:g("ta_terminal_commit_async"),commit_amount_async:g("ta_terminal_commit_amount_async",["number"]),connect_async:g("ta_terminal_connect_async"),counter_request_async:g("ta_terminal_counter_request_async",["number"]),create:g("ta_terminal_create",["number"]),dcc_rates_async:g("ta_terminal_dcc_rates_async"),deactivate_async:g("ta_terminal_deactivate_async"),disconnect_async:g("ta_terminal_disconnect_async"),dispose:g("ta_terminal_dispose"),eject_card_async:g("ta_terminal_eject_card_async"),finish_checkout_async:g("ta_terminal_finish_checkout_async"),hardware_information_async:g("ta_terminal_hardware_information_async"),hold_commit:g("ta_terminal_hold_commit"),init_transaction_async:g("ta_terminal_init_transaction_async",["number","number"]),init_transaction2_async:g("ta_terminal_init_transaction2_async",["number","number","number"]),login_async:g("ta_terminal_login_async"),logout_async:g("ta_terminal_logout_async"),loyalty_data_async:g("ta_terminal_loyalty_data_async",["number","number","number","number"]),open_dialog_mode_async:g("ta_terminal_open_dialog_mode_async"),open_maintenance_window_async:g("ta_terminal_open_maintenance_window_async"),open_reader_async:g("ta_terminal_open_reader_async"),provide_loyalty_basket_async:g("ta_terminal_provide_loyalty_basket_async",["number"]),provide_vas_result_async:g("ta_terminal_provide_vas_result_async",["number"]),reboot_async:g("ta_terminal_reboot_async"),receipt_request_async:g("ta_terminal_receipt_request_async",["number"]),reconciliation_async:g("ta_terminal_reconciliation_async",["number"]),reconfig_async:g("ta_terminal_reconfig_async"),rollback_async:g("ta_terminal_rollback_async"),send_card_command_async:g("ta_terminal_send_card_command_async",["number"]),show_dialog_async:g("ta_terminal_show_dialog_async",["number"]),show_signature_capture_async:g("ta_terminal_show_signature_capture_async",["number"]),print_on_terminal_async:g("ta_terminal_print_on_terminal_async",["number"]),software_update_async:g("ta_terminal_software_update_async"),start_checkout_async:g("ta_terminal_start_checkout_async"),system_information_async:g("ta_terminal_system_information_async"),transaction_async:g("ta_terminal_transaction_async",["number","number"]),transaction2_async:g("ta_terminal_transaction2_async",["number","number"]),transaction_cashback_async:g("ta_terminal_transaction_cashback_async",["number","number","number"]),transaction_tip_async:g("ta_terminal_transaction_tip_async",["number","number","number"]),device_maintenance_async:g("ta_terminal_device_maintenance_async",["number"]),get_act_seq_counter:g("ta_terminal_get_act_seq_counter",["number"]),get_brands:g("ta_terminal_get_brands",["number"]),get_config_data:g("ta_terminal_get_config_data",["number"]),get_license:g("ta_terminal_get_license",["number"]),can_dcc:g("ta_terminal_can_dcc",["number"]),can_declined_receipts:g("ta_terminal_can_declined_receipts",["number"]),get_ecr_data:g("ta_terminal_get_ecr_data",["number"]),set_ecr_data:g("ta_terminal_set_ecr_data",["number"]),add_ecr_data:g("ta_terminal_add_ecr_data",["number"]),get_features:g("ta_terminal_get_features",["number","number"]),get_merchant_options:g("ta_terminal_get_merchant_options",["number"]),set_merchant_options:g("ta_terminal_set_merchant_options",["number"]),can_multi_account_selection:g("ta_terminal_can_multi_account_selection",["number"]),get_pos_id:g("ta_terminal_get_pos_id",["number"]),set_pos_id:g("ta_terminal_set_pos_id",["number"]),get_print_options:g("ta_terminal_get_print_options",["number"]),set_print_options:g("ta_terminal_set_print_options",["number"]),get_settings:g("ta_terminal_get_settings",["number"]),has_sw_update:g("ta_terminal_has_sw_update",["number"]),get_terminal_id:g("ta_terminal_get_terminal_id",["number"]),get_terminal_status:g("ta_terminal_get_terminal_status",["number"]),get_transaction_data:g("ta_terminal_get_transaction_data",["number"]),set_transaction_data:g("ta_terminal_set_transaction_data",["number"]),get_user_id:g("ta_terminal_get_user_id",["number"]),set_user_id:g("ta_terminal_set_user_id",["number"]),set_receipt_formatter:g("ta_terminal_set_receipt_formatter",["number"])}}return t},terminalSettings:undefined,prepareTerminalSettings:function(){let f=timapi._TimApiHelpers.terminalSettings;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.terminalSettings={create:g("ta_terminal_settings_create"),set_connection_mode:g("ta_terminal_settings_set_connection_mode",["number"]),get_connection_ip_string:g("ta_terminal_settings_get_connection_ip_string",["number"]),set_connection_ip_string:g("ta_terminal_settings_set_connection_ip_string",["number"]),get_connection_ip_port:g("ta_terminal_settings_get_connection_ip_port",["number"]),set_connection_ip_port:g("ta_terminal_settings_set_connection_ip_port",["number"]),get_protocol_type:g("ta_terminal_settings_get_protocol_type",["number"]),set_protocol_type:g("ta_terminal_settings_set_protocol_type",["number"]),get_integrator_id:g("ta_terminal_settings_get_integrator_id",["number"]),set_integrator_id:g("ta_terminal_settings_set_integrator_id",["number"]),get_guides:g("ta_terminal_settings_get_guides",["number"]),set_guides:g("ta_terminal_settings_set_guides",["number"]),get_manufacturer_flags:g("ta_terminal_settings_get_manufacturer_flags",["number"]),set_manufacturer_flags:g("ta_terminal_settings_set_manufacturer_flags",["number"]),is_fetch_brands:g("ta_terminal_settings_is_fetch_brands",["number"]),set_fetch_brands:g("ta_terminal_settings_set_fetch_brands",["number"]),is_auto_commit:g("ta_terminal_settings_is_auto_commit",["number"]),set_auto_commit:g("ta_terminal_settings_set_auto_commit",["number"]),is_auto_shift_management:g("ta_terminal_settings_is_auto_shift_management",["number"]),set_auto_shift_management:g("ta_terminal_settings_set_auto_shift_management",["number"]),is_auto_shutter_management:g("ta_terminal_settings_is_auto_shutter_management",["number"]),set_auto_shutter_management:g("ta_terminal_settings_set_auto_shutter_management",["number"]),get_card_insertion_timeout:g("ta_terminal_settings_get_card_insertion_timeout",["number"]),set_card_insertion_timeout:g("ta_terminal_settings_set_card_insertion_timeout",["number"]),get_card_removal_timeout:g("ta_terminal_settings_get_card_removal_timeout",["number"]),set_card_removal_timeout:g("ta_terminal_settings_set_card_removal_timeout",["number"]),get_commit_timeout:g("ta_terminal_settings_get_commit_timeout",["number"]),set_commit_timeout:g("ta_terminal_settings_set_commit_timeout",["number"]),get_proceed_timeout:g("ta_terminal_settings_get_proceed_timeout",["number"]),set_proceed_timeout:g("ta_terminal_settings_set_proceed_timeout",["number"]),is_dcc:g("ta_terminal_settings_is_dcc",["number"]),set_dcc:g("ta_terminal_settings_set_dcc",["number"]),is_partial_approval:g("ta_terminal_settings_is_partial_approval",["number"]),set_partial_approval:g("ta_terminal_settings_set_partial_approval",["number"]),is_allow_closed_card_insert:g("ta_terminal_settings_is_allow_closed_card_insert",["number"]),set_allow_closed_card_insert:g("ta_terminal_settings_set_allow_closed_card_insert",["number"]),is_tip_allowed:g("ta_terminal_settings_is_tip_allowed",["number"]),set_tip_allowed:g("ta_terminal_settings_set_tip_allowed",["number"]),is_fast_ntf_mode:g("ta_terminal_settings_is_fast_ntf_mode",["number"]),set_fast_ntf_mode:g("ta_terminal_settings_set_fast_ntf_mode",["number"]),get_request_repetition:g("ta_terminal_settings_get_request_repetition",["number"]),set_request_repetition:g("ta_terminal_settings_set_request_repetition",["number"]),is_enabled_keep_alive:g("ta_terminal_settings_is_enabled_keep_alive",["number"]),set_enabled_keep_alive:g("ta_terminal_settings_set_enabled_keep_alive",["number"]),get_persistent_state:g("ta_terminal_settings_get_persistent_state",["number"]),set_persistent_state:g("ta_terminal_settings_set_persistent_state",["number"])}}return f},convertTerminalSettings:function(terminalSettings){if(terminalSettings===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTerminalSettings();let wa_terminal_settings,s;try{wa_terminal_settings=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_terminal_settings.p()));h.assert(f.set_connection_mode(wa_terminal_settings.v(),timapi.constants.ConnectionMode.onFixIp._value));h.assert(f.set_connection_ip_string(wa_terminal_settings.v(),s.replaceOrUndef(terminalSettings.connectionIPString)));h.assert(f.set_connection_ip_port(wa_terminal_settings.v(),terminalSettings.connectionIPPort));h.assert(f.set_protocol_type(wa_terminal_settings.v(),terminalSettings.protocolType._value));h.assert(f.set_integrator_id(wa_terminal_settings.v(),s.replaceOrUndef(terminalSettings.integratorId)));h.assert(f.set_guides(wa_terminal_settings.v(),h.eSet_t_eBit(terminalSettings.guides)));h.assert(f.set_manufacturer_flags(wa_terminal_settings.v(),terminalSettings.manufacturerFlags));h.assert(f.set_fetch_brands(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.fetchBrands)));h.assert(f.set_auto_commit(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.autoCommit)));h.assert(f.set_auto_shift_management(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.autoShiftManagement)));h.assert(f.set_auto_shutter_management(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.autoShutterManagement)));h.assert(f.set_card_insertion_timeout(wa_terminal_settings.v(),terminalSettings.cardInsertionTimeout));h.assert(f.set_card_removal_timeout(wa_terminal_settings.v(),terminalSettings.cardRemovalTimeout));h.assert(f.set_commit_timeout(wa_terminal_settings.v(),terminalSettings.commitTimeout));h.assert(f.set_proceed_timeout(wa_terminal_settings.v(),terminalSettings.proceedTimeout));h.assert(f.set_dcc(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.dcc)));h.assert(f.set_partial_approval(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.partialApproval)));h.assert(f.set_allow_closed_card_insert(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.allowClosedCardInsert)));h.assert(f.set_tip_allowed(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.tipAllowed)));h.assert(f.set_fast_ntf_mode(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.fastNtfMode)));h.assert(f.set_request_repetition(wa_terminal_settings.v(),terminalSettings.requestRepetition));h.assert(f.set_enabled_keep_alive(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.enableKeepAlive)));h.assert(f.set_persistent_state(wa_terminal_settings.v(),h.bc_js_t_c(terminalSettings.persistentState)))}catch(err){if(wa_terminal_settings)wa_terminal_settings.dispose();throw err}finally{if(s)s.dispose()}return wa_terminal_settings},terminalStatus:undefined,unwrapTerminalStatus:function(c_terminal_status){if(c_terminal_status===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.terminalStatus;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.terminalStatus={get_display_content:g("ta_terminal_status_get_display_content",["number"]),get_connection_status:g("ta_terminal_status_get_connection_status",["number"]),get_management_status:g("ta_terminal_status_get_management_status",["number"]),get_card_reader_status:g("ta_terminal_status_get_card_reader_status",["number"]),get_transaction_status:g("ta_terminal_status_get_transaction_status",["number"]),get_sleep_mode_status:g("ta_terminal_status_get_sleep_mode_status",["number"]),get_receipt_information:g("ta_terminal_status_get_receipt_information",["number"]),get_card_data:g("ta_terminal_status_get_card_data",["number"]),get_sw_update_available:g("ta_terminal_status_get_sw_update_available",["number"]),get_final_amount:g("ta_terminal_status_get_final_amount",["number"])}}let p;let status=new timapi.TerminalStatus;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_display_content(c_terminal_status,p.p()));let displayContent=[];h.lit(p.v(),function(c_display_content){displayContent.push(timapi._TimApiHelpers.TAString.unwrapString(c_display_content))});status.displayContent=displayContent;h.assert(f.get_connection_status(c_terminal_status,p.p()));status.connectionStatus=h.ebv(timapi.constants.ConnectionStatus,p.v());h.assert(f.get_management_status(c_terminal_status,p.p()));status.managementStatus=h.ebv(timapi.constants.ManagementStatus,p.v());h.assert(f.get_card_reader_status(c_terminal_status,p.p()));status.cardReaderStatus=h.ebv(timapi.constants.CardReaderStatus,p.v());h.assert(f.get_transaction_status(c_terminal_status,p.p()));status.transactionStatus=h.ebv(timapi.constants.TransactionStatus,p.v());h.assert(f.get_sleep_mode_status(c_terminal_status,p.p()));if(p.isValid()){status.sleepModeStatus=h.ebv(timapi.constants.SleepModeStatus,p.v())}h.assert(f.get_receipt_information(c_terminal_status,p.p()));status.receiptInformation=h.bc_c_t_js(p.v());h.assert(f.get_card_data(c_terminal_status,p.p()));if(p.isValid()){status.cardData=timapi._TimApiHelpers.unwrapCardData(p.v())}h.assert(f.get_sw_update_available(c_terminal_status,p.p()));status.swUpdateAvailable=h.bc_c_t_js(p.v());h.assert(f.get_final_amount(c_terminal_status,p.p()));if(p.isValid()){status.finalAmount=timapi._TimApiHelpers.unwrapAmountFinal(p.v())}}finally{if(p)p.dispose()}return Object.freeze(status)},timeDate:undefined,prepareTimeDate:function(){let f=timapi._TimApiHelpers.timeDate;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.timeDate={create:g("ta_timedate_create",["number"]),get_values:g("ta_timedate_get_values",["number"])}}return f},convertTimeDate:function(timeDate){if(timeDate===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTimeDate();let wa_timedate;let mem_timedate;try{mem_timedate=Module._malloc(24);if(mem_timedate!=0){Module.setValue(mem_timedate,timeDate.year,"i32");Module.setValue(mem_timedate+4,timeDate.month,"i32");Module.setValue(mem_timedate+8,timeDate.day,"i32");Module.setValue(mem_timedate+12,timeDate.hour,"i32");Module.setValue(mem_timedate+16,timeDate.minute,"i32");Module.setValue(mem_timedate+20,timeDate.second,"i32")}wa_timedate=new timapi._TimApiHelpers.TAObject;h.assert(f.create(wa_timedate.p(),mem_timedate))}catch(err){if(wa_timedate)wa_timedate.dispose();throw err}finally{if(mem_timedate)Module._free(mem_timedate)}return wa_timedate},unwrapTimeDate:function(c_timedate){if(c_timedate===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTimeDate();let timeDate=new timapi.TimeDate;let p;try{p=Module._malloc(24);h.assert(f.get_values(c_timedate,p));if(p!=0){timeDate.year=Module.getValue(p,"i32");timeDate.month=Module.getValue(p+4,"i32");timeDate.day=Module.getValue(p+8,"i32");timeDate.hour=Module.getValue(p+12,"i32");timeDate.minute=Module.getValue(p+16,"i32");timeDate.second=Module.getValue(p+20,"i32")}}finally{if(p)Module._free(p)}return timeDate},subTransaction:undefined,prepareSubTransaction:function(){let f=timapi._TimApiHelpers.subTransaction;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.subTransaction={create:g("ta_sub_transaction_create",["number"]),set_function:g("ta_sub_transaction_set_function",["number"]),set_amount:g("ta_sub_transaction_set_amount",["number"]),set_rate:g("ta_sub_transaction_set_rate",["number"]),set_rate_exponent:g("ta_sub_transaction_set_rate_exponent",["number"])}}return f},convertSubTransaction:function(subTransaction){if(subTransaction===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareSubTransaction();let wa_subTransaction,wa_amount;try{wa_subTransaction=new timapi._TimApiHelpers.TAObject;h.assert(f.create(wa_subTransaction.p()));h.assert(f.set_function(wa_subTransaction.v(),subTransaction.function._value));h.assert(f.set_rate(wa_subTransaction.v(),subTransaction.rate));h.assert(f.set_rate_exponent(wa_subTransaction.v(),subTransaction.rateExponent));wa_amount=new timapi._TimApiHelpers.TAObject;wa_amount.takeover(timapi._TimApiHelpers.convertAmount(subTransaction.amount));h.assert(f.set_amount(wa_subTransaction.v(),wa_amount.v()))}catch(err){if(wa_subTransaction)wa_subTransaction.dispose();throw err}finally{if(wa_amount)wa_amount.dispose()}return wa_subTransaction},convertSubTransactions:function(subTransactions){if(subTransactions===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareList();let wa_list,wa_item;try{wa_list=new timapi._TimApiHelpers.TAObject;h.assert(f.create(wa_list.p()));wa_item=new timapi._TimApiHelpers.TAObject;subTransactions.forEach(function(subTransaction){wa_item.takeover(timapi._TimApiHelpers.convertSubTransaction(subTransaction));h.assert(f.add(wa_list.v(),wa_item.v()))})}finally{if(wa_item)wa_item.dispose()}return wa_list},timError:undefined,prepareTimError:function(){let f=timapi._TimApiHelpers.timError;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.timError={get_result_code:g("ta_tim_error_get_result_code",["number"]),get_error_message:g("ta_tim_error_get_error_message",["number"]),get_native_error:g("ta_tim_error_get_native_error",["number"]),get_ppinfo:g("ta_tim_error_get_ppinfo",["number"]),get_additional_error_info:g("ta_tim_error_get_additional_error_info",["number"]),get_print_data:g("ta_tim_error_get_print_data",["number"]),get_rejected_basket:g("ta_tim_error_get_rejected_basket",["number"])}}return f},unwrapTimError:function(c_tim_error,resultCode){let timException;if(c_tim_error===0){if(resultCode!==undefined&&resultCode._value!==timapi.constants.ResultCode.ok._value){timException=new timapi.TimException(resultCode)}return timException}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTimError();let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_error_message(c_tim_error,p.p()));let errorText=h.contentOfIfValid(p);timException=new timapi.TimException(resultCode,errorText);h.assert(f.get_result_code(c_tim_error,p.p()));timException.resultCode=h.ebv(timapi.constants.ResultCode,p.v());h.assert(f.get_native_error(c_tim_error,p.p()));timException.nativeError=timapi._TimApiHelpers.unwrapNativeError(p.v());h.assert(f.get_ppinfo(c_tim_error,p.p()));timException.ppInfo=timapi._TimApiHelpers.unwrapPpInfo(p.v());h.assert(f.get_additional_error_info(c_tim_error,p.p()));timException.additionalErrorInfo=timapi._TimApiHelpers.unwrapSSMap(p.v());h.assert(f.get_print_data(c_tim_error,p.p()));timException.printData=timapi._TimApiHelpers.unwrapPrintData(p.v());h.assert(f.get_rejected_basket(c_tim_error,p.p()));timException.rejectedBasket=timapi._TimApiHelpers.unwrapBasket(p.v())}finally{if(p)p.dispose()}return timException},total:undefined,prepareTotal:function(){let f=timapi._TimApiHelpers.total;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.total={get_count:g("ta_total_get_count",["number"]),get_amount_sum:g("ta_total_get_amount_sum",["number"]),get_amount_sum_tip:g("ta_total_get_amount_sum_tip",["number"]),get_amount_sum_other:g("ta_total_get_amount_sum_other",["number"]),get_trx_details:g("ta_total_get_trx_details",["number"]),get_currency:g("ta_total_get_currency",["number"]),get_exponent:g("ta_total_get_exponent",["number"])}}return f},unwrapTotal:function(c_total){if(c_total===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTotal();let total=new timapi.Total;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_count(c_total,p.p()));total.count=p.v();h.assert(f.get_amount_sum(c_total,p.p()));total.amountSum=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_sum_tip(c_total,p.p()));total.amountSumTip=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_sum_other(c_total,p.p()));total.amountSumOther=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_trx_details(c_total,p.p()));let trxDetails=[];h.lit(p.v(),function(c_trx_details){trxDetails.push(timapi._TimApiHelpers.unwrapTrxDetail(c_trx_details))});total.trxDetails=trxDetails;h.assert(f.get_currency(c_total,p.p()));total.currency=h.cbv(timapi.constants.Currency,p.v()-1);h.assert(f.get_exponent(c_total,p.p()));total.exponent=p.v()}finally{if(p)p.dispose()}return Object.freeze(total)},transactionData:undefined,prepareTransactionData:function(){let f=timapi._TimApiHelpers.transactionData;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.transactionData={create:g("ta_transaction_data_create",[]),get_dcc_allowed:g("ta_transaction_data_get_dcc_allowed",["number"]),set_dcc_allowed:g("ta_transaction_data_set_dcc_allowed",["number"]),get_trx_original_date:g("ta_transaction_data_get_trx_original_date",["number"]),set_trx_original_date:g("ta_transaction_data_set_trx_original_date",["number"]),get_ecr_seq_counter:g("ta_transaction_data_get_ecr_seq_counter",["number"]),set_ecr_seq_counter:g("ta_transaction_data_set_ecr_seq_counter",["number"]),get_partial_approval_allowed:g("ta_transaction_data_get_partial_approval_allowed",["number"]),set_partial_approval_allowed:g("ta_transaction_data_set_partial_approval_allowed",["number"]),get_trans_ref:g("ta_transaction_data_get_trans_ref",["number"]),set_trans_ref:g("ta_transaction_data_set_trans_ref",["number"]),get_trans_seq:g("ta_transaction_data_get_trans_seq",["number"]),set_trans_seq:g("ta_transaction_data_set_trans_seq",["number"]),get_card_ref:g("ta_transaction_data_get_card_ref",["number"]),set_card_ref:g("ta_transaction_data_set_card_ref",["number"]),get_acq_id:g("ta_transaction_data_get_acq_id",["number"]),set_acq_id:g("ta_transaction_data_set_acq_id",["number"]),get_acq_trans_ref:g("ta_transaction_data_get_acq_trans_ref",["number"]),set_acq_trans_ref:g("ta_transaction_data_set_acq_trans_ref",["number"]),get_trm_trans_ref:g("ta_transaction_data_get_trm_trans_ref",["number"]),set_trm_trans_ref:g("ta_transaction_data_set_trm_trans_ref",["number"]),get_tip_allowed:g("ta_transaction_data_get_tip_allowed",["number"]),set_tip_allowed:g("ta_transaction_data_set_tip_allowed",["number"]),get_phone_auth_code:g("ta_transaction_data_get_phone_auth_code",["number"]),set_phone_auth_code:g("ta_transaction_data_set_phone_auth_code",["number"]),get_language:g("ta_transaction_data_get_language",["number"]),set_language:g("ta_transaction_data_set_language",["number"]),get_multi_currency_flag:g("ta_transaction_data_get_multi_currency_flag",["number"]),set_multi_currency_flag:g("ta_transaction_data_set_multi_currency_flag",["number"]),get_ngv_mode:g("ta_transaction_data_get_ngv_mode",["number"]),set_ngv_mode:g("ta_transaction_data_set_ngv_mode",["number"]),get_ngv_clearing_delay:g("ta_transaction_data_get_ngv_clearing_delay",["number"]),set_ngv_clearing_delay:g("ta_transaction_data_set_ngv_clearing_delay",["number"]),get_cvc2:g("ta_transaction_data_get_cvc2",["number"]),set_cvc2:g("ta_transaction_data_set_cvc2",["number"]),get_app_expiration_date:g("ta_transaction_data_get_app_expiration_date",["number"]),set_app_expiration_date:g("ta_transaction_data_set_app_expiration_date",["number"]),get_six_trx_ref_num:g("ta_transaction_data_get_six_trx_ref_num",["number"]),set_six_trx_ref_num:g("ta_transaction_data_set_six_trx_ref_num",["number"]),get_installment_allowed:g("ta_transaction_data_get_installment_allowed",["number"]),set_installment_allowed:g("ta_transaction_data_set_installment_allowed",["number"]),get_deferred_auth_ind:g("ta_transaction_data_get_deferred_auth_ind",["number"]),set_deferred_auth_ind:g("ta_transaction_data_set_deferred_auth_ind",["number"]),get_transaction_reason:g("ta_transaction_data_get_transaction_reason",["number"]),set_transaction_reason:g("ta_transaction_data_set_transaction_reason",["number"]),get_sub_transactions:g("ta_transaction_data_get_sub_transactions",["number"]),set_sub_transactions:g("ta_transaction_data_set_sub_transactions",["number"])}}return f},convertTransactionData:function(transactionData){if(transactionData===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTransactionData();let wa_transaction_data,s,i;let wa_timedate,wa_appExpirationDate,wa_subTransactions;try{wa_transaction_data=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;i=new timapi._TimApiHelpers.TAInteger;h.assert(f.create(wa_transaction_data.p()));h.assert(f.set_dcc_allowed(wa_transaction_data.v(),h.bc_js_t_c(transactionData.dccAllowed)));wa_timedate=timapi._TimApiHelpers.convertTimeDate(transactionData.trxOriginalDate);h.assert(f.set_trx_original_date(wa_transaction_data.v(),wa_timedate.v()));h.assert(f.set_ecr_seq_counter(wa_transaction_data.v(),i.replaceOrUndef(transactionData.ecrSeqCounter)));h.assert(f.set_partial_approval_allowed(wa_transaction_data.v(),h.bc_js_t_c(transactionData.partialApprovalAllowed)));h.assert(f.set_trans_ref(wa_transaction_data.v(),i.replaceOrUndef(transactionData.transRef)));h.assert(f.set_trans_seq(wa_transaction_data.v(),i.replaceOrUndef(transactionData.transSeq)));h.assert(f.set_card_ref(wa_transaction_data.v(),s.replaceOrUndef(transactionData.cardRef)));h.assert(f.set_acq_id(wa_transaction_data.v(),i.replaceOrUndef(transactionData.acqId)));h.assert(f.set_acq_trans_ref(wa_transaction_data.v(),s.replaceOrUndef(transactionData.acqTransRef)));h.assert(f.set_trm_trans_ref(wa_transaction_data.v(),s.replaceOrUndef(transactionData.trmTransRef)));h.assert(f.set_tip_allowed(wa_transaction_data.v(),h.bc_js_t_c(transactionData.tipAllowed)));h.assert(f.set_phone_auth_code(wa_transaction_data.v(),s.replaceOrUndef(transactionData.phoneAuthCode)));h.assert(f.set_language(wa_transaction_data.v(),s.replaceOrUndef(transactionData.language)));h.assert(f.set_multi_currency_flag(wa_transaction_data.v(),h.bc_js_t_c(transactionData.multiCurrencyFlag)));if(transactionData.ngvMode){h.assert(f.set_ngv_mode(wa_transaction_data.v(),transactionData.ngvMode._value))}h.assert(f.set_ngv_clearing_delay(wa_transaction_data.v(),transactionData.ngvClearingDelay));h.assert(f.set_cvc2(wa_transaction_data.v(),i.replaceOrUndef(transactionData.cvc2)));wa_appExpirationDate=timapi._TimApiHelpers.convertTimeDate(transactionData.appExpirationDate);h.assert(f.set_app_expiration_date(wa_transaction_data.v(),wa_appExpirationDate.v()));h.assert(f.set_six_trx_ref_num(wa_transaction_data.v(),s.replaceOrUndef(transactionData.sixTrxRefNum)));h.assert(f.set_installment_allowed(wa_transaction_data.v(),h.bc_js_t_c(transactionData.installmentAllowed)));h.assert(f.set_deferred_auth_ind(wa_transaction_data.v(),h.bc_js_t_c(transactionData.deferredAuthInd)));if(transactionData.transactionReason){h.assert(f.set_transaction_reason(wa_transaction_data.v(),transactionData.transactionReason._value))}wa_subTransactions=timapi._TimApiHelpers.convertSubTransactions(transactionData.subTransactions);h.assert(f.set_sub_transactions(wa_transaction_data.v(),wa_subTransactions.v()))}catch(err){if(wa_transaction_data)wa_transaction_data.dispose();throw err}finally{if(s)s.dispose();if(i)i.dispose();if(wa_timedate)wa_timedate.dispose();if(wa_appExpirationDate)wa_appExpirationDate.dispose();if(wa_subTransactions)wa_subTransactions.dispose()}return wa_transaction_data},unwrapTransactionData:function(c_transaction_data){if(c_transaction_data===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTransactionData();let transactionData=new timapi.TransactionData;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_dcc_allowed(c_transaction_data,p.p()));transactionData.dccAllowed=h.bc_c_t_js(p.v());h.assert(f.get_trx_original_date(c_transaction_data,p.p()));transactionData.trxOriginalDate=timapi._TimApiHelpers.unwrapTimeDate(p.v());h.assert(f.get_ecr_seq_counter(c_transaction_data,p.p()));transactionData.ecrSeqCounter=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_partial_approval_allowed(c_transaction_data,p.p()));transactionData.partialApprovalAllowed=h.bc_c_t_js(p.v());h.assert(f.get_trans_ref(c_transaction_data,p.p()));transactionData.transRef=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_trans_seq(c_transaction_data,p.p()));transactionData.transSeq=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_card_ref(c_transaction_data,p.p()));transactionData.cardRef=h.contentOfIfValid(p);h.assert(f.get_acq_id(c_transaction_data,p.p()));transactionData.acqId=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_acq_trans_ref(c_transaction_data,p.p()));transactionData.acqTransRef=h.contentOfIfValid(p);h.assert(f.get_trm_trans_ref(c_transaction_data,p.p()));transactionData.trmTransRef=h.contentOfIfValid(p);h.assert(f.get_tip_allowed(c_transaction_data,p.p()));transactionData.tipAllowed=h.bc_c_t_js(p.v());h.assert(f.get_phone_auth_code(c_transaction_data,p.p()));transactionData.phoneAuthCode=h.contentOfIfValid(p);h.assert(f.get_language(c_transaction_data,p.p()));transactionData.language=h.contentOfIfValid(p);h.assert(f.get_multi_currency_flag(c_transaction_data,p.p()));transactionData.multiCurrencyFlag=h.bc_c_t_js(p.v());h.assert(f.get_ngv_mode(c_transaction_data,p.p()));transactionData.ngvMode=h.ebv(timapi.constants.NgvMode,p.v());h.assert(f.get_ngv_clearing_delay(c_transaction_data,p.p()));transactionData.ngvClearingDelay=p.v();h.assert(f.get_six_trx_ref_num(c_transaction_data,p.p()));transactionData.sixTrxRefNum=h.contentOfIfValid(p);h.assert(f.get_installment_allowed(c_transaction_data,p.p()));transactionData.installmentAllowed=h.contentOfIfValid(p);h.assert(f.get_deferred_auth_ind(c_transaction_data,p.p()));transactionData.deferredAuthInd=h.contentOfIfValid(p);h.assert(f.get_transaction_reason(c_transaction_data,p.p()));transactionData.transactionReason=h.ebv(timapi.constants.TransactionReason,p.v())}finally{if(p)p.dispose()}return transactionData},transactionInformation:undefined,prepareTransactionInformation:function(){let f=timapi._TimApiHelpers.transactionInformation;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.transactionInformation={get_pos_entry_mode:g("ta_transaction_information_get_pos_entry_mode",["number"]),get_cvm:g("ta_transaction_information_get_cvm",["number"]),get_merchant_action:g("ta_transaction_information_get_merchant_action",["number"]),get_auth_code:g("ta_transaction_information_get_auth_code",["number"]),get_time_stamp:g("ta_transaction_information_get_time_stamp",["number"]),get_trans_ref:g("ta_transaction_information_get_trans_ref",["number"]),get_trans_seq:g("ta_transaction_information_get_trans_seq",["number"]),get_acq:g("ta_transaction_information_get_acq",["number"]),get_signature_information:g("ta_transaction_information_get_signature_information",["number"]),get_trm_trans_ref:g("ta_transaction_information_get_trm_trans_ref",["number"]),get_acq_trans_ref:g("ta_transaction_information_get_acq_trans_ref",["number"]),get_six_trx_ref_num:g("ta_transaction_information_get_six_trx_ref_num",["number"]),get_cardholder_name:g("ta_transaction_information_get_cardholder_name",["number"]),get_client_identifier:g("ta_transaction_information_get_client_identifier",["number"]),get_account_number:g("ta_transaction_information_get_account_number",["number"]),get_person_oid:g("ta_transaction_information_get_person_oid",["number"]),get_card_id:g("ta_transaction_information_get_card_id",["number"]),get_ngv_used_flag:g("ta_transaction_information_get_ngv_used_flag",["number"])}}return f},unwrapTransactionInformation:function(c_transaction_information){if(c_transaction_information===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTransactionInformation();let transactionInformation=new timapi.TransactionInformation;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_pos_entry_mode(c_transaction_information,p.p()));transactionInformation.posEntryMode=h.ebv(timapi.constants.PosEntryMode,p.v());h.assert(f.get_cvm(c_transaction_information,p.p()));transactionInformation.cvm=h.ebv(timapi.constants.Cvm,p.v());h.assert(f.get_merchant_action(c_transaction_information,p.p()));transactionInformation.merchantAction=h.ebv(timapi.constants.MerchantAction,p.v());h.assert(f.get_auth_code(c_transaction_information,p.p()));transactionInformation.authCode=h.contentOfIfValid(p);h.assert(f.get_time_stamp(c_transaction_information,p.p()));transactionInformation.timeStamp=timapi._TimApiHelpers.unwrapTimeDate(p.v());h.assert(f.get_trans_ref(c_transaction_information,p.p()));transactionInformation.transRef=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_trans_seq(c_transaction_information,p.p()));transactionInformation.transSeq=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_acq(c_transaction_information,p.p()));transactionInformation.acqId=p.v()!==0?p.v():undefined;h.assert(f.get_signature_information(c_transaction_information,p.p()));transactionInformation.signatureInformation=timapi._TimApiHelpers.unwrapSignatureInformation(p.v());h.assert(f.get_trm_trans_ref(c_transaction_information,p.p()));transactionInformation.trmTransRef=h.contentOfIfValid(p);h.assert(f.get_acq_trans_ref(c_transaction_information,p.p()));transactionInformation.acqTransRef=h.contentOfIfValid(p);h.assert(f.get_six_trx_ref_num(c_transaction_information,p.p()));transactionInformation.sixTrxRefNum=h.contentOfIfValid(p);h.assert(f.get_cardholder_name(c_transaction_information,p.p()));transactionInformation.cardholderName=h.contentOfIfValid(p);h.assert(f.get_client_identifier(c_transaction_information,p.p()));transactionInformation.clientIdentifier=h.contentOfIfValid(p);h.assert(f.get_account_number(c_transaction_information,p.p()));transactionInformation.accountNumber=h.contentOfIfValid(p);h.assert(f.get_person_oid(c_transaction_information,p.p()));transactionInformation.personOid=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_card_id(c_transaction_information,p.p()));transactionInformation.cardId=h.contentOfIfValid(p);h.assert(f.get_ngv_used_flag(c_transaction_information,p.p()));transactionInformation.ngvUsedFlag=h.bc_c_t_js(p.v())}finally{if(p)p.dispose()}return Object.freeze(transactionInformation)},transactionRequest:undefined,prepareTransactionRequest:function(){let f=timapi._TimApiHelpers.transactionRequest;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.transactionRequest={create:g("ta_transaction_request_create",[]),set_user_id:g("ta_transaction_request_set_user_id",["number"]),set_amount:g("ta_transaction_request_set_amount",["number"]),set_transaction_data:g("ta_transaction_request_set_transaction_data",["number"]),set_merchant_options:g("ta_transaction_request_set_merchant_options",["number"]),set_amount_discount:g("ta_transaction_request_set_amount_discount",["number"]),set_amount_tip:g("ta_transaction_request_set_amount_tip",["number"]),set_basket:g("ta_transaction_request_set_basket",["number"]),set_additional_info:g("ta_transaction_request_set_additional_info",["number"]),set_customer_data:g("ta_transaction_request_set_customer_data",["number"]),set_amount_other:g("ta_transaction_request_set_amount_other",["number"]),set_retain_card:g("ta_transaction_request_set_retain_card",["number"]),set_loyalty_coupon_list:g("ta_transaction_request_set_loyalty_coupon_list",["number"])}}return f},convertTransactionRequest:function(transactionRequest){if(transactionRequest===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTransactionRequest();let f_list=timapi._TimApiHelpers.prepareList();let wa_transaction_request,wa_obj,wa_merchant_options,wa_loyalty_coupon_list,i;try{wa_transaction_request=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;wa_merchant_options=new timapi._TimApiHelpers.TAObject;wa_loyalty_coupon_list=new timapi._TimApiHelpers.TAObject;i=new timapi._TimApiHelpers.TAInteger;h.assert(f.create(wa_transaction_request.p()));h.assert(f.set_user_id(wa_transaction_request.v(),i.replaceOrUndef(transactionRequest.userId)));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(transactionRequest.amount));h.assert(f.set_amount(wa_transaction_request.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertTransactionData(transactionRequest.transactionData));h.assert(f.set_transaction_data(wa_transaction_request.v(),wa_obj.v()));h.assert(f_list.create(wa_merchant_options.p()));for(let merchantOption of transactionRequest.merchantOptions){wa_obj.takeover(timapi._TimApiHelpers.convertMerchantOption(merchantOption));h.assert(f_list.add(wa_merchant_options.v(),wa_obj.v()))}h.assert(f.set_merchant_options(wa_transaction_request.v(),wa_merchant_options.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmountDiscount(transactionRequest.amountDiscount));h.assert(f.set_amount_discount(wa_transaction_request.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(transactionRequest.amountTip));h.assert(f.set_amount_tip(wa_transaction_request.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertBasket(transactionRequest.basket));h.assert(f.set_basket(wa_transaction_request.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertNSMap(transactionRequest.additionalInfo));h.assert(f.set_additional_info(wa_transaction_request.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertESMap(transactionRequest.customerData));h.assert(f.set_customer_data(wa_transaction_request.v(),wa_obj.v()));wa_obj.takeover(timapi._TimApiHelpers.convertAmount(transactionRequest.amountOther));h.assert(f.set_amount_other(wa_transaction_request.v(),wa_obj.v()));h.assert(f.set_retain_card(wa_transaction_request.v(),h.bc_js_t_c(transactionRequest.retainCard)));h.assert(f_list.create(wa_loyalty_coupon_list.p()));for(let loyaltyCoupon of transactionRequest.loyaltyCouponList){wa_obj.takeover(timapi._TimApiHelpers.convertLoyaltyCoupon(loyaltyCoupon));h.assert(f_list.add(wa_loyalty_coupon_list.v(),wa_obj.v()))}h.assert(f.set_loyalty_coupon_list(wa_transaction_request.v(),wa_loyalty_coupon_list.v()))}catch(err){if(wa_transaction_request)wa_transaction_request.dispose();throw err}finally{if(i)i.dispose();if(wa_obj)wa_obj.dispose();if(wa_merchant_options)wa_merchant_options.dispose();if(wa_loyalty_coupon_list)wa_loyalty_coupon_list.dispose()}return wa_transaction_request},transactionResponse:undefined,prepareTransactionResponse:function(){let f=timapi._TimApiHelpers.transactionResponse;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.transactionResponse={get_type:g("ta_transaction_response_get_type",["number"]),get_amount:g("ta_transaction_response_get_amount",["number"]),get_amount_other:g("ta_transaction_response_get_amount_other",["number"]),get_amount_due:g("ta_transaction_response_get_amount_due",["number"]),get_amount_dcc:g("ta_transaction_response_get_amount_dcc",["number"]),get_amount_saldo:g("ta_transaction_response_get_amount_saldo",["number"]),get_amount_loyalty_cashback:g("ta_transaction_response_get_amount_loyalty_cashback",["number"]),get_transaction_information:g("ta_transaction_response_get_transaction_information",["number"]),get_dcc_disclaimer:g("ta_transaction_response_get_dcc_disclaimer",["number"]),get_card_data:g("ta_transaction_response_get_card_data",["number"]),get_print_data:g("ta_transaction_response_get_print_data",["number"]),get_amount_tip:g("ta_transaction_response_get_amount_tip",["number"]),get_additional_info:g("ta_transaction_response_get_additional_info",["number"]),get_basket:g("ta_transaction_response_get_basket",["number"])}}return f},unwrapTransactionResponse:function(c_transaction_response){if(c_transaction_response===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTransactionResponse();let transactionResponse=new timapi.TransactionResponse;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_type(c_transaction_response,p.p()));transactionResponse.transactionType=h.ebv(timapi.constants.TransactionType,p.v());h.assert(f.get_amount(c_transaction_response,p.p()));transactionResponse.amount=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_other(c_transaction_response,p.p()));transactionResponse.amountOther=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_due(c_transaction_response,p.p()));transactionResponse.amountDue=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_dcc(c_transaction_response,p.p()));transactionResponse.amountDcc=timapi._TimApiHelpers.unwrapAmountDcc(p.v());h.assert(f.get_amount_saldo(c_transaction_response,p.p()));transactionResponse.amountSaldo=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_loyalty_cashback(c_transaction_response,p.p()));transactionResponse.amountLoyaltyCashback=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_transaction_information(c_transaction_response,p.p()));transactionResponse.transactionInformation=timapi._TimApiHelpers.unwrapTransactionInformation(p.v());h.assert(f.get_dcc_disclaimer(c_transaction_response,p.p()));transactionResponse.dccDisclaimer=h.contentOfIfValid(p);h.assert(f.get_card_data(c_transaction_response,p.p()));transactionResponse.cardData=timapi._TimApiHelpers.unwrapCardData(p.v());h.assert(f.get_print_data(c_transaction_response,p.p()));transactionResponse.printData=timapi._TimApiHelpers.unwrapPrintData(p.v());h.assert(f.get_amount_tip(c_transaction_response,p.p()));transactionResponse.amountTip=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_additional_info(c_transaction_response,p.p()));transactionResponse.additionalInfo=timapi._TimApiHelpers.unwrapNSMap(p.v());h.assert(f.get_basket(c_transaction_response,p.p()));transactionResponse.basket=timapi._TimApiHelpers.unwrapBasket(p.v())}finally{if(p)p.dispose()}return Object.freeze(transactionResponse)},trxDetail:undefined,prepareTrxDetail:function(){let f=timapi._TimApiHelpers.trxDetail;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.trxDetail={get_dcc_flag:g("ta_trx_detail_get_dcc_flag",["number"]),get_transaction_type:g("ta_trx_detail_get_transaction_type",["number"]),get_count:g("ta_trx_detail_get_count",["number"]),get_amount_sum:g("ta_trx_detail_get_amount_sum",["number"]),get_amount_sum_tip:g("ta_trx_detail_get_amount_sum_tip",["number"]),get_amount_sum_other:g("ta_trx_detail_get_amount_sum_other",["number"]),get_aid:g("ta_trx_detail_get_aid",["number"]),get_markup:g("ta_trx_detail_get_markup",["number"]),get_markup_exponent:g("ta_trx_detail_get_markup_exponent",["number"]),get_ngvused_flag:g("ta_trx_detail_get_ngvused_flag",["number"])}}return f},unwrapTrxDetail:function(c_trx_detail){if(c_trx_detail===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareTrxDetail();let trxDetail=new timapi.TrxDetail;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_dcc_flag(c_trx_detail,p.p()));trxDetail.dccFlag=h.bc_c_t_js(p.v());h.assert(f.get_transaction_type(c_trx_detail,p.p()));trxDetail.transactionType=h.ebv(timapi.constants.TransactionType,p.v());h.assert(f.get_count(c_trx_detail,p.p()));trxDetail.count=p.v();h.assert(f.get_amount_sum(c_trx_detail,p.p()));trxDetail.amountSum=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_sum_tip(c_trx_detail,p.p()));trxDetail.amountSumTip=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_amount_sum_other(c_trx_detail,p.p()));trxDetail.amountSumOther=timapi._TimApiHelpers.unwrapAmount(p.v());h.assert(f.get_aid(c_trx_detail,p.p()));trxDetail.aid=h.contentOfIfValid(p);h.assert(f.get_markup(c_trx_detail,p.p()));trxDetail.markup=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_markup_exponent(c_trx_detail,p.p()));trxDetail.markupExponent=timapi._TimApiHelpers.unwrapInteger(p.v());h.assert(f.get_ngvused_flag(c_trx_detail,p.p()));trxDetail.ngvUsedFlag=h.bc_c_t_js(p.v())}finally{if(p)p.dispose()}return Object.freeze(trxDetail)},vasCheckoutInformation:undefined,prepareVasCheckoutInformation:function(){let f=timapi._TimApiHelpers.vasCheckoutInformation;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.vasCheckoutInformation={get_loyalty_coupons:g("ta_vas_checkout_information_get_loyalty_coupons",["number"]),get_loyalty_information:g("ta_vas_checkout_information_get_loyalty_information",["number"]),get_provide_basket:g("ta_vas_checkout_information_get_provide_basket",["number"]),get_vas_information:g("ta_vas_checkout_information_get_vas_information",["number"])}}return f},unwrapVasCheckoutInformation:function(c_vas_checkout_information){if(c_vas_checkout_information===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareVasCheckoutInformation();let vasCheckoutInformation=new timapi.VasCheckoutInformation;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_loyalty_coupons(c_vas_checkout_information,p.p()));let loyaltyCoupons=[];h.lit(p.v(),function(c_loyalty_coupon){loyaltyCoupons.push(timapi._TimApiHelpers.unwrapLoyaltyCoupon(c_loyalty_coupon))});vasCheckoutInformation.loyaltyCoupons=loyaltyCoupons;h.assert(f.get_loyalty_information(c_vas_checkout_information,p.p()));let loyaltyInformation=[];h.lit(p.v(),function(c_loyalty_information){loyaltyInformation.push(timapi._TimApiHelpers.unwrapLoyaltyInformation(c_loyalty_information))});vasCheckoutInformation.loyaltyInformation=loyaltyInformation;h.assert(f.get_provide_basket(c_vas_checkout_information,p.p()));vasCheckoutInformation.provideBasket=h.bc_c_t_js(p.v());h.assert(f.get_vas_information(c_vas_checkout_information,p.p()));vasCheckoutInformation.vasInformation=timapi._TimApiHelpers.unwrapSBMap(p.v())}finally{if(p)p.dispose()}return vasCheckoutInformation},screenshotInformation:undefined,prepareScreenshotInformation:function(){let f=timapi._TimApiHelpers.screenshotInformation;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.screenshotInformation={get_image_file_format:g("ta_screenshot_information_get_image_file_format",["number"]),get_image_width:g("ta_screenshot_information_get_image_width",["number"]),get_image_height:g("ta_screenshot_information_get_image_height",["number"]),get_image_data:g("ta_screenshot_information_get_image_data",["number"])}}return f},unwrapScreenshotInformation:function(c_screenshot_information){if(c_screenshot_information===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareScreenshotInformation();let screenshotInformation=new timapi.ScreenshotInformation;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_image_file_format(c_screenshot_information,p.p()));screenshotInformation.imageFileFormat=h.ebv(timapi.constants.ImageFileFormat,p.v());h.assert(f.get_image_width(c_screenshot_information,p.p()));screenshotInformation.imageWidth=p.v();h.assert(f.get_image_height(c_screenshot_information,p.p()));screenshotInformation.imageHeight=p.v();h.assert(f.get_image_data(c_screenshot_information,p.p()));screenshotInformation.imageData=timapi._TimApiHelpers.getBinaryContent(p)}finally{if(p)p.dispose()}return screenshotInformation},terminalGetActSeqCounter:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let p;let actSeqCounter;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(t.get_act_seq_counter(terminal._wao.v(),p.p()));actSeqCounter=p.v()}finally{if(p)p.dispose()}return actSeqCounter},terminalGetBrands:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let brands=[];let wa_list;try{wa_list=new timapi._TimApiHelpers.TAObject;h.assert(t.get_brands(terminal._wao.v(),wa_list.p()));h.lit(wa_list.v(),function(c_brand){brands.push(timapi._TimApiHelpers.unwrapBrand(c_brand))})}finally{if(wa_list)wa_list.dispose()}return brands},terminalGetConfigData:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let configData;let wa_config_data;try{wa_config_data=new timapi._TimApiHelpers.TAObject;h.assert(t.get_config_data(terminal._wao.v(),wa_config_data.p()));configData=timapi._TimApiHelpers.unwrapConfigData(wa_config_data.v())}finally{if(wa_config_data)wa_config_data.dispose()}return configData},terminalGetLicense:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let license;let wa_license;try{wa_license=new timapi._TimApiHelpers.TAObject;h.assert(t.get_license(terminal._wao.v(),wa_license.p()));license=timapi._TimApiHelpers.getBinaryContent(wa_license)}finally{if(wa_license)wa_license.dispose()}return license},terminalCanDcc:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let p;let canDcc;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(t.can_dcc(terminal._wao.v(),p.p()));canDcc=h.bc_c_t_js(p.v())}finally{if(p)p.dispose()}return canDcc},terminalCanDeclinedReceipts:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let p;let canDeclinedReceipts;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(t.can_declined_receipts(terminal._wao.v(),p.p()));canDeclinedReceipts=h.bc_c_t_js(p.v())}finally{if(p)p.dispose()}return canDeclinedReceipts},terminalGetEcrData:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f=timapi._TimApiHelpers.prepareEcrInfo();let ecrData=[];let wa_list,p;try{wa_list=new timapi._TimApiHelpers.TAObject;p=new timapi._TimApiHelpers.TAPointer;h.assert(t.get_ecr_data(terminal._wao.v(),wa_list.p()));h.lit(wa_list.v(),function(c_ecr_info){let ecrInfo=new timapi.EcrInfo;h.assert(f.get_type(c_ecr_info,p.p()));ecrInfo.type=h.ebv(timapi.constants.EcrInfoType,p.v());h.assert(f.get_name(c_ecr_info,p.p()));ecrInfo.name=h.contentOfIfValid(p);h.assert(f.get_manufacturer_name(c_ecr_info,p.p()));ecrInfo.manufacturerName=h.contentOfIfValid(p);h.assert(f.get_version(c_ecr_info,p.p()));ecrInfo.version=h.contentOfIfValid(p);h.assert(f.get_serial_number(c_ecr_info,p.p()));ecrInfo.serialNumber=h.contentOfIfValid(p);h.assert(f.get_architecture(c_ecr_info,p.p()));ecrInfo.architecture=h.contentOfIfValid(p);h.assert(f.get_integrator_solution(c_ecr_info,p.p()));ecrInfo.integratorSolution=h.contentOfIfValid(p);h.assert(f.get_remote_ip(c_ecr_info,p.p()));ecrInfo.remoteIp=h.contentOfIfValid(p);ecrData.push(ecrInfo)})}finally{if(wa_list)wa_list.dispose();if(p)p.dispose()}return ecrData},terminalSetEcrData:function(terminal,ecrData){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f=timapi._TimApiHelpers.prepareEcrInfo();let f_list=timapi._TimApiHelpers.prepareList();let wa_list,wa_ecr_info,s;try{wa_list=new timapi._TimApiHelpers.TAObject;wa_ecr_info=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;h.assert(f_list.create(wa_list.p()));for(let ecrInfo of ecrData){wa_ecr_info.releaseIfValid();h.assert(f.create(wa_ecr_info.p()));h.assert(f.set_type(wa_ecr_info.v(),ecrInfo.type._value));h.assert(f.set_name(wa_ecr_info.v(),s.replaceOrUndef(ecrInfo.name)));h.assert(f.set_manufacturer_name(wa_ecr_info.v(),s.replaceOrUndef(ecrInfo.manufacturerName)));h.assert(f.set_version(wa_ecr_info.v(),s.replaceOrUndef(ecrInfo.version)));h.assert(f.set_serial_number(wa_ecr_info.v(),s.replaceOrUndef(ecrInfo.serialNumber)));h.assert(f.set_architecture(wa_ecr_info.v(),s.replaceOrUndef(ecrInfo.architecture)));h.assert(f.set_integrator_solution(wa_ecr_info.v(),s.replaceOrUndef(ecrInfo.integratorSolution)));h.assert(f.set_remote_ip(wa_ecr_info.v(),s.replaceOrUndef(ecrInfo.remoteIp)));h.assert(f_list.add(wa_list.v(),wa_ecr_info.v()))}h.assert(t.set_ecr_data(terminal._wao.v(),wa_list.v()))}finally{if(wa_list)wa_list.dispose();if(wa_ecr_info)wa_ecr_info.dispose();if(s)s.dispose()}},terminalAddEcrData:function(terminal,ecr_info){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f=timapi._TimApiHelpers.prepareEcrInfo();let wa_ecr_info,s;try{wa_ecr_info=new timapi._TimApiHelpers.TAObject;s=new timapi._TimApiHelpers.TAString;h.assert(f.create(wa_ecr_info.p()));h.assert(f.set_type(wa_ecr_info.v(),ecr_info.type._value));h.assert(f.set_name(wa_ecr_info.v(),s.replaceOrUndef(ecr_info.name)));h.assert(f.set_manufacturer_name(wa_ecr_info.v(),s.replaceOrUndef(ecr_info.manufacturerName)));h.assert(f.set_version(wa_ecr_info.v(),s.replaceOrUndef(ecr_info.version)));h.assert(f.set_serial_number(wa_ecr_info.v(),s.replaceOrUndef(ecr_info.serialNumber)));h.assert(f.set_architecture(wa_ecr_info.v(),s.replaceOrUndef(ecr_info.architecture)));h.assert(f.set_integrator_solution(wa_ecr_info.v(),s.replaceOrUndef(ecr_info.integratorSolution)));h.assert(f.set_remote_ip(wa_ecr_info.v(),s.replaceOrUndef(ecr_info.remoteIp)));h.assert(t.add_ecr_data(terminal._wao.v(),wa_ecr_info.v()))}finally{if(wa_ecr_info)wa_ecr_info.dispose();if(s)s.dispose()}},terminalGetFeatures:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let features;let mem,p_has_features;try{p_has_features=new timapi._TimApiHelpers.TAPointer;let memSizeFHardware=8*4;let memSizeFPayment=9*4;let memSizeFSixml=21*4;mem=Module._malloc(memSizeFHardware+memSizeFPayment+memSizeFSixml);h.assert(t.get_features(terminal._wao.v(),mem,p_has_features.p()));if(h.bc_c_t_js(p_has_features.v())){features=new timapi.Features;features.hardware=timapi._TimApiHelpers.unwrapFeaturesHardware(mem);features.payment=timapi._TimApiHelpers.unwrapFeaturesPayment(mem+memSizeFHardware);features.sixml=timapi._TimApiHelpers.unwrapFeaturesSixml(mem+memSizeFHardware+memSizeFPayment)}}finally{if(mem)Module._free(mem);if(p_has_features)p_has_features.dispose()}return Object.freeze(features)},unwrapFeaturesHardware:function(mem){let h=timapi._TimApiHelpers.prepareHelpers();let featuresHardware;if(mem!=0){featuresHardware=new timapi.FeaturesHardware;featuresHardware.hasCardholderDisplay=h.bc_c_t_js(Module.getValue(mem,"i32"));featuresHardware.hasMerchantDisplay=h.bc_c_t_js(Module.getValue(mem+4,"i32"));featuresHardware.hasPrinter=h.bc_c_t_js(Module.getValue(mem+8,"i32"));featuresHardware.canSetDisplayBrightness=h.bc_c_t_js(Module.getValue(mem+12,"i32"));featuresHardware.canSetDisplayContrast=h.bc_c_t_js(Module.getValue(mem+16,"i32"));featuresHardware.canSetAlertTones=h.bc_c_t_js(Module.getValue(mem+20,"i32"));featuresHardware.canSetKeypadTones=h.bc_c_t_js(Module.getValue(mem+24,"i32"));featuresHardware.canPowerManagement=h.bc_c_t_js(Module.getValue(mem+28,"i32"))}return Object.freeze(featuresHardware)},unwrapFeaturesPayment:function(mem){let h=timapi._TimApiHelpers.prepareHelpers();let featuresPayment;if(mem!=0){featuresPayment=new timapi.FeaturesPayment;featuresPayment.canDcc=h.bc_c_t_js(Module.getValue(mem,"i32"));featuresPayment.canDeclinedReceipts=h.bc_c_t_js(Module.getValue(mem+4,"i32"));featuresPayment.canPartialApproval=h.bc_c_t_js(Module.getValue(mem+8,"i32"));featuresPayment.canPartialCommit=h.bc_c_t_js(Module.getValue(mem+12,"i32"));featuresPayment.isEp2Available=h.bc_c_t_js(Module.getValue(mem+16,"i32"));featuresPayment.canEp2Dcc=h.bc_c_t_js(Module.getValue(mem+20,"i32"));featuresPayment.canEp2DeclinedReceipts=h.bc_c_t_js(Module.getValue(mem+24,"i32"));featuresPayment.canEp2MultiAccountSelection=h.bc_c_t_js(Module.getValue(mem+28,"i32"));featuresPayment.canEp2MultiContractSelection=h.bc_c_t_js(Module.getValue(mem+32,"i32"))}return Object.freeze(featuresPayment)},unwrapFeaturesSixml:function(mem){let h=timapi._TimApiHelpers.prepareHelpers();let featuresSixml;if(mem!=0){featuresSixml=new timapi.FeaturesSixml;featuresSixml.adminFunctions=h.eBit_t_eSet(timapi.constants.AdminFunctions,Module.getValue(mem,"i32"));featuresSixml.hasAutoCommit=h.ebv(timapi.FeaturesSixml.Support,Module.getValue(mem+4,"i32"));featuresSixml.hasAutoShiftManagement=h.ebv(timapi.FeaturesSixml.Support,Module.getValue(mem+8,"i32"));featuresSixml.hasAutoShutterManagement=h.ebv(timapi.FeaturesSixml.Support,Module.getValue(mem+12,"i32"));featuresSixml.canRequestRepetition=h.bc_c_t_js(Module.getValue(mem+16,"i32"));featuresSixml.financialFunctions=h.eBit_t_eSet(timapi.constants.FinancialTransactions,Module.getValue(mem+20,"i32"));featuresSixml.guides=h.eBit_t_eSet(timapi.constants.Guides,Module.getValue(mem+24,"i32"));featuresSixml.nonFinancialFunctions=h.eBit_t_eSet(timapi.constants.NonFinancialTransactions,Module.getValue(mem+28,"i32"));featuresSixml.protocolLevel=h.ebv(timapi.constants.ProtocolLevel,Module.getValue(mem+32,"i32"));featuresSixml.hasSleepTimer=h.bc_c_t_js(Module.getValue(mem+36,"i32"));featuresSixml.statusFunctions=h.eBit_t_eSet(timapi.constants.StatusFunctions,Module.getValue(mem+40,"i32"));featuresSixml.dialogFunctions=h.eBit_t_eSet(timapi.constants.DialogFunctions,Module.getValue(mem+44,"i32"));featuresSixml.remoteFunctions=h.eBit_t_eSet(timapi.constants.RemoteFunctions,Module.getValue(mem+80,"i32"));featuresSixml.allowsClosedCardInsertion=h.bc_c_t_js(Module.getValue(mem+48,"i32"));featuresSixml.hasFastNtfMode=h.bc_c_t_js(Module.getValue(mem+52,"i32"));featuresSixml.persistentState=h.bc_c_t_js(Module.getValue(mem+56,"i32"));featuresSixml.ep2ReferencedTransaction=h.bc_c_t_js(Module.getValue(mem+60,"i32"));featuresSixml.ep2DeferredAuthorisation=h.bc_c_t_js(Module.getValue(mem+64,"i32"));featuresSixml.deferredAuthorisation=h.bc_c_t_js(Module.getValue(mem+68,"i32"));featuresSixml.ep2CredentialOnFile=h.bc_c_t_js(Module.getValue(mem+72,"i32"));featuresSixml.credentialOnFile=h.bc_c_t_js(Module.getValue(mem+76,"i32"))}return Object.freeze(featuresSixml)},terminalGetMerchantOptions:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let merchantOptions=[];let wa_list,p;try{wa_list=new timapi._TimApiHelpers.TAObject;p=new timapi._TimApiHelpers.TAPointer;h.assert(t.get_merchant_options(terminal._wao.v(),wa_list.p()));h.lit(wa_list.v(),function(c_merchant_option){merchantOptions.push(timapi._TimApiHelpers.unwrapMerchantOption(c_merchant_option))})}finally{if(wa_list)wa_list.dispose();if(p)p.dispose()}return merchantOptions},terminalSetMerchantOptions:function(terminal,merchantOptions){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f_list=timapi._TimApiHelpers.prepareList();let wa_list,wa_merchant_option;try{wa_list=new timapi._TimApiHelpers.TAObject;wa_merchant_option=new timapi._TimApiHelpers.TAObject;h.assert(f_list.create(wa_list.p()));for(let merchantOption of merchantOptions){wa_merchant_option.takeover(timapi._TimApiHelpers.convertMerchantOption(merchantOption));h.assert(f_list.add(wa_list.v(),wa_merchant_option.v()))}h.assert(t.set_merchant_options(terminal._wao.v(),wa_list.v()))}finally{if(wa_list)wa_list.dispose();if(wa_merchant_option)wa_merchant_option.dispose()}},terminalCanMultiAccountSelection:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let p;let canMultiAccountSelection;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(t.can_multi_account_selection(terminal._wao.v(),p.p()));canMultiAccountSelection=h.bc_c_t_js(p.v())}finally{if(p)p.dispose()}return canMultiAccountSelection},terminalGetPosId:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let s;let posId;try{s=new timapi._TimApiHelpers.TAString;h.assert(t.get_pos_id(terminal._wao.v(),s.p()));posId=s.content()}finally{if(s)s.dispose()}return posId},terminalSetPosId:function(terminal,posId){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let s;try{s=timapi._TimApiHelpers.TAString.create(posId);h.assert(t.set_pos_id(terminal._wao.v(),s.v()))}finally{if(s)s.dispose()}},terminalGetPrintOptions:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let printOptions=[];let wa_list,p;try{wa_list=new timapi._TimApiHelpers.TAObject;p=new timapi._TimApiHelpers.TAPointer;h.assert(t.get_print_options(terminal._wao.v(),wa_list.p()));h.lit(wa_list.v(),function(c_print_option){printOptions.push(timapi._TimApiHelpers.unwrapPrintOption(c_print_option))})}finally{if(wa_list)wa_list.dispose();if(p)p.dispose()}return printOptions},terminalSetPrintOptions:function(terminal,printOptions){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f_list=timapi._TimApiHelpers.prepareList();let wa_list,wa_print_option;try{wa_list=new timapi._TimApiHelpers.TAObject;wa_print_option=new timapi._TimApiHelpers.TAObject;h.assert(f_list.create(wa_list.p()));for(let printOption of printOptions){wa_print_option.takeover(timapi._TimApiHelpers.convertPrintOption(printOption));h.assert(f_list.add(wa_list.v(),wa_print_option.v()))}h.assert(t.set_print_options(terminal._wao.v(),wa_list.v()))}finally{if(wa_list)wa_list.dispose();if(wa_print_option)wa_print_option.dispose()}},terminalGetSettings:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f=timapi._TimApiHelpers.prepareTerminalSettings();let terminalSettings=new timapi.TerminalSettings;let wa_terminal_settings,p;try{wa_terminal_settings=new timapi._TimApiHelpers.TAObject;h.assert(t.get_settings(terminal._wao.v(),wa_terminal_settings.p()));p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_connection_ip_string(wa_terminal_settings.v(),p.p()));terminalSettings.connectionIPString=h.contentOfIfValid(p);h.assert(f.get_connection_ip_port(wa_terminal_settings.v(),p.p()));terminalSettings.connectionIPPort=p.v();h.assert(f.get_protocol_type(wa_terminal_settings.v(),p.p()));terminalSettings.protocolType=h.ebv(timapi.constants.ProtocolType,p.v());h.assert(f.get_integrator_id(wa_terminal_settings.v(),p.p()));terminalSettings.integratorId=h.contentOfIfValid(p);h.assert(f.get_guides(wa_terminal_settings.v(),p.p()));terminalSettings.guides=h.eBit_t_eSet(timapi.constants.Guides,p.v());h.assert(f.get_manufacturer_flags(wa_terminal_settings.v(),p.p()));terminalSettings.manufacturerFlags=p.v();h.assert(f.is_fetch_brands(wa_terminal_settings.v(),p.p()));terminalSettings.fetchBrands=h.bc_c_t_js(p.v());h.assert(f.is_auto_commit(wa_terminal_settings.v(),p.p()));terminalSettings.autoCommit=h.bc_c_t_js(p.v());h.assert(f.is_auto_shift_management(wa_terminal_settings.v(),p.p()));terminalSettings.autoShiftManagement=h.bc_c_t_js(p.v());h.assert(f.is_auto_shutter_management(wa_terminal_settings.v(),p.p()));terminalSettings.autoShutterManagement=h.bc_c_t_js(p.v());h.assert(f.get_card_insertion_timeout(wa_terminal_settings.v(),p.p()));terminalSettings.cardInsertionTimeout=p.v();h.assert(f.get_card_removal_timeout(wa_terminal_settings.v(),p.p()));terminalSettings.cardRemovalTimeout=p.v();h.assert(f.get_commit_timeout(wa_terminal_settings.v(),p.p()));terminalSettings.commitTimeout=p.v();h.assert(f.get_proceed_timeout(wa_terminal_settings.v(),p.p()));terminalSettings.proceedTimeout=p.v();h.assert(f.is_dcc(wa_terminal_settings.v(),p.p()));terminalSettings.dcc=h.bc_c_t_js(p.v());h.assert(f.is_partial_approval(wa_terminal_settings.v(),p.p()));terminalSettings.partialApproval=h.bc_c_t_js(p.v());h.assert(f.is_allow_closed_card_insert(wa_terminal_settings.v(),p.p()));terminalSettings.allowClosedCardInsert=h.bc_c_t_js(p.v());h.assert(f.is_tip_allowed(wa_terminal_settings.v(),p.p()));terminalSettings.tipAllowed=h.bc_c_t_js(p.v());h.assert(f.is_fast_ntf_mode(wa_terminal_settings.v(),p.p()));terminalSettings.fastNtfMode=h.bc_c_t_js(p.v());h.assert(f.get_request_repetition(wa_terminal_settings.v(),p.p()));terminalSettings.requestRepetition=p.v();h.assert(f.get_persistent_state(wa_terminal_settings.v(),p.p()));terminalSettings.persistentState=p.v();h.assert(f.is_enabled_keep_alive(wa_terminal_settings.v(),p.p()));terminalSettings.enableKeepAlive=h.bc_c_t_js(p.v())}finally{if(wa_terminal_settings)wa_terminal_settings.dispose();if(p)p.dispose()}return Object.freeze(terminalSettings)},terminalHasSwUpdate:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let p;let hasSwUpdate;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(t.has_sw_update(terminal._wao.v(),p.p()));hasSwUpdate=h.bc_c_t_js(p.v())}finally{if(p)p.dispose()}return hasSwUpdate},terminalGetTerminalId:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let s;let terminalId;try{s=new timapi._TimApiHelpers.TAString;h.assert(t.get_terminal_id(terminal._wao.v(),s.p()));terminalId=s.content()}finally{if(s)s.dispose()}return terminalId},terminalGetTerminalStatus:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_terminal_settings;let terminalSettings;try{wa_terminal_settings=new timapi._TimApiHelpers.TAObject;h.assert(t.get_terminal_status(terminal._wao.v(),wa_terminal_settings.p()));terminalSettings=timapi._TimApiHelpers.unwrapTerminalStatus(wa_terminal_settings.v())}finally{if(wa_terminal_settings)wa_terminal_settings.dispose()}return terminalSettings},terminalGetTransactionData:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_transaction_data;let transactionData=undefined;try{wa_transaction_data=new timapi._TimApiHelpers.TAObject;h.assert(t.get_transaction_data(terminal._wao.v(),wa_transaction_data.p()));transactionData=timapi._TimApiHelpers.unwrapTransactionData(wa_transaction_data.v())}finally{if(wa_transaction_data)wa_transaction_data.dispose()}return transactionData},terminalSetTransactionData:function(terminal,transactionData){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_transaction_data;try{wa_transaction_data=timapi._TimApiHelpers.convertTransactionData(transactionData);h.assert(t.set_transaction_data(terminal._wao.v(),wa_transaction_data.v()))}finally{if(wa_transaction_data)wa_transaction_data.dispose()}},terminalGetUserId:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let p;let userId;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(t.get_user_id(terminal._wao.v(),p.p()));userId=p.v()}finally{if(p)p.dispose()}return userId},terminalSetUserId:function(terminal,userId){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();h.assert(t.set_user_id(terminal._wao.v(),userId))},terminalSetNormalReceiptFormatter:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let rf=timapi._TimApiHelpers.prepareReceiptFormatter();let wa_formatter;try{wa_formatter=new timapi._TimApiHelpers.TAObject;h.assert(rf.normal_create(wa_formatter.p()));h.assert(t.set_receipt_formatter(terminal._wao.v(),wa_formatter.v()))}finally{if(wa_formatter)wa_formatter.dispose()}},terminalSetCompactReceiptFormatter:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let rf=timapi._TimApiHelpers.prepareReceiptFormatter();let wa_formatter;try{wa_formatter=new timapi._TimApiHelpers.TAObject;h.assert(rf.compact_create(wa_formatter.p()));h.assert(t.set_receipt_formatter(terminal._wao.v(),wa_formatter.v()))}finally{if(wa_formatter)wa_formatter.dispose()}},terminalSetSuperCompactReceiptFormatter:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let rf=timapi._TimApiHelpers.prepareReceiptFormatter();let wa_formatter;try{wa_formatter=new timapi._TimApiHelpers.TAObject;h.assert(rf.super_compact_create(wa_formatter.p()));h.assert(t.set_receipt_formatter(terminal._wao.v(),wa_formatter.v()))}finally{if(wa_formatter)wa_formatter.dispose()}},terminalSetUltraCompactReceiptFormatter:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let rf=timapi._TimApiHelpers.prepareReceiptFormatter();let wa_formatter;try{wa_formatter=new timapi._TimApiHelpers.TAObject;h.assert(rf.ultra_compact_create(wa_formatter.p()));h.assert(t.set_receipt_formatter(terminal._wao.v(),wa_formatter.v()))}finally{if(wa_formatter)wa_formatter.dispose()}},terminalSetCustomReceiptFormatter:function(terminal,definition){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_formatter;try{wa_formatter=new timapi._TimApiHelpers.createCustomReceiptFormatter(definition);h.assert(t.set_receipt_formatter(terminal._wao.v(),wa_formatter.v()))}finally{if(wa_formatter)wa_formatter.dispose()}},amtAdjustment:function(terminal,amount){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_amount;try{wa_amount=timapi._TimApiHelpers.convertAmount(amount);h.assert(t.amt_adjustment(terminal._wao.v(),wa_amount.v()))}finally{if(wa_amount)wa_amount.dispose()}},amtAdjustmentVas:function(terminal,amount,amountDiscount,loyaltyCouponList,loyaltyInformationList){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f_list=timapi._TimApiHelpers.prepareList();let wa_obj,wa_amount,wa_amount_discount,wa_loyalty_coupon_list,wa_loyalty_information_list;try{wa_obj=new timapi._TimApiHelpers.TAObject;wa_loyalty_coupon_list=new timapi._TimApiHelpers.TAObject;wa_loyalty_information_list=new timapi._TimApiHelpers.TAObject;wa_amount=timapi._TimApiHelpers.convertAmount(amount);wa_amount_discount=timapi._TimApiHelpers.convertAmountDiscount(amountDiscount);if(loyaltyCouponList!==undefined){h.assert(f_list.create(wa_loyalty_coupon_list.p()));for(let loyaltyCoupon of loyaltyCouponList){wa_obj.takeover(timapi._TimApiHelpers.convertLoyaltyCoupon(loyaltyCoupon));h.assert(f_list.add(wa_loyalty_coupon_list.v(),wa_obj.v()))}}if(loyaltyInformationList!==undefined){h.assert(f_list.create(wa_loyalty_information_list.p()));for(let loyaltyInformation of loyaltyInformationList){wa_obj.takeover(timapi._TimApiHelpers.convertLoyaltyInformation(loyaltyInformation));h.assert(f_list.add(wa_loyalty_information_list.v(),wa_obj.v()))}}h.assert(t.amt_adjustment_2(terminal._wao.v(),wa_amount.v(),wa_amount_discount.v(),wa_loyalty_coupon_list.v(),wa_loyalty_information_list.v()))}finally{if(wa_obj)wa_obj.dispose();if(wa_amount)wa_amount.dispose();if(wa_amount_discount)wa_amount_discount.dispose();if(wa_loyalty_coupon_list)wa_loyalty_coupon_list.dispose();if(wa_loyalty_information_list)wa_loyalty_information_list.dispose()}},cancelUnattended:function(terminal,silent,retainCard){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();h.assert(t.cancel2(terminal._wao.v(),h.bc_js_t_c(silent),h.bc_js_t_c(retainCard)))},changeSettingsAsync:function(terminal,settings){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_settings;try{wa_settings=timapi._TimApiHelpers.convertESMap(settings);h.assert(t.change_settings_async(terminal._wao.v(),wa_settings.v()))}finally{if(wa_settings)wa_settings.dispose()}},commitAmountAsync:function(terminal,amount){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_amount;try{wa_amount=timapi._TimApiHelpers.convertAmount(amount);h.assert(t.commit_amount_async(terminal._wao.v(),wa_amount.v()))}finally{if(wa_amount)wa_amount.dispose()}},counterRequestAsync:function(terminal,counterType){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();h.assert(t.counter_request_async(terminal._wao.v(),counterType._value))},initTransactionAsync:function(terminal,displayAmount,amount,functionHint){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_amount;try{wa_amount=timapi._TimApiHelpers.convertAmount(amount);if(functionHint!==undefined){h.assert(t.init_transaction2_async(terminal._wao.v(),h.bc_js_t_c(displayAmount),wa_amount.v(),functionHint._value))}else{h.assert(t.init_transaction_async(terminal._wao.v(),h.bc_js_t_c(displayAmount),wa_amount.v()))}}finally{if(wa_amount)wa_amount.dispose()}},loyaltyDataAsync(terminal,functionType,dataType,data,retainCard){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let s_data_type,s_data;try{s_data_type=timapi._TimApiHelpers.TAString.create(dataType);s_data=timapi._TimApiHelpers.TAString.createFromUint8Array(data);h.assert(t.loyalty_data_async(terminal._wao.v(),functionType._value,s_data_type.v(),s_data.v(),h.bc_js_t_c(retainCard)))}finally{if(s_data_type)s_data_type.dispose();if(s_data)s_data.dispose()}},provideLoyaltyBasketAsync(terminal,basket){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f_list=timapi._TimApiHelpers.prepareList();let wa_basket,wa_obj;try{wa_basket=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;h.assert(f_list.create(wa_basket.p()));for(let loyaltyItem of basket){wa_obj.takeover(timapi._TimApiHelpers.convertLoyaltyItem(loyaltyItem));h.assert(f_list.add(wa_basket.v(),wa_obj.v()))}h.assert(t.provide_loyalty_basket_async(terminal._wao.v(),wa_basket.v()))}finally{if(wa_basket)wa_basket.dispose();if(wa_obj)wa_obj.dispose()}},provideVasResultAsync(terminal,result){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_result;try{wa_result=timapi._TimApiHelpers.convertVasResult(result);h.assert(t.provide_vas_result_async(terminal._wao.v(),wa_result.v()))}finally{if(wa_result)wa_result.dispose()}},receiptRequestAsync:function(terminal,type){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();h.assert(t.receipt_request_async(terminal._wao.v(),type._value))},reconciliationAsync:function(terminal){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();h.assert(t.reconciliation_async(terminal._wao.v()))},sendCardCommandAsync:function(terminal,requests){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let f_list=timapi._TimApiHelpers.prepareList();let wa_list,wa_obj;try{wa_list=new timapi._TimApiHelpers.TAObject;wa_obj=new timapi._TimApiHelpers.TAObject;h.assert(f_list.create(wa_list.p()));for(let commandRequest of requests){wa_obj.takeover(timapi._TimApiHelpers.convertCommandRequest(commandRequest));h.assert(f_list.add(wa_list.v(),wa_obj.v()))}h.assert(t.send_card_command_async(terminal._wao.v(),wa_list.v()))}finally{if(wa_list)wa_list.dispose();if(wa_obj)wa_obj.dispose()}},showDialogAsync:function(terminal,request){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_request;try{wa_request=timapi._TimApiHelpers.convertShowDialogRequest(request);h.assert(t.show_dialog_async(terminal._wao.v(),wa_request.v()))}finally{if(wa_request)wa_request.dispose()}},printOnTerminalAsync:function(terminal,ticketData){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let s;try{s=timapi._TimApiHelpers.TAString.create(ticketData);h.assert(t.print_on_terminal_async(terminal._wao.v(),s.v()))}finally{if(s)s.dispose()}},showSignatureCaptureAsync:function(terminal,request){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_request;try{wa_request=timapi._TimApiHelpers.convertShowSignatureCaptureRequest(request);h.assert(t.show_signature_capture_async(terminal._wao.v(),wa_request.v()))}finally{if(wa_request)wa_request.dispose()}},transactionAsync:function(terminal,type,amount){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_amount;try{wa_amount=timapi._TimApiHelpers.convertAmount(amount);h.assert(t.transaction_async(terminal._wao.v(),type._value,wa_amount.v()))}finally{if(wa_amount)wa_amount.dispose()}},transactionCashbackAsync:function(terminal,type,amount,amountCashback){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_amount,wa_amount_cashback;try{wa_amount=timapi._TimApiHelpers.convertAmount(amount);wa_amount_cashback=timapi._TimApiHelpers.convertAmount(amountCashback);h.assert(t.transaction_cashback_async(terminal._wao.v(),type._value,wa_amount.v(),wa_amount_cashback.v()))}finally{if(wa_amount)wa_amount.dispose();if(wa_amount_cashback)wa_amount_cashback.dispose()}},transactionTipAsync:function(terminal,type,amount,amountTip){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_amount,wa_amount_tip;try{wa_amount=timapi._TimApiHelpers.convertAmount(amount);wa_amount_tip=timapi._TimApiHelpers.convertAmount(amountTip);h.assert(t.transaction_tip_async(terminal._wao.v(),type._value,wa_amount.v(),wa_amount_tip.v()))}finally{if(wa_amount)wa_amount.dispose();if(wa_amount_tip)wa_amount_tip.dispose()}},transactionWithRequestAsync:function(terminal,type,transactionRequest){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();let wa_transaction_request;try{wa_transaction_request=timapi._TimApiHelpers.convertTransactionRequest(transactionRequest);h.assert(t.transaction2_async(terminal._wao.v(),type._value,wa_transaction_request.v()))}finally{if(wa_transaction_request)wa_transaction_request.dispose()}},deviceMaintenanceAsync:function(terminal,type){let h=timapi._TimApiHelpers.prepareHelpers();let t=timapi._TimApiHelpers.prepareTerminal();h.assert(t.device_maintenance_async(terminal._wao.v(),type._value))},receiptFormatter:undefined,prepareReceiptFormatter:function(){let f=timapi._TimApiHelpers.receiptFormatter;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.receiptFormatter={custom_create:g("ta_custom_receipt_formatter_create",["number"]),normal_create:g("ta_normal_receipt_formatter_create",[]),compact_create:g("ta_compact_receipt_formatter_create",[]),super_compact_create:g("ta_super_compact_receipt_formatter_create",[]),ultra_compact_create:g("ta_ultra_compact_receipt_formatter_create",[]),process_print_receipts:g("ta_process_print_receipts",["number"])}}return f},createCustomReceiptFormatter:function(definition){if(definition===undefined){return new timapi._TimApiHelpers.TAObject}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.prepareReceiptFormatter();let wa_formatter,memoryDefinition;try{memoryDefinition=timapi._TimApiHelpers.getCustomReceiptFormatterStruct(definition);wa_formatter=new timapi._TimApiHelpers.TAObject;h.assert(f.custom_create(wa_formatter.p(),memoryDefinition))}catch(err){if(wa_formatter)wa_formatter.dispose();if(memoryDefinition)Module._free(memoryDefinition);throw err}finally{if(memoryDefinition)Module._free(memoryDefinition)}return wa_formatter},getCustomReceiptFormatterStruct:function(definition){let alm=timapi._TimApiHelpers.prepareHelpers().alm;let workdef=definition.copy();let requiredSize=28+alm(Module.lengthBytesUTF8(workdef.emptyValueString)+1);let allLineFormats=[];allLineFormats.push(workdef.lineFormatsCardholder);allLineFormats.push(workdef.lineFormatsMerchant);allLineFormats.push(workdef.lineFormatsSaldo);allLineFormats.forEach(function(lineFormats){requiredSize+=16*lineFormats.length;lineFormats.forEach(function(lineFormat){requiredSize+=12*lineFormat.elements.length;lineFormat.elements.forEach(function(element){if(element.text!==undefined){requiredSize+=alm(Module.lengthBytesUTF8(element.text)+1)}})})});let memory,reqMemString;try{memory=Module._malloc(requiredSize);reqMemString=alm(Module.lengthBytesUTF8(workdef.emptyValueString)+1);Module.stringToUTF8(workdef.emptyValueString,memory+28,reqMemString);let offset=28+reqMemString;allLineFormats.forEach(function(lineFormats){lineFormats.forEach(function(lineFormat){lineFormat.elements.forEach(function(element){if(element.text!==undefined){reqMemString=alm(Module.lengthBytesUTF8(element.text)+1);Module.stringToUTF8(element.text,memory+offset,reqMemString);element.textOffset=offset;offset+=reqMemString}})})});allLineFormats.forEach(function(lineFormats){lineFormats.forEach(function(lineFormat){lineFormat.elementsOffset=offset;lineFormat.elements.forEach(function(element){Module.setValue(memory+offset,element.type._value,"i32");Module.setValue(memory+offset+4,element.alignment._value,"i32");Module.setValue(memory+offset+8,memory+element.textOffset,"i32");Module.setValue(memory+offset+12,element.receiptType._value,"i32");offset+=16})})});let offsetLines=offset;allLineFormats.forEach(function(lineFormats){lineFormats.forEach(function(lineFormat){if(lineFormat.padding.length!=1){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,"Padding has to be a single character string")}padding=lineFormat.padding.charCodeAt(0);if(padding<32||padding>127){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,"Padding character is not a printable character")}Module.setValue(memory+offset,memory+lineFormat.elementsOffset,"i32");Module.setValue(memory+offset+4,lineFormat.elements.length,"i32");Module.setValue(memory+offset+8,lineFormat.condition._value,"i32");Module.setValue(memory+offset+12,padding,"i8");offset+=16})});Module.setValue(memory,memory+offsetLines,"i32");Module.setValue(memory+4,workdef.lineFormatsCardholder.length,"i32");offsetLines+=16*workdef.lineFormatsCardholder.length;Module.setValue(memory+8,memory+offsetLines,"i32");Module.setValue(memory+12,workdef.lineFormatsMerchant.length,"i32");offsetLines+=16*workdef.lineFormatsMerchant.length;Module.setValue(memory+16,memory+offsetLines,"i32");Module.setValue(memory+20,workdef.lineFormatsSaldo.length,"i32");Module.setValue(memory+24,28,"i32")}catch(err){if(memory)Module._free(memory);throw err}return memory}};timapi._TimApiHelpers.TAObject=class{constructor(value){let t=timapi._TimApiHelpers;if(!t.TAObject._functions){t.TAObject._functions={retain:t.getFunction("ta_object_retain"),release:t.getFunction("ta_object_release"),releaseIfValid:t.getFunction("ta_object_release_if_valid")}}this._pointer=Module._malloc(4);Module.setValue(this._pointer,value||0,"i32");if(value){t.TAObject._functions.retain(value)}}release(){timapi._TimApiHelpers.TAObject._functions.release(this.value());Module.setValue(this._pointer,0,"i32")}releaseIfValid(){timapi._TimApiHelpers.TAObject._functions.releaseIfValid(this.value());Module.setValue(this._pointer,0,"i32")}dispose(){this.releaseIfValid();Module._free(this._pointer)}pointer(){return this._pointer}p(){return this._pointer}value(){return Module.getValue(this._pointer,"i32")}v(){return Module.getValue(this._pointer,"i32")}assign(object){let pointer=object.value();this.releaseIfValid();Module.setValue(this._pointer,pointer,"i32");if(pointer){timapi._TimApiHelpers.TAObject._functions.retain(pointer)}}takeover(object){let pointer=object.value();this.releaseIfValid();Module.setValue(this._pointer,pointer,"i32")}isValid(){return this.value()!=0}};timapi._TimApiHelpers.TAObject._functions=undefined;timapi.Amount=class{constructor(value,currency,exponent){this.value=undefined;if(value!==undefined){this.setValue(value)}this.currency=currency;this.exponent=exponent;if(this.exponent===undefined&&currency!==undefined){this.exponent=currency.exponent}}static fromMajorUnits(valueMajor,currency,exponent){let amount=new timapi.Amount(undefined,currency,exponent);amount.setDecimalValue(valueMajor);return amount}getDecimalValue(){let value=this.value;let i;if(this.exponent>0){for(i=0;i<this.exponent;i++){value/=10}}else if(this.exponent<0){for(i=this.exponent;i<0;i++){value*=10}}return value}setValue(value){if(Math.abs(value-Math.round(value))>1e-4){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"Value of amount in minor units has to be an integer. Given value in minor units is ${value}"`)}value=Math.round(value);if(!Number.isSafeInteger(value)){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,'"Amount is too large (only 52bit values supported by JavaScript)"')}if(value<0){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,'"Value of amount has to be positive or zero"')}this.value=value}setDecimalValue(value){let i;if(this.exponent>0){for(i=0;i<this.exponent;i++){value*=10}}else if(this.exponent<0){for(i=this.exponent;i<0;i++){value/=10}}value=Math.round(value);this.setValue(value)}setCurrency(currency){this.currency=currency;this.exponent=currency.exponent}toString(){return`${this.constructor.name}(`+`value=${this.value}`+`, currency=${this.currency}`+`, exponent=${this.exponent}`+`)`}static copy(amount){return new timapi.Amount(amount.value,amount.currency,amount.exponent)}};Object.defineProperty(timapi.Amount,"name",{value:"timapi.Amount"});timapi.LogRecordTrace=class{constructor(){this.file=undefined;this.method=undefined;this.line=0}};timapi.LogRecord=class{constructor(){this.level=timapi.LogRecord.LogLevel.none;this.thread=0;this.file=undefined;this.method=undefined;this.line=0;this.message=undefined;this.parameters=[];this.stackTrace=[];this.timestamp=new Date}toString(){let levelName=this.level.name.toUpperCase();let string=`[${levelName}] in ${this.method}() at ${this.file}:${this.line} : ${this.message}`;if(this.parameters.length>0){string=string+" "+this.parameters.join(", ")}let i,count=this.stackTrace.length;for(i=0;i<count;i++){let st=this.stackTrace[i];string=string+`\n${i+1}) in ${st.method}() at ${st.file}:${st.line}`}return string}matchesLevel(requiredLogLevel){return this.level._value<=requiredLogLevel._value}};timapi.LogRecord.LogLevel=new timapi.constants.Enumeration(["none","severe","warning","info","fine","finest"],0);timapi.VasCheckoutInformation=class{constructor(){this.loyaltyCoupons=undefined;this.loyaltyInformation=undefined;this.provideBasket=undefined;this.vasInformation=undefined}toString(){return`${this.constructor.name}(`+`loyaltyCoupons=${timapi._TimApiHelpers.arrayAsDebugString(this.loyaltyCoupons)}`+`, loyaltyInformation=${timapi._TimApiHelpers.arrayAsDebugString(this.loyaltyInformation)}`+`, provideBasket=${this.provideBasket}`+`, vasInformation=${timapi._TimApiHelpers.stringUint8MapAsDebugString(this.vasInformation)}`+`)`}};Object.defineProperty(timapi.VasCheckoutInformation,"name",{value:"timapi.VasCheckoutInformation"});timapi.Basket=class{constructor(){this.items=[];this.loyaltyAuthResult=undefined}toString(){return`${this.constructor.name}(`+`items=${timapi._TimApiHelpers.arrayAsDebugString(this.items)}`+`, loyaltyAuthResult=${this.loyaltyAuthResult}`+`)`}static copy(basket){var copy=new timapi.Basket;if(basket.items!==undefined){for(let item of basket.items){copy.items.push(timapi.BasketItem.copy(item))}}copy.loyaltyAuthResult=basket.loyaltyAuthResult;return copy}};Object.defineProperty(timapi.Basket,"name",{value:"timapi.Basket"});timapi.PrintOption=class{constructor(recipient,printFormat,printWidth,printFlags){this.recipient=recipient;this.printFormat=printFormat;this.printWidth=printWidth;this.printFlags=printFlags;Object.freeze(this)}toString(){return this.constructor.name+"("+`recipient=${this.recipient}`+`, printFormat=${this.printFormat}`+`, printWidth=${this.printWidth}`+`, printFlags=${timapi._TimApiHelpers.setAsDebugString(this.printFlags)}`+")"}};Object.defineProperty(timapi.PrintOption,"name",{value:"timapi.PrintOption"});timapi.LoyaltyInformation=class{constructor(value,loyaltyInfoType){this.value=value;this.loyaltyInfoType=loyaltyInfoType;this.loyaltyFunctionType=undefined;this.loyaltyNumber=undefined}toString(){return`${this.constructor.name}(`+`value=${timapi._TimApiHelpers.uint8ArrayAsDebugString(this.value)}`+`, loyaltyInfoType=${this.loyaltyInfoType}`+`, loyaltyFunctionType=${this.loyaltyFunctionType}`+`, loyaltyNumber=${timapi._TimApiHelpers.uint8ArrayAsDebugString(this.loyaltyNumber)}`+`)`}static copy(loyaltyInformation){var copy=new timapi.LoyaltyInformation;if(loyaltyInformation.value!==undefined){copy.value=new Uint8Array(loyaltyInformation.value)}copy.loyaltyInfoType=loyaltyInformation.loyaltyInfoType;copy.loyaltyFunctionType=loyaltyInformation.loyaltyFunctionType;if(loyaltyInformation.loyaltyNumber!==undefined){copy.loyaltyNumber=new Uint8Array(loyaltyInformation.loyaltyNumber)}return copy}};Object.defineProperty(timapi.LoyaltyInformation,"name",{value:"timapi.LoyaltyInformation"});timapi.LoyaltyCoupon=class{constructor(amount,couponId,currency,exponent,couponRejectionReason){this.amount=amount;this.couponId=couponId;this.currency=currency;this.exponent=exponent;this.couponRejectionReason=couponRejectionReason}toString(){return`${this.constructor.name}(`+`amount=${this.amount}`+`, couponId=${this.couponId}`+`, currency=${this.currency}`+`, exponent=${this.exponent}`+`, couponRejectionReason=${this.couponRejectionReason}`+`)`}static copy(loyaltyCoupon){var copy=new timapi.LoyaltyCoupon;copy.amount=loyaltyCoupon.amount;copy.couponId=loyaltyCoupon.couponId;copy.currency=loyaltyCoupon.currency;copy.exponent=loyaltyCoupon.exponent;copy.couponRejectionReason=loyaltyCoupon.couponRejectionReason;return copy}};Object.defineProperty(timapi.LoyaltyCoupon,"name",{value:"timapi.LoyaltyCoupon"});timapi.ShowSignatureCaptureResponse=class{constructor(){this.reason=undefined;this.imageFileFormat=timapi.constants.ImageFileFormat.png;this.imageWidth=undefined;this.imageHeight=undefined;this.imageData=undefined}toString(){return`${this.constructor.name}(`+`reason=${this.reason}`+`, imageFileFormat=${this.imageFileFormat}`+`, imageFileWidth=${this.imageFileWidth}`+`, imageFileHeight=${this.imageFileHeight}`+`, imageData=${timapi._TimApiHelpers.uint8ArrayAsDebugString(this.imageData)}`+`)`}};Object.defineProperty(timapi.ShowSignatureCaptureResponse,"name",{value:"timapi.ShowSignatureCaptureResponse"});timapi.ItemQuantity=class{constructor(quantity,exponent,quantityType){this.quantity=quantity;this.exponent=exponent;this.quantityType=quantityType}toString(){return`${this.constructor.name}(`+`quantity=${this.quantity}`+`, exponent=${this.exponent}`+`, quantityType=${this.quantityType}`+`)`}static copy(itemQuantity){var copy=new timapi.ItemQuantity;copy.quantity=itemQuantity.quantity;copy.exponent=itemQuantity.exponent;copy.quantityType=itemQuantity.quantityType;return copy}};Object.defineProperty(timapi.ItemQuantity,"name",{value:"timapi.ItemQuantity"});timapi.AmountDiscount=class extends timapi.Amount{constructor(value,currency,exponent,discountId){super(value,currency,exponent);this.discountId=discountId}static fromMajorUnits(valueMajor,currency,exponent,discountId){let amountDiscount=new timapi.AmountDiscount(undefined,currency,exponent,discountId);amountDiscount.setDecimalValue(valueMajor);return amountDiscount}toString(){return`${this.constructor.name}(`+`value=${this.value}`+`, currency=${this.currency}`+`, exponent=${this.exponent}`+`, discountId=${this.discountId}`+`)`}static copy(amountDiscount){return new timapi.AmountDiscount(amountDiscount.value,amountDiscount.currency,amountDiscount.exponent,amountDiscount.discountId)}};Object.defineProperty(timapi.AmountDiscount,"name",{value:"timapi.AmountDiscount"});timapi.TransactionResponse=class{constructor(){this.transactionType=undefined;this.amount=undefined;this.amountOther=undefined;this.amountDue=undefined;this.amountDcc=undefined;this.amountSaldo=undefined;this.amountLoyaltyCashback=undefined;this.transactionInformation=undefined;this.dccDisclaimer=undefined;this.cardData=undefined;this.printData=undefined;this.amountTip=undefined;this.additionalInfo=new Map;this.basket=undefined}wasDcc(){return this.amountDcc!==undefined&&this.amountDcc.value>0}wasTip(){return this.amountTip!==undefined&&this.amountTip.value>0}needsAction(){if(this.transactionInformation===undefined){return timapi.TransactionResponse.Action.none}if(this.transactionInformation.cvm!==undefined){switch(this.transactionInformation.cvm){case timapi.constants.Cvm.pinSignature:case timapi.constants.Cvm.signature:return timapi.TransactionResponse.Action.cardholderSignature;default:break}}if(this.transactionInformation.merchantAction!==undefined){switch(this.transactionInformation.merchantAction){case timapi.constants.MerchantAction.signature:return timapi.TransactionResponse.Action.merchantSignature;default:break}}return timapi.TransactionResponse.Action.none}wasPartialApproval(){return this.amountDue!==undefined&&this.amountDue.amount>0}cardCountryCode(){return this.cardData!==undefined?this.cardData.cardCountryCode:undefined}terminalCountryCode(){return this.cardData!==undefined?this.cardData.terminalCountryCode:undefined}isDomestic(){return this.cardCountryCode()!==undefined&&this.terminalCountryCode()!==undefined&&this.cardCountryCode()==this.terminalCountryCode()}toString(){return`${this.constructor.name}(`+`transactionType=${this.transactionType}`+`, amount=${this.amount}`+`, amountOther=${this.amountOther}`+`, amountDue=${this.amountDue}`+`, amountDcc=${this.amountDcc}`+`, amountSaldo=${this.amountSaldo}`+`, amountLoyaltyCashback=${this.amountLoyaltyCashback}`+`, transactionInformation=${this.transactionInformation}`+`, dccDisclaimer=${this.dccDisclaimer}`+`, cardData=${this.cardData}`+`, printData=${this.printData}`+`, amountTip=${this.amountTip}`+`, additionalInfo=${timapi._TimApiHelpers.mapAsDebugString(this.additionalInfo)}`+`, basket=${this.basket}`+`, wasDcc=${this.wasDcc()}`+`, wasTip=${this.wasTip()}`+`, needsAction=${this.needsAction()}`+`, wasPartialApproval=${this.wasPartialApproval()}`+`, cardCountryCode=${this.cardCountryCode()}`+`, terminalCountryCode=${this.terminalCountryCode()}`+`, isDomestic=${this.isDomestic()}`+`)`}};Object.defineProperty(timapi.TransactionResponse,"name",{value:"timapi.TransactionResponse"});timapi.TransactionResponse.Action=new timapi.constants.Enumeration(["none","cardholderSignature","merchantSignature"]);timapi.Total=class{constructor(){this.count=undefined;this.amountSum=undefined;this.amountSumTip=undefined;this.amountSumOther=undefined;this.trxDetails=undefined;this.currency=undefined;this.exponent=undefined}toString(){return`${this.constructor.name}(`+`count=${this.count}`+`, amountSum=${this.amountSum}`+`, amountSumTip=${this.amountSumTip}`+`, amountSumOther=${this.amountSumOther}`+`, trxDetails=${timapi._TimApiHelpers.arrayAsDebugString(this.trxDetails)}`+`, currency=${this.currency}`+`, exponent=${this.exponent}`+`)`}};Object.defineProperty(timapi.Total,"name",{value:"timapi.Total"});timapi.LoyaltyItem=class{constructor(){this.itemId=undefined;this.prodDescription=undefined;this.amount=undefined;this.itemQuantity=undefined;this.amountTotal=undefined;this.loyaltyDiscounts=[];this.loyaltyCoupons=[];this.displayProductInfo=undefined}toString(){return`${this.constructor.name}(`+`itemId=${this.itemId}`+`, prodDescription=${this.prodDescription}`+`, amount=${this.amount}`+`, itemQuantity=${this.itemQuantity}`+`, amountTotal=${this.amountTotal}`+`, loyaltyDiscounts=${this.loyaltyDiscounts}`+`, loyaltyCoupons=${this.loyaltyCoupons}`+`, displayProductInfo=${this.displayProductInfo}`+`)`}static copy(loyaltyItem){var copy=new timapi.LoyaltyItem;copy.itemId=loyaltyItem.itemId;copy.prodDescription=loyaltyItem.prodDescription;copy.amount=timapi.Amount.copy(loyaltyItem.amount);copy.itemQuantity=timapi.ItemQuantity.copy(loyaltyItem.itemQuantity);copy.amountTotal=timapi.Amount.copy(loyaltyItem.amountTotal);if(loyaltyItem.loyaltyDiscounts!==undefined){for(let loyaltyDiscount of loyaltyItem.loyaltyDiscounts){copy.loyaltyDiscounts.push(timapi.LoyaltyDiscount.copy(loyaltyDiscount))}}if(loyaltyItem.loyaltyCoupons!==undefined){for(let loyaltyCoupon of loyaltyItem.loyaltyCoupons){copy.loyaltyCoupons.push(timapi.LoyaltyCoupon.copy(loyaltyCoupon))}}if(loyaltyItem.displayProductInfo!==undefined){copy.displayProductInfo=timapi.DisplayProductInfo.copy(loyaltyItem.displayProductInfo)}return copy}};Object.defineProperty(timapi.LoyaltyItem,"name",{value:"timapi.LoyaltyItem"});timapi.TimeDate=class{constructor(){this.year=0;this.month=0;this.day=1;this.hour=0;this.minute=0;this.second=0}toDate(){return Date(this.year,this.month,this.day,this.hour,this.minute,this.second,0)}static fromDate(date){let timeDate=new timapi.TimeDate;timeDate.year=date.getFullYear();timeDate.month=date.getMonth();timeDate.day=date.getDate();timeDate.hour=date.getHours();timeDate.minute=date.getMinutes();timeDate.second=date.getSeconds();return timeDate}static fromNow(){return timapi.TimeDate.fromDate(new Date)}_pad(num,size){var s="000000000"+num;return s.substr(s.length-size)}toStringRegular(){return this.toStringReceiptDate()+" "+this.toStringReceiptTime()}toStringReceiptDate(){return`${this._pad(this.day,2)}${this._pad(this.month+1,2)}${this._pad(this.year,4)}`}toStringReceiptTime(){return`${this._pad(this.hour,2)}${this._pad(this.minute,2)}${this._pad(this.second,2)}`}toString(){let s=this.constructor.name;s+="(";s+=this.toStringRegular();s+=")";return s}static copy(timeDate){var copy=new timapi.TimeDate;copy.year=timeDate.year;copy.month=timeDate.month;copy.day=timeDate.day;copy.hour=timeDate.hour;copy.minute=timeDate.minute;copy.second=timeDate.second;return copy}};Object.defineProperty(timapi.TimeDate,"name",{value:"timapi.TimeDate"});timapi.FeaturesPayment=class{constructor(){this.canDcc=undefined;this.canDeclinedReceipts=undefined;this.canPartialApproval=undefined;this.canPartialCommit=undefined;this.isEp2Available=undefined;this.canEp2Dcc=undefined;this.canEp2DeclinedReceipts=undefined;this.canEp2MultiAccountSelection=undefined;this.canEp2MultiContractSelection=undefined}toString(){return`${this.constructor.name}(`+`canDcc=${this.canDcc}`+`, canDeclinedReceipts=${this.canDeclinedReceipts}`+`, canPartialApproval=${this.canPartialApproval}`+`, canPartialCommit=${this.canPartialCommit}`+`, isEp2Available=${this.isEp2Available}`+`, canEp2Dcc=${this.canEp2Dcc}`+`, canEp2DeclinedReceipts=${this.canEp2DeclinedReceipts}`+`, canEp2MultiAccountSelection=${this.canEp2MultiAccountSelection}`+`, canEp2MultiContractSelection=${this.canEp2MultiContractSelection}`+`)`}};Object.defineProperty(timapi.FeaturesPayment,"name",{value:"timapi.FeaturesPayment"});timapi.ShowDialogRequest=class{constructor(){this.brandBar=[];this.brandMode=timapi.constants.BrandMode.all;this.resourceId=timapi.constants.ResourceId.welcomeCard;this.theme=timapi.constants.Theme.six;this.timeout=60;this.language=undefined;this.placeholderItems=new Map;this.resourceParameters=new Map}toString(){return`${this.constructor.name}(`+`brandBar=${timapi._TimApiHelpers.arrayAsDebugString(this.brandBar)}`+`, brandMode=${this.brandMode}`+`, resourceId=${this.resourceId}`+`, theme=${this.theme}`+`, timeout=${this.timeout}`+`, language=${this.language}`+`, placeholderItems=${timapi._TimApiHelpers.mapAsDebugString(this.placeholderItems)}`+`, resourceParameters=${timapi._TimApiHelpers.mapAsDebugString(this.resourceParameters)}`+`)`}static copy(showDialogRequest){var copy=new timapi.ShowDialogRequest;if(showDialogRequest.brandBar!==undefined){copy.brandBar=showDialogRequest.brandBar.slice()}copy.brandMode=showDialogRequest.brandMode;copy.resourceId=showDialogRequest.resourceId;copy.theme=showDialogRequest.theme;copy.timeout=showDialogRequest.timeout;copy.language=showDialogRequest.language;copy.placeholderItems=new Map(showDialogRequest.placeholderItems);copy.resourceParameters=new Map(showDialogRequest.resourceParameters);return copy}};Object.defineProperty(timapi.ShowDialogRequest,"name",{value:"timapi.ShowDialogRequest"});timapi.ReceiptRequestResponse=class{constructor(){this.printData=undefined;this.hasMoreReceipts=undefined}toString(){return`${this.constructor.name}(`+`printData=${this.printData}`+`, hasMoreReceipts=${this.hasMoreReceipts}`+`)`}};Object.defineProperty(timapi.ReceiptRequestResponse,"name",{value:"timapi.ReceiptRequestResponse"});timapi.FeaturesHardware=class{constructor(){this.hasCardholderDisplay=undefined;this.hasMerchantDisplay=undefined;this.hasPrinter=undefined;this.canSetDisplayBrightness=undefined;this.canSetDisplayContrast=undefined;this.canSetAlertTones=undefined;this.canSetKeypadTones=undefined;this.canPowerManagement=undefined}toString(){return`${this.constructor.name}(`+`hasCardholderDisplay=${this.hasCardholderDisplay}`+`, hasMerchantDisplay=${this.hasMerchantDisplay}`+`, hasPrinter=${this.hasPrinter}`+`, canSetDisplayBrightness=${this.canSetDisplayBrightness}`+`, canSetDisplayContrast=${this.canSetDisplayContrast}`+`, canSetAlertTones=${this.canSetAlertTones}`+`, canSetKeypadTones=${this.canSetKeypadTones}`+`, canPowerManagement=${this.canPowerManagement}`+`)`}};Object.defineProperty(timapi.FeaturesHardware,"name",{value:"timapi.FeaturesHardware"});timapi.ActivateResponse=class{constructor(){this.printData=undefined;this.actSeqCounter=undefined}toString(){return`${this.constructor.name}(`+`printData=${this.printData}`+`, actSeqCounter=${this.actSeqCounter}`+`)`}};Object.defineProperty(timapi.ActivateResponse,"name",{value:"timapi.ActivateResponse"});timapi.NetworkInformation=class{constructor(){this.terminalIp=undefined;this.terminalIpMask=undefined;this.terminalIpGw=undefined;this.terminalIpDns=undefined}toString(){return`${this.constructor.name}(`+`terminalIp=${this.terminalIp}`+`, terminalIpMask=${this.terminalIpMask}`+`, terminalIpGw=${this.terminalIpGw}`+`, terminalIpDns=${this.terminalIpDns}`+`)`}};Object.defineProperty(timapi.NetworkInformation,"name",{value:"timapi.NetworkInformation"});timapi.ReconciliationResponse=class{constructor(){this.counters=undefined;this.printData=undefined}toString(){return`${this.constructor.name}(`+`counters=${this.counters}`+`, printData=${this.printData}`+`)`}};Object.defineProperty(timapi.ReconciliationResponse,"name",{value:"timapi.ReconciliationResponse"});timapi.ReceiptItems=class{constructor(){this.receiptType=undefined;this.receiptItem=undefined}toString(){return`${this.constructor.name}(`+` receiptType=${this.receiptType}`+` receiptItem=${timapi._TimApiHelpers.arrayAsDebugString(this.receiptItem)}`+`)`}};Object.defineProperty(timapi.ReceiptItems,"name",{value:"timapi.ReceiptItems"});timapi.BalanceResponse=class{constructor(){this.printData=undefined;this.counters=undefined}toString(){return`${this.constructor.name}(`+`printData=${this.printData}`+`, counters=${this.counters}`+`)`}};Object.defineProperty(timapi.BalanceResponse,"name",{value:"timapi.BalanceResponse"});timapi.PpInfo=class{constructor(){this.paymentProtocol=undefined;this.ppEp2TransSeqCnt=undefined;this.ppEp2TransSeqCntOrig=undefined;this.ppEp2AuthReslt=undefined;this.ppEp2AuthRespC=undefined}toString(){return`${this.constructor.name}(`+`paymentProtocol=${this.paymentProtocol}`+`, ppEp2TransSeqCnt=${this.ppEp2TransSeqCnt}`+`, ppEp2TransSeqCntOrig=${this.ppEp2TransSeqCntOrig}`+`, ppEp2AuthReslt=${this.ppEp2AuthReslt}`+`, ppEp2AuthRespC=${this.ppEp2AuthRespC}`+`)`}};Object.defineProperty(timapi.PpInfo,"name",{value:"timapi.PpInfo"});timapi.ReceiptItem=class{constructor(){this.receiptItemType=undefined;this.recipient=undefined;this.value=undefined}toString(){return`${this.constructor.name}(`+`receiptItemType=${this.receiptItemType}`+`, recipient=${this.recipient}`+`, value=${this.value}`+`)`}};Object.defineProperty(timapi.ReceiptItem,"name",{value:"timapi.ReceiptItem"});timapi.DisplayProductInfo=class{constructor(){this.imageFileFormat=timapi.constants.ImageFileFormat.png;this.imageFileWidth=undefined;this.imageFileHeight=undefined;this.imageData=undefined;this.productDisplayName=undefined;this.backgroundColor=new timapi.Color(255,255,255)}toString(){return`${this.constructor.name}(`+`imageData=${timapi._TimApiHelpers.uint8ArrayAsDebugString(this.imageData)}`+`, imageFileFormat=${this.imageFileFormat}`+`, imageFileWidth=${this.imageFileWidth}`+`, imageFileHeight=${this.imageFileHeight}`+`, productDisplayName=${this.productDisplayName}`+`, backgroundColor=${this.backgroundColor}`+`)`}static copy(displayProductInfo){var copy=new timapi.DisplayProductInfo;copy.imageData=new Uint8Array(displayProductInfo.imageData);copy.itemId=DisplayProductInfo.itemId;copy.imageFileFormat=displayProductInfo.imageFileFormat;copy.imageFileWidth=displayProductInfo.imageFileWidth;copy.imageFileHeight=displayProductInfo.imageFileHeight;copy.productDisplayName=timapi.Color.copy(displayProductInfo.productDisplayName);copy.backgroundColor=timapi.Color.copy(displayProductInfo.backgroundColor);return copy}};Object.defineProperty(timapi.DisplayProductInfo,"name",{value:"timapi.DisplayProductInfo"});timapi.PrintData=class{constructor(){this.receipts=[];this.receiptItems=[]}toString(){return`${this.constructor.name}(`+`receipts=${timapi._TimApiHelpers.arrayAsDebugString(this.receipts)}`+`, receiptItems=${timapi._TimApiHelpers.arrayAsDebugString(this.receiptItems)}`+`)`}};Object.defineProperty(timapi.PrintData,"name",{value:"timapi.PrintData"});timapi.BasketItem=class{constructor(){this.authResult=undefined;this.itemId=undefined;this.loyaltyId=undefined;this.amount=undefined;this.amountTotal=undefined;this.amountDiscount=undefined;this.amountTax=undefined;this.amountGross=undefined;this.unitAmountDiscount=undefined;this.unitAmountGross=undefined;this.itemQuantity=undefined;this.prodDescription=undefined}toString(){return`${this.constructor.name}(`+`authResult=${this.authResult}`+`, itemId=${this.itemId}`+`, loyaltyId=${this.loyaltyId}`+`, amount=${this.amount}`+`, amountTotal=${this.amountTotal}`+`, amountDiscount=${this.amountDiscount}`+`, amountTax=${this.amountTax}`+`, amountGross=${this.amountGross}`+`, unitAmountDiscount=${this.unitAmountDiscount}`+`, unitAmountGross=${this.unitAmountGross}`+`, itemQuantity=${this.itemQuantity}`+`, prodDescription=${this.prodDescription}`+`)`}static copy(basketItem){var copy=new timapi.BasketItem;copy.authResult=basketItem.authResult;copy.itemId=basketItem.itemId;copy.loyaltyId=basketItem.loyaltyId;if(basketItem.amount!==undefined){copy.amount=timapi.Amount.copy(basketItem.amount)}if(basketItem.amountTotal!==undefined){copy.amountTotal=timapi.Amount.copy(basketItem.amountTotal)}if(basketItem.amountDiscount!==undefined){copy.amountDiscount=timapi.AmountDiscount.copy(basketItem.amountDiscount)}if(basketItem.amountTax!==undefined){copy.amountTax=timapi.Amount.copy(basketItem.amountTax)}if(basketItem.amountGross!==undefined){copy.amountGross=timapi.Amount.copy(basketItem.amountGross)}if(basketItem.unitAmountDiscount!==undefined){copy.unitAmountDiscount=timapi.AmountDiscount.copy(basketItem.unitAmountDiscount)}if(basketItem.unitAmountGross!==undefined){copy.unitAmountGross=timapi.Amount.copy(basketItem.unitAmountGross)}if(basketItem.itemQuantity!==undefined){copy.itemQuantity=timapi.ItemQuantity.copy(basketItem.itemQuantity)}return copy}};Object.defineProperty(timapi.BasketItem,"name",{value:"timapi.BasketItem"});timapi.Application=class{constructor(){this.aid=undefined;this.label=undefined}toString(){return`${this.constructor.name}(`+`aid=${this.aid}`+`, label=${this.label}`+`)`}};Object.defineProperty(timapi.Application,"name",{value:"timapi.Application"});timapi.Receipt=class{constructor(){this.recipient=undefined;this.value=undefined}toString(){return`${this.constructor.name}(`+`recipient=${this.recipient}`+`, value=${this.value}`+`)`}};Object.defineProperty(timapi.Receipt,"name",{value:"timapi.Receipt"});timapi.Features=class{constructor(){this.hardware=undefined;this.payment=undefined;this.sixml=undefined}toString(){return`${this.constructor.name}(`+`hardware=${this.hardware}`+`, payment=${this.payment}`+`, sixml=${this.sixml}`+`)`}};Object.defineProperty(timapi.Features,"name",{value:"timapi.Features"});timapi.Counter=class{constructor(){this.brandName=undefined;this.paymentProtocol=undefined;this.acqId=undefined;this.count=undefined;this.countDcc=undefined;this.countForeign=undefined;this.totals=undefined}toString(){return`${this.constructor.name}(`+`brandName=${this.brandName}`+`, paymentProtocol=${this.paymentProtocol}`+`, acqId=${this.acqId}`+`, count=${this.count}`+`, countDcc=${this.countDcc}`+`, countForeign=${this.countForeign}`+`, totals=${timapi._TimApiHelpers.arrayAsDebugString(this.totals)}`+`)`}};Object.defineProperty(timapi.Counter,"name",{value:"timapi.Counter"});timapi.TimEvent=class{constructor(){this.terminal=undefined;this.exception=undefined;this.requestType=undefined}toString(){return`TimEvent(exception=${this.exception}, requestType=${this.requestType})`}};Object.defineProperty(timapi.TimEvent,"name",{value:"timapi.TimEvent"});timapi.DeactivateResponse=class{constructor(){this.printData=undefined;this.counters=undefined}toString(){return`${this.constructor.name}(`+`printData=${this.printData}`+`, counters=${this.counters}`+`)`}};Object.defineProperty(timapi.DeactivateResponse,"name",{value:"timapi.DeactivateResponse"});timapi.ScreenshotInformation=class{constructor(){this.imageFileFormat=timapi.constants.ImageFileFormat.png;this.imageWidth=undefined;this.imageHeight=undefined;this.imageData=undefined}toString(){return`${this.constructor.name}(`+`imageFileFormat=${this.imageFileFormat}`+`, imageFileWidth=${this.imageFileWidth}`+`, imageFileHeight=${this.imageFileHeight}`+`, imageData=${timapi._TimApiHelpers.uint8ArrayAsDebugString(this.imageData)}`+`)`}};Object.defineProperty(timapi.ScreenshotInformation,"name",{value:"timapi.ScreenshotInformation"});timapi.FeaturesSixml=class{constructor(){this.adminFunctions=undefined;this.hasAutoCommit=undefined;this.hasAutoShiftManagement=undefined;this.hasAutoShutterManagement=undefined;this.canRequestRepetition=undefined;this.financialFunctions=undefined;this.guides=undefined;this.nonFinancialFunctions=undefined;this.protocolLevel=undefined;this.hasSleepTimer=undefined;this.statusFunctions=undefined;this.dialogFunctions=undefined;this.allowsClosedCardInsertion=undefined;this.hasFastNtfMode=undefined;this.persistentState=undefined;this.ep2ReferencedTransaction=undefined;this.ep2DeferredAuthorisation=undefined;this.deferredAuthorisation=undefined;this.ep2CredentialOnFile=undefined;this.credentialOnFile=undefined}toString(){return`${this.constructor.name}(`+`adminFunctions=${timapi._TimApiHelpers.setAsDebugString(this.adminFunctions)}`+`, hasAutoCommit=${this.hasAutoCommit}`+`, hasAutoShiftManagement=${this.hasAutoShiftManagement}`+`, hasAutoShutterManagement=${this.hasAutoShutterManagement}`+`, canRequestRepetition=${this.canRequestRepetition}`+`, financialFunctions=${timapi._TimApiHelpers.setAsDebugString(this.financialFunctions)}`+`, guides=${timapi._TimApiHelpers.setAsDebugString(this.guides)}`+`, nonFinancialFunctions=${timapi._TimApiHelpers.setAsDebugString(this.nonFinancialFunctions)}`+`, protocolLevel=${this.protocolLevel}`+`, hasSleepTimer=${this.hasSleepTimer}`+`, statusFunctions=${timapi._TimApiHelpers.setAsDebugString(this.statusFunctions)}`+`, dialogFunctions=${timapi._TimApiHelpers.setAsDebugString(this.dialogFunctions)}`+`, allowsClosedCardInsertion=${this.allowsClosedCardInsertion}`+`, hasFastNtfMode=${this.hasFastNtfMode}`+`, persistentState=${this.persistentState}`+`, ep2ReferencedTransaction=${this.ep2ReferencedTransaction}`+`, ep2DeferredAuthorisation=${this.ep2DeferredAuthorisation}`+`, deferredAuthorisation=${this.deferredAuthorisation}`+`, ep2CredentialOnFile=${this.ep2CredentialOnFile}`+`, credentialOnFile=${this.credentialOnFile}`+`)`}};Object.defineProperty(timapi.FeaturesSixml,"name",{value:"timapi.FeaturesSixml"});timapi.FeaturesSixml.Support=new timapi.constants.Enumeration(["disabled","supported","mandatory"],0);timapi.ShowSignatureCaptureRequest=class{constructor(){this.brandBar=[];this.brandMode=timapi.constants.BrandMode.all;this.backgroundColor=new timapi.Color(255,255,255);this.imageFileFormat=timapi.constants.ImageFileFormat.png;this.imageFileWidth=200;this.imageFileHeight=150;this.resourceId=timapi.constants.ResourceId.welcomeCard;this.signatureColor=new timapi.Color(0,0,0);this.theme=timapi.constants.Theme.six;this.timeout=60;this.language=undefined;this.watermarkColor=new timapi.Color(0,0,0);this.watermarkItems=[]}toString(){return`${this.constructor.name}(`+`brandBar=${timapi._TimApiHelpers.arrayAsDebugString(this.brandBar)}`+`, brandMode=${this.brandMode}`+`, backgroundColor=${this.backgroundColor}`+`, imageFileFormat=${this.imageFileFormat}`+`, imageFileWidth=${this.imageFileWidth}`+`, imageFileHeight=${this.imageFileHeight}`+`, resourceId=${this.resourceId}`+`, signatureColor=${this.signatureColor}`+`, theme=${this.theme}`+`, timeout=${this.timeout}`+`, language=${this.language}`+`, watermarkColor=${this.watermarkColor}`+`, watermarkItems=${timapi._TimApiHelpers.arrayAsDebugString(this.watermarkItems)}`+`)`}static copy(showSignatureCaptureRequest){var copy=new timapi.ShowSignatureCaptureRequest;if(showSignatureCaptureRequest.brandBar!==undefined){copy.brandBar=showSignatureCaptureRequest.brandBar.slice()}copy.brandMode=showSignatureCaptureRequest.brandMode;copy.backgroundColor=timapi.Color.copy(showSignatureCaptureRequest.backgroundColor);copy.imageFileFormat=showSignatureCaptureRequest.imageFileFormat;copy.imageFileWidth=showSignatureCaptureRequest.imageFileWidth;copy.imageFileHeight=showSignatureCaptureRequest.imageFileHeight;copy.resourceId=showSignatureCaptureRequest.resourceId;copy.signatureColor=timapi.Color.copy(showSignatureCaptureRequest.signatureColor);copy.theme=showSignatureCaptureRequest.theme;copy.timeout=showSignatureCaptureRequest.timeout;copy.language=showSignatureCaptureRequest.language;copy.watermarkColor=timapi.Color.copy(showSignatureCaptureRequest.watermarkColor);if(showSignatureCaptureRequest.watermarkItems!==undefined){copy.watermarkItems=showSignatureCaptureRequest.watermarkItems.slice()}return copy}};Object.defineProperty(timapi.ShowSignatureCaptureRequest,"name",{value:"timapi.ShowSignatureCaptureRequest"});timapi.HardwareInformationResponse=class{constructor(){this.hardwares=undefined;this.kernelVersions=undefined;this.settings=undefined;this.statistics=undefined;this.batteryLevel=undefined;this.batteryCharging=undefined}toString(){return`${this.constructor.name}(`+`hardwares=${timapi._TimApiHelpers.arrayAsDebugString(this.hardwares)}`+`, kernelVersions=${timapi._TimApiHelpers.mapAsDebugString(this.kernelVersions)}`+`, settings=${timapi._TimApiHelpers.mapAsDebugString(this.settings)}`+`, statistics=${timapi._TimApiHelpers.mapAsDebugString(this.statistics)}`+`, batteryLevel=${this.batteryLevel}`+`, batteryCharging=${this.batteryCharging}`+`)`}};Object.defineProperty(timapi.HardwareInformationResponse,"name",{value:"timapi.HardwareInformationResponse"});timapi.VasResult=class{constructor(){this.vasInfoListType=undefined;this.vasInformation=new Map}toString(){return`${this.constructor.name}(`+`vasInfoListType=${this.vasInfoListType}`+`, vasInformation=${this.vasInformation}`+`)`}static copy(vasResult){var copy=new timapi.VasResult;copy.vasInfoListType=vasResult.vasInfoListType;copy.vasInformation=new Map(vasResult.vasInformation);return copy}};Object.defineProperty(timapi.VasResult,"name",{value:"timapi.VasResult"});timapi.TerminalStatus=class{constructor(){this.displayContent=[];this.connectionStatus=undefined;this.managementStatus=undefined;this.cardReaderStatus=undefined;this.transactionStatus=undefined;this.sleepModeStatus=undefined;this.hasReceiptInformation=undefined;this.cardData=undefined;this.swUpdateAvailable=undefined;this.finalAmount=undefined}toString(){return`${this.constructor.name}(`+`displayContent=${timapi._TimApiHelpers.arrayAsDebugString(this.displayContent)}`+`, connectionStatue=${this.connectionStatus}`+`, managementStatus=${this.managementStatus}`+`, cardReaderStatus=${this.cardReaderStatus}`+`, transactionStatus=${this.transactionStatus}`+`, sleepModeStatus=${this.sleepModeStatus}`+`, receiptInformation=${this.receiptInformation}`+`, cardData=${this.cardData}`+`, swUpdateAvailable=${this.swUpdateAvailable}`+`, finalAmount=${this.finalAmount}`+")"}};Object.defineProperty(timapi.TerminalStatus,"name",{value:"timapi.TerminalStatus"});timapi.NativeError=class{constructor(){this.code=undefined;this.message=undefined;this.source=undefined}toString(){return`${this.constructor.name}(`+`code=${this.code}`+`, message=${this.message}`+`, source=${this.source}`+`)`}};Object.defineProperty(timapi.NativeError,"name",{value:"timapi.NativeError"});timapi.CardData=class{constructor(){this.posEntryMode=undefined;this.aid=undefined;this.acc=undefined;this.cardNumber=undefined;this.cardNumberPrintable=undefined;this.cardNumberPrintableCardholder=undefined;this.cardNumberEnc=undefined;this.cardNumberEncKeyIndex=undefined;this.cardExpiryDate=undefined;this.brandName=undefined;this.tenderName=undefined;this.cardTrackDatas=undefined;this.loyaltyInformation=undefined;this.cardRef=undefined;this.processingDisposition=undefined;this.language=undefined;this.cardCountryCode=undefined;this.terminalCountryCode=undefined;this.uid=undefined;this.asrpd=undefined;this.cardProductType=undefined;this.cardType=undefined;this.cardholder=undefined}toString(){return`${this.constructor.name}(`+`posEntryMode=${this.posEntryMode}`+`, aid=${this.aid}`+`, acc=${this.acc}`+`, cardNumber=${this.cardNumber}`+`, cardNumberPrintable=${this.cardNumberPrintable}`+`, cardNumberPrintableCardholder=${this.cardNumberPrintableCardholder}`+`, cardNumberEnc=${timapi._TimApiHelpers.uint8ArrayAsDebugString(this.cardNumberEnc)}`+`, cardNumberEncKeyIndex=${this.cardNumberEncKeyIndex}`+`, cardExpiryDate=${this.cardExpiryDate}`+`, brandName=${this.brandName}`+`, tenderName=${this.tenderName}`+`, cardTrackDatas=${timapi._TimApiHelpers.arrayAsDebugString(this.cardTrackDatas)}`+`, loyaltyInformations=${timapi._TimApiHelpers.stringUint8MapAsDebugString(this.loyaltyInformations)}`+`, cardRef=${this.cardRef}`+`, processingDisposition=${this.processingDisposition}`+`, language=${this.language}`+`, cardCountryCode=${this.cardCountryCode}`+`, terminalCountryCode=${this.terminalCountryCode}`+`, uid=${this.uid}`+`, asrpd=${this.asrpd}`+`, cardProductType=${this.cardProductType}`+`, cardType=${this.cardType}`+`, cardholder=${this.cardholder}`+`)`}};Object.defineProperty(timapi.CardData,"name",{value:"timapi.CardData"});timapi.SignatureInformation=class{constructor(){this.imageFileFormat=undefined;this.imageWidth=undefined;this.imageHeight=undefined;this.imageData=undefined}toString(){return`${this.constructor.name}(`+`imageFileFormat=${this.imageFileFormat}`+`, imageWidth=${this.imageWidth}`+`, imageHeight=${this.imageHeight}`+`, imageData=${timapi._TimApiHelpers.uint8ArrayAsDebugString(this.imageData)}`+`)`}};Object.defineProperty(timapi.SignatureInformation,"name",{value:"timapi.SignatureInformation"});timapi.SubTransaction=class{constructor(copySubTrx=undefined){this.function=undefined;this.amount=undefined;this.rate=undefined;this.rateExponent=undefined;if(copySubTrx!==undefined){this.function=copySubTrx.function;this.amount=copySubTrx.amount;this.rate=copySubTrx.rate;this.rateExponent=copySubTrx.rateExponent}}toString(){return`${this.constructor.name}(`+`function=${this.function}`+`, amount=${this.amount}`+`, rate=${this.rate}`+`, rateExponent=${this.rateExponent}`+`)`}static copy(subTransaction){var copy=new timapi.SubTransaction;copy.function=subTransaction.function;copy.amount=timapi.Amount.copy(subTransaction.amount);copy.rate=subTransaction.rate;copy.rateExponent=subTransaction.rateExponent;return copy}};Object.defineProperty(timapi.SubTransaction,"name",{value:"timapi.SubTransaction"});timapi.TransactionInformation=class{constructor(){this.posEntryMode=undefined;this.cvm=undefined;this.merchantAction=undefined;this.authCode=undefined;this.timeStamp=undefined;this.transRef=undefined;this.transSeq=undefined;this.acqId=undefined;this.signatureInformation=undefined;this.trmTransRef=undefined;this.acqTransRef=undefined;this.sixTrxRefNum=undefined;this.cardholderName=undefined;this.clientIdentifier=undefined;this.accountNumber=undefined;this.personOid=undefined;this.cardId=undefined;this.ngvUsedFlag=undefined}toString(){return`${this.constructor.name}(`+`posEntryMode=${this.posEntryMode}`+`, cvm=${this.cvm}`+`, merchantAction=${this.merchantAction}`+`, authCode=${this.authCode}`+`, timeStamp=${this.timeStamp}`+`, transRef=${this.transRef}`+`, transSeq=${this.transSeq}`+`, acqId=${this.acqId}`+`, signatureInformation=${this.signatureInformation}`+`, trmTransRef=${this.trmTransRef}`+`, acqTransRef=${this.acqTransRef}`+`, sixTrxRefNum=${this.sixTrxRefNum}`+`, cardholderName=${this.cardholderName}`+`, clientIdentifier=${this.clientIdentifier}`+`, accountNumber=${this.accountNumber}`+`, personOid=${this.personOid}`+`, cardId=${this.cardId}`+`, ngvUsedFlag=${this.ngvUsedFlag}`+`)`}};Object.defineProperty(timapi.TransactionInformation,"name",{value:"timapi.TransactionInformation"});timapi.MerchantOption=class{constructor(type,value){this.type=type;this.value=value;Object.freeze(this)}toString(){return`${this.constructor.name}(`+`type=${this.type}`+`, value=${this.value}`+`)`}};Object.defineProperty(timapi.MerchantOption,"name",{value:"timapi.MerchantOption"});timapi.Hardware=class{constructor(){this.hardwareType=undefined;this.serialNumber=undefined;this.productionDate=undefined;this.productVersion=undefined;this.firmwareVersion=undefined;this.securityStatus=undefined;this.lastCleaningDate=undefined;this.imsi=undefined;this.imei=undefined;this.iccid=undefined;this.hardwareAddress=undefined;this.hardwareDescription=undefined}toString(){return`${this.constructor.name}(`+`hardwareType=${this.hardwareType}`+`, serialNumber=${this.serialNumber}`+`, productionDate=${this.productionDate}`+`, productVersion=${this.productVersion}`+`, firmwareVersion=${this.firmwareVersion}`+`, securityStatus=${this.securityStatus}`+`, lastCleaningDate=${this.lastCleaningDate}`+`, imsi=${this.imsi}`+`, imei=${this.imei}`+`, iccid=${this.iccid}`+`, hardwareAddress=${this.hardwareAddress}`+`, hardwareDescription=${this.hardwareDescription}`+`)`}};Object.defineProperty(timapi.Hardware,"name",{value:"timapi.Hardware"});timapi.CurrencyItem=class{constructor(){this.currency=undefined;this.type=undefined}toString(){return`${this.constructor.name}(`+`currency=${this.currency}`+`, type=${this.type}`+`)`}};Object.defineProperty(timapi.CurrencyItem,"name",{value:"timapi.CurrencyItem"});timapi.AmountDcc=class extends timapi.Amount{constructor(){super(undefined,undefined,undefined);this.rate=undefined;this.rateExponent=undefined;this.markup=undefined;this.markupExponent=undefined;this.rateRegulated=undefined;this.rateExponentRegulated=undefined;this.markupRegulated=undefined;this.markupExponentRegulated=undefined}getRateDecimal(){if(this.rate==undefined||this.rateExponent==undefined){return undefined}let rate=this.rate;if(this.rateExponent>0){for(i=0;i<this.rateExponent;i++){rate/=10}}else if(this.rateExponent<0){for(i=this.rateExponent;i<0;i++){rate*=10}}return rate}getMarkupDecimal(){if(this.markup==undefined||this.markupExponent==undefined){return undefined}let markup=this.markup;if(this.markupExponent>0){for(i=0;i<this.markupExponent;i++){markup/=10}}else if(this.markupExponent<0){for(i=this.markupExponent;i<0;i++){markup*=10}}return markup}getRateDecimalRegulated(){if(this.rateRegulated==undefined||this.rateExponentRegulated==undefined){return undefined}let rateRegulated=this.rateRegulated;if(this.rateExponentRegulated>0){for(i=0;i<this.rateExponentRegulated;i++){rateRegulated/=10}}else if(this.rateExponentRegulated<0){for(i=this.rateExponentRegulated;i<0;i++){rateRegulated*=10}}return rateRegulated}getMarkupDecimalRegulated(){if(this.markupRegulated==undefined||this.markupExponentRegulated==undefined){return undefined}let markupRegulated=this.markupRegulated;if(this.markupExponentRegulated>0){for(i=0;i<this.markupExponentRegulated;i++){markupRegulated/=10}}else if(this.markupExponentRegulated<0){for(i=this.markupExponentRegulated;i<0;i++){markupRegulated*=10}}return markupRegulated}toString(){return`${this.constructor.name}(`+`value=${this.value}`+`, currency=${this.currency}`+`, exponent=${this.exponent}`+`, rate=${this.rate}`+`, rateExponent=${this.rateExponent}`+`, markup=${this.markup}`+`, markupExponent=${this.markupExponent}`+`, rateRegulated=${this.rateRegulated}`+`, rateExponentRegulated=${this.rateExponentRegulated}`+`, markupRegulated=${this.markupRegulated}`+`, markupExponentRegulated=${this.markupExponentRegulated}`+`)`}};Object.defineProperty(timapi.AmountDcc,"name",{value:"timapi.AmountDcc"});timapi.TrxDetail=class{constructor(){this.dccFlag=undefined;this.transactionType=undefined;this.count=undefined;this.amountSum=undefined;this.amountSumTip=undefined;this.amountSumOther=undefined;this.aid=undefined;this.markup=undefined;this.markupExponent=undefined;this.ngvUsedFlag=undefined}toString(){return`${this.constructor.name}(`+`dccFlag=${this.dccFlag}`+`, transactionType=${this.transactionType}`+`, count=${this.count}`+`, amountSum=${this.amountSum}`+`, amountSumTip=${this.amountSumTip}`+`, amountSumOther=${this.amountSumOther}`+`, aid=${this.aid}`+`, markup=${this.markup}`+`, markupExponent=${this.markupExponent}`+`, ngvUsedFlag=${this.ngvUsedFlag}`+`)`}};Object.defineProperty(timapi.TrxDetail,"name",{value:"timapi.TrxDetail"});timapi.CommandRequest=class{constructor(){this.cardReader=undefined;this.order=undefined;this.cardCommand=undefined;this.positiveResource=undefined;this.negativeResource=undefined;this.executionResource=undefined;this.preResource=undefined;this.positiveAnswers=undefined}toString(){return`${this.constructor.name}(`+`cardReader=${this.cardReader}`+`, order=${this.order}`+`, cardCommand=${this.cardCommand}`+`, positiveResource=${this.positiveResource}`+`, negativeResource=${this.negativeResource}`+`, executionResource=${this.executionResource}`+`, preResource=${this.preResource}`+`, positiveAnswers=${timapi._TimApiHelpers.arrayAsDebugString(this.positiveAnswers)}`+`)`}static copy(commandRequest){var copy=new timapi.CommandRequest;copy.cardReader=commandRequest.cardReader;copy.order=commandRequest.order;copy.cardCommand=commandRequest.cardCommand;copy.positiveResource=commandRequest.positiveResource;copy.negativeResource=commandRequest.negativeResource;copy.executionResource=commandRequest.executionResource;copy.preResource=commandRequest.preResource;if(commandRequest.positiveAnswers!==undefined){copy.positiveAnswers=commandRequest.positiveAnswers.slice()}return copy}};Object.defineProperty(timapi.CommandRequest,"name",{value:"timapi.CommandRequest"});timapi.ConfigData=class{constructor(){this.receiptHeader=undefined;this.language=undefined}toString(){return`${this.constructor.name}(`+`receiptHeader=${timapi._TimApiHelpers.arrayAsDebugString(this.receiptHeader)}`,+`, language=${this.language}`+`)`}};Object.defineProperty(timapi.ConfigData,"name",{value:"timapi.ConfigData"});timapi.ShowDialogResponse=class{constructor(){this.reason=undefined;this.userInput=undefined;this.cardData=undefined}toString(){return`${this.constructor.name}(`+`reason=${this.reason}`+`, userInput=${this.userInput}`+`, cardData=${this.cardData}`+`)`}};Object.defineProperty(timapi.ShowDialogResponse,"name",{value:"timapi.ShowDialogResponse"});timapi.LoyaltyDiscount=class extends timapi.Amount{constructor(){super();this.value=undefined;this.currency=undefined;this.exponent=undefined;this.discountDescription=undefined}setValue(){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"Cannot set amount for LoyaltyDiscount."`)}setDecimalValue(){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"Cannot set amount decimal for LoyaltyDiscount."`)}setCurrency(){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"Cannot set currency for LoyaltyDiscount."`)}toString(){return`${this.constructor.name}(`+`value=${this.value}`+`, currency=${this.currency}`+`, exponent=${this.exponent}`+`, discountDescription=${this.discountDescription}`+`)`}};Object.defineProperty(timapi.LoyaltyDiscount,"name",{value:"timapi.LoyaltyDiscount"});timapi.SystemInformationResponse=class{constructor(){this.networkInformation=undefined}toString(){return`${this.constructor.name}(`+`networkInformation=${this.networkInformation}`+`)`}};Object.defineProperty(timapi.SystemInformationResponse,"name",{value:"timapi.SystemInformationResponse"});timapi.CardTrackData=class{constructor(){this.trackNum=undefined;this.data=undefined}toString(){return`${this.constructor.name}(Track: ${this.trackNum}: ${timapi._TimApiHelpers.uint8ArrayAsDebugString(this.data)})`}};Object.defineProperty(timapi.CardTrackData,"name",{value:"timapi.CardTrackData"});timapi.TransactionRequest=class{constructor(){this.userId=undefined;this.amount=undefined;this.transactionData=undefined;this.merchantOptions=[];this.amountDiscount=undefined;this.amountTip=undefined;this.basket=undefined;this.additionalInfo=new Map;this.customerData=new Map;this.amountOther=undefined;this.retainCard=undefined;this.loyaltyCouponList=[]}toString(){return`${this.constructor.name}(`+`userId=${this.userId}`+`, amount=${this.amount}`+`, transactionData=${this.transactionData}`+`, merchantOptions=${timapi._TimApiHelpers.arrayAsDebugString(this.merchantOptions)}`+`, amountDiscount=${this.amountDiscount}`+`, amountTip=${this.amountTip}`+`, basket=${this.basket}`+`, additionalInfo=${timapi._TimApiHelpers.mapAsDebugString(this.additionalInfo)}`+`, customerData=${timapi._TimApiHelpers.mapAsDebugString(this.customerData)}`+`, amountOther=${this.amountOther}`+`, retainCard=${this.retainCard}`+`, loyaltyCouponList=${timapi._TimApiHelpers.arrayAsDebugString(this.loyaltyCouponList)}`+`)`}static copy(transactionRequest){var copy=new timapi.TransactionRequest;copy.userId=transactionRequest.userId;if(transactionRequest.amount!==undefined){copy.amount=timapi.Amount.copy(transactionRequest.amount)}if(transactionRequest.transactionData!==undefined){copy.transactionData=timapi.TransactionData.copy(transactionRequest.transactionData)}if(transactionRequest.merchantOptions!==undefined){copy.merchantOptions=transactionRequest.merchantOptions.slice()}if(transactionRequest.amountDiscount!==undefined){copy.amountDiscount=timapi.AmountDiscount.copy(transactionRequest.amountDiscount)}if(transactionRequest.amountTip!==undefined){copy.amountTip=timapi.Amount.copy(transactionRequest.amountTip)}if(transactionRequest.basket!==undefined){copy.basket=timapi.Basket.copy(transactionRequest.basket)}copy.additionalInfo=new Map(transactionRequest.additionalInfo);copy.customerData=new Map(transactionRequest.customerData);if(transactionRequest.amountOther!==undefined){copy.amountOther=timapi.Amount.copy(transactionRequest.amountOther)}copy.retainCard=transactionRequest.retainCard;if(transactionRequest.loyaltyCouponList!==undefined){copy.loyaltyCouponList=transactionRequest.loyaltyCouponList.slice()}return copy}};Object.defineProperty(timapi.TransactionRequest,"name",{value:"timapi.TransactionRequest"});timapi.CustomReceiptFormatter=class{constructor(){this.lineFormatsCardholder=[];this.lineFormatsMerchant=[];this.lineFormatsSaldo=[];this.emptyValueString="NA"}copy(){let crf=new timapi.CustomReceiptFormatter;this.lineFormatsCardholder.forEach(function(lineFormat){crf.lineFormatsCardholder.push(lineFormat.copy())});this.lineFormatsMerchant.forEach(function(lineFormat){crf.lineFormatsMerchant.push(lineFormat.copy())});this.lineFormatsSaldo.forEach(function(lineFormat){crf.lineFormatsSaldo.push(lineFormat.copy())});crf.emptyValueString=this.emptyValueString;return crf}};Object.defineProperty(timapi.CustomReceiptFormatter,"name",{value:"timapi.CustomReceiptFormatter"});timapi.CustomReceiptFormatter.TextType=new timapi.constants.Enumeration(["text","userId","posId","transactionType","dashedLine","receiptHeader","fieldActId","fieldAccPer","fieldAcqId","fieldAid","fieldAmount","fieldAmountDcc","fieldAmountOther","fieldAuthCode","fieldBrandName","fieldCurrency","fieldCurrencyDcc","fieldDccDisclaimer","fieldDisclaimer","fieldExponent","fieldExponentDcc","fieldMarkupDcc","fieldMarkupExponentDcc","fieldCardNumberPrintableMerchant","fieldCardNumberPrintableCardholder","fieldRateDcc","fieldRateExponentDcc","fieldTimeStampDate","fieldTimeStampTime","fieldTrmId","fieldTrxRefNum","fieldTrxSeqCnt","fieldPosEntryMode","fieldCardExpiryDate","fieldCardNumberEnc","fieldAmountSaldo","fieldEcrSeqCnt","fieldNumberOfInstallments","fieldInstallmentDisclaimer","fieldTenderName","fieldTenderOrBrandName","fieldMarkupDccRegulated","fieldMarkupExponentDccRegulated","fieldRateDccRegulated","fieldRateExponentDccRegulated","fieldOriginalTransRef","fieldKeyPanReceiptIndex"],0);timapi.CustomReceiptFormatter.TextAlignment=new timapi.constants.Enumeration(["left","right","center"],0);timapi.CustomReceiptFormatter.Condition=new timapi.constants.Enumeration(["none","ecrInfo","eftInfo","signature","hasValues","installment","dcc"],0);timapi.CustomReceiptFormatter.TextElement=class{constructor(type,alignment,text,receiptType){this.type=timapi.CustomReceiptFormatter.TextType.text;this.alignment=timapi.CustomReceiptFormatter.TextAlignment.left;this.text="";this.receiptType=undefined;if(type!==undefined){this.type=type}if(alignment!==undefined){this.alignment=alignment}if(text!==undefined){this.text=text}this.receiptType=receiptType}copy(){return new timapi.CustomReceiptFormatter.TextElement(this.type,this.alignment,this.text,this.receiptType)}static createText(text,alignment,receiptType){return new timapi.CustomReceiptFormatter.TextElement(timapi.CustomReceiptFormatter.TextType.text,alignment,text,receiptType)}static createField(field,alignment,receiptType){return new timapi.CustomReceiptFormatter.TextElement(field,alignment,receiptType)}};Object.defineProperty(timapi.CustomReceiptFormatter.TextElement,"name",{value:"timapi.CustomReceiptFormatter.TextElement"});timapi.CustomReceiptFormatter.LineFormat=class{constructor(elements,condition,padding){this.elements=[];this.condition=timapi.CustomReceiptFormatter.Condition.none;this.padding=" ";if(elements!==undefined){this.elements=elements}if(condition!==undefined){this.condition=condition}if(padding!==undefined){this.padding=padding}}copy(){let lf=new timapi.CustomReceiptFormatter.LineFormat;this.elements.forEach(function(element){lf.elements.push(element.copy())});lf.condition=this.condition;lf.padding=this.padding;return lf}};Object.defineProperty(timapi.CustomReceiptFormatter.LineFormat,"name",{value:"timapi.CustomReceiptFormatter.LineFormat"});timapi.CommandResponse=class{constructor(){this.order=undefined;this.responseType=undefined;this.cardResponse=undefined;this.uid=undefined;this.atr=undefined}toString(){return`${this.constructor.name}(`+`order=${this.order}`+`, responseType=${this.responseType}`+`, cardResponse=${this.cardResponse}`+`, uid=${this.uid}`+`, atr=${this.atr}`+`)`}};Object.defineProperty(timapi.CommandResponse,"name",{value:"timapi.CommandResponse"});timapi.BalanceInquiryResponse=class{constructor(){this.amount=undefined;this.printData=undefined;this.cardData=undefined;this.disclaimer=undefined;this.transactionInformation=undefined}toString(){return`${this.constructor.name}(`+`amount=${this.amount}`+`, printData=${this.printData}`+`, cardData=${this.cardData}`+`, disclaimer=${this.disclaimer}`+`, transactionInformation=${this.transactionInformation}`+`)`}};Object.defineProperty(timapi.BalanceInquiryResponse,"name",{value:"timapi.BalanceInquiryResponse"});timapi.Terminal=class{constructor(settings){let t=timapi._TimApiHelpers.prepareTerminal();this._listeners=[];this._listenersLocked=false;let waSettings=timapi._TimApiHelpers.convertTerminalSettings(settings);try{this._wao=new timapi._TimApiHelpers.TAObject;timapi.TimException.assert(t.create(this._wao.p(),waSettings.v()));if(typeof navigator!=="undefined"){let ecrInfo=new timapi.EcrInfo;ecrInfo.name=navigator.userAgent;ecrInfo.architecture=`${navigator.platform}${navigator.oscpu?" "+navigator.oscpu:""}`;ecrInfo.manufacturerName=navigator.vendor;ecrInfo.type=timapi.constants.EcrInfoType.os;this.addEcrData(ecrInfo)}}catch(err){if(this._wao)this._wao.dispose();throw err}finally{if(waSettings)waSettings.dispose()}timapi._TimApiHelpers.terminalMap[this._wao.value()]=this}dispose(){let pointer=this._wao.v();timapi.TimException.assert(timapi._TimApiHelpers.terminal.dispose(pointer));this._wao.dispose();this._wao=undefined;this._listeners=undefined;if(pointer in timapi._TimApiHelpers.terminalMap){delete timapi._TimApiHelpers.terminalMap[pointer]}}assertNotDisposed(){if(typeof this._wao==="undefined"){throw new timapi.TimException(timapi.constants.ResultCode.invalidState,"Terminal instance has been disposed")}}getActSeqCounter(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetActSeqCounter(this)}getBrands(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetBrands(this)}getConfigData(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetConfigData(this)}getLicense(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetLicense(this)}canDcc(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalCanDcc(this)}canDeclinedReceipts(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalCanDeclinedReceipts(this)}getEcrData(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetEcrData(this)}setEcrData(ecrData){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetEcrData(this,ecrData)}addEcrData(ecrInfo){this.assertNotDisposed();timapi._TimApiHelpers.terminalAddEcrData(this,ecrInfo)}getFeatures(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetFeatures(this)}addListener(listener){if(!listener){throw new Error("Listener is null")}if(this._listeners.includes(listener)){throw new Error("Listener is already registered")}if(this._listenersLocked){this._listeners=this._listeners.slice(0);this._listenersLocked=false}this._listeners.push(listener)}removeListener(listener){let index=this._listeners.indexOf(listener);if(index==-1){throw new Error("Listener is not registered")}if(this._listenersLocked){this._listeners=this._listeners.slice(0);this._listenersLocked=false}this._listeners.splice(index,1)}getMerchantOptions(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetMerchantOptions(this)}setMerchantOptions(merchantOptions){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetMerchantOptions(this,merchantOptions)}canMultiAccountSelection(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalCanMultiAccountSelection(this)}getPosId(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetPosId(this)}setPosId(posId){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetPosId(this,posId)}getPrintOptions(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetPrintOptions(this)}setPrintOptions(printOptions){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetPrintOptions(this,printOptions)}getSettings(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetSettings(this)}hasSwUpdate(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalHasSwUpdate(this)}getTerminalId(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetTerminalId(this)}getTerminalStatus(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetTerminalStatus(this)}getTransactionData(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetTransactionData(this)}setTransactionData(transactionData){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetTransactionData(this,transactionData)}getUserId(){this.assertNotDisposed();return timapi._TimApiHelpers.terminalGetUserId(this)}setUserId(userId){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetUserId(this,userId)}setNormalReceiptFormatter(){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetNormalReceiptFormatter(this)}setCompactReceiptFormatter(){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetCompactReceiptFormatter(this)}setSuperCompactReceiptFormatter(){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetSuperCompactReceiptFormatter(this)}setUltraCompactReceiptFormatter(){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetUltraCompactReceiptFormatter(this)}setReceiptFormatter(formatter){this.assertNotDisposed();this._receiptFormatter=formatter}setCustomReceiptFormatter(customReceiptFormatter){this.assertNotDisposed();timapi._TimApiHelpers.terminalSetCustomReceiptFormatter(this,customReceiptFormatter)}activateAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.activate_async(this._wao.v()))}activateServiceMenuAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.activate_service_menu_async(this._wao.v()))}amtAdjustment(amount){this.assertNotDisposed();timapi._TimApiHelpers.amtAdjustment(this,amount)}amtAdjustmentVas(amount,amountDiscount,loyaltyCouponList,loyaltyInformationList){this.assertNotDisposed();timapi._TimApiHelpers.amtAdjustmentVas(this,amount,amountDiscount,loyaltyCouponList,loyaltyInformationList)}applicationInformationAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.application_information_async(this._wao.v()))}balanceAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.balance_async(this._wao.v()))}balanceInquiryAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.balance_inquiry_async(this._wao.v()))}cancel(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.cancel(this._wao.v()))}cancelUnattended(silent,retainCard){this.assertNotDisposed();timapi._TimApiHelpers.cancelUnattended(this,silent,retainCard)}changeSettingsAsync(settings){this.assertNotDisposed();timapi._TimApiHelpers.changeSettingsAsync(this,settings)}closeDialogModeAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.close_dialog_mode_async(this._wao.v()))}closeMaintenanceWindowAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.close_maintenance_window_async(this._wao.v()))}closeReaderAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.close_reader_async(this._wao.v()))}commitAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.commit_async(this._wao.v()))}commitAmountAsync(amount){this.assertNotDisposed();timapi._TimApiHelpers.commitAmountAsync(this,amount)}connectAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.connect_async(this._wao.v()))}counterRequestAsync(counterType){this.assertNotDisposed();timapi._TimApiHelpers.counterRequestAsync(this,counterType)}dccRatesAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.dcc_rates_async(this._wao.v()))}deactivateAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.deactivate_async(this._wao.v()))}disconnectAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.disconnect_async(this._wao.v()))}ejectCardAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.eject_card_async(this._wao.v()))}finishCheckoutAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.finish_checkout_async(this._wao.v()))}hardwareInformationAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.hardware_information_async(this._wao.v()))}holdCommit(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.hold_commit(this._wao.v()))}initTransactionAsync(displayAmount,amount,functionHint){this.assertNotDisposed();timapi._TimApiHelpers.initTransactionAsync(this,displayAmount,amount,functionHint)}loginAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.login_async(this._wao.v()))}logoutAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.logout_async(this._wao.v()))}loyaltyDataAsync(functionType,dataType,data,retainCard){this.assertNotDisposed();timapi._TimApiHelpers.loyaltyDataAsync(this,functionType,dataType,data,retainCard)}openDialogModeAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.open_dialog_mode_async(this._wao.v()))}openMaintenanceWindowAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.open_maintenance_window_async(this._wao.v()))}openReaderAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.open_reader_async(this._wao.v()))}provideLoyaltyBasketAsync(basket){this.assertNotDisposed();timapi._TimApiHelpers.provideLoyaltyBasketAsync(this,basket)}provideVasResultAsync(basket){this.assertNotDisposed();timapi._TimApiHelpers.provideVasResultAsync(this,basket)}rebootAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.reboot_async(this._wao.v()))}receiptRequestAsync(type){this.assertNotDisposed();timapi._TimApiHelpers.receiptRequestAsync(this,type)}reconciliationAsync(){this.assertNotDisposed();timapi._TimApiHelpers.reconciliationAsync(this)}reconfigAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.reconfig_async(this._wao.v()))}rollbackAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.rollback_async(this._wao.v()))}sendCardCommandAsync(requests){this.assertNotDisposed();timapi._TimApiHelpers.sendCardCommandAsync(this,requests)}showDialogAsync(request){this.assertNotDisposed();timapi._TimApiHelpers.showDialogAsync(this,request)}showSignatureCaptureAsync(request){this.assertNotDisposed();timapi._TimApiHelpers.showSignatureCaptureAsync(this,request)}printOnTerminalAsync(ticketData){this.assertNotDisposed();timapi._TimApiHelpers.printOnTerminalAsync(this,ticketData)}softwareUpdateAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.software_update_async(this._wao.v()))}startCheckoutAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.start_checkout_async(this._wao.v()))}systemInformationAsync(){this.assertNotDisposed();timapi.TimException.assert(timapi._TimApiHelpers.terminal.system_information_async(this._wao.v()))}transactionAsync(type,amount){this.assertNotDisposed();timapi._TimApiHelpers.transactionAsync(this,type,amount)}transactionCashbackAsync(type,amount,amountCashback){this.assertNotDisposed();timapi._TimApiHelpers.transactionCashbackAsync(this,type,amount,amountCashback)}transactionTipAsync(type,amount,amountTip){this.assertNotDisposed();timapi._TimApiHelpers.transactionTipAsync(this,type,amount,amountTip)}transactionWithRequestAsync(type,request){this.assertNotDisposed();timapi._TimApiHelpers.transactionWithRequestAsync(this,type,request)}deviceMaintenanceAsync(type){this.assertNotDisposed();timapi._TimApiHelpers.deviceMaintenanceAsync(this,type)}};Object.defineProperty(timapi.Terminal,"name",{value:"timapi.Terminal"});timapi.DefaultTerminalListener=class{activateCompleted(event,data){if(data&&data.printData&&data.printData.receipts&&data.printData.receipts.length>0){this.printReceipts(event.terminal,data.printData)}this.requestCompleted(event,data)}activateServiceMenuCompleted(event){this.requestCompleted(event,undefined)}applicationInformationCompleted(event){this.requestCompleted(event,undefined)}balanceCompleted(event,data){if(data&&data.printData&&data.printData.receipts&&data.printData.receipts.length>0){this.printReceipts(event.terminal,data.printData)}this.requestCompleted(event,data)}balanceInquiryCompleted(event,data){if(data&&data.printData&&data.printData.receipts&&data.printData.receipts.length>0){this.printReceipts(event.terminal,data.printData)}this.requestCompleted(event,data)}changeSettingsCompleted(event){this.requestCompleted(event,undefined)}closeDialogModeCompleted(event){this.requestCompleted(event,undefined)}closeMaintenanceWindowCompleted(event){this.requestCompleted(event,undefined)}closeReaderCompleted(event){this.requestCompleted(event,undefined)}commitCompleted(event,data){if(data&&data.receipts&&data.receipts.length>0){this.printReceipts(event.terminal,data)}this.requestCompleted(event,data)}connectCompleted(event){this.requestCompleted(event,null)}counterRequestCompleted(event,data){this.requestCompleted(event,data)}deactivateCompleted(event,data){if(data&&data.printData&&data.printData.receipts&&data.printData.receipts.length>0){this.printReceipts(event.terminal,data.printData)}this.requestCompleted(event,data)}dccRatesCompleted(event,data){if(data&&data.receipts&&data.receipts.length>0){this.printReceipts(event.terminal,data)}this.requestCompleted(event,data)}disconnected(terminal,exception){}ejectCardCompleted(event){this.requestCompleted(event,undefined)}finishCheckoutCompleted(event,data){this.requestCompleted(event,data)}hardwareInformationCompleted(event,data){this.requestCompleted(event,data)}initTransactionCompleted(event,data){this.requestCompleted(event,data)}loginCompleted(event){this.requestCompleted(event,undefined)}logoutCompleted(event){this.requestCompleted(event,undefined)}loyaltyDataCompleted(event,data){this.requestCompleted(event,data)}openDialogModeCompleted(event){this.requestCompleted(event,undefined)}openMaintenanceWindowCompleted(event){this.requestCompleted(event,undefined)}openReaderCompleted(event){this.requestCompleted(event,undefined)}printReceipts(terminal,printData){}processPrintReceipts(terminal,printData){if(!printData||!printData.receipts||printData.receipts.length==0){return}this.printReceipts(terminal,printData)}provideLoyaltyBasketCompleted(event,data){this.requestCompleted(event,data)}provideVasResultCompleted(event,data){this.requestCompleted(event,data)}rebootCompleted(event){this.requestCompleted(event,undefined)}reconciliationCompleted(event,data){if(data&&data.printData&&data.printData.receipts&&data.printData.receipts.length>0){this.printReceipts(event.terminal,data.printData)}this.requestCompleted(event,data)}receiptRequestCompleted(event,data){if(data&&data.printData&&data.printData.receipts&&data.printData.receipts.length>0){this.printReceipts(event.terminal,data.printData)}this.requestCompleted(event,data)}reconfigCompleted(event,data){if(data&&data.receipts&&data.receipts.length>0){this.printReceipts(event.terminal,data)}this.requestCompleted(event,data)}requestCompleted(event,data){}rollbackCompleted(event,data){if(data&&data.receipts&&data.receipts.length>0){this.printReceipts(event.terminal,data)}this.requestCompleted(event,data)}sendCardCommandCompleted(event,data){this.requestCompleted(event,data)}showSignatureCaptureCompleted(event,data){this.requestCompleted(event,data)}showDialogCompleted(event,data){this.requestCompleted(event,data)}printOnTerminalCompleted(event){this.requestCompleted(event)}softwareUpdateCompleted(event,data){this.requestCompleted(event,data)}startCheckoutCompleted(event){this.requestCompleted(event,undefined)}systemInformationCompleted(event,data){this.requestCompleted(event,data)}terminalStatusChanged(terminal){}transactionCompleted(event,data){if(data&&data.printData&&data.printData.receipts&&data.printData.receipts.length>0){this.processPrintReceipts(event.terminal,data.printData)}if(event.exception&&event.exception.printData&&event.exception.printData.receipts&&event.exception.printData.receipts.length>0){this.processPrintReceipts(event.terminal,event.exception.printData)}this.requestCompleted(event,data)}deviceMaintenanceCompleted(event,data){this.requestCompleted(event)}vasInfo(terminal,vasCheckoutInformation){}deferredAuth(terminal,response){if(response&&response.printData&&response.printData.receipts&&response.printData.receipts.length>0){this.processPrintReceipts(terminal,response.printData)}}screenshot(terminal,info){}licenseChanged(terminal){}errorNotification(terminal,timError){if(timError&&timError.printData&&timError.printData.receipts&&timError.printData.receipts.length>0){this.processPrintReceipts(terminal,timError.printData)}}};Object.defineProperty(timapi.DefaultTerminalListener,"name",{value:"timapi.DefaultTerminalListener"});timapi.AmountFinal=class extends timapi.Amount{constructor(){super();this.value=undefined;this.currency=undefined;this.exponent=undefined;this.adjustmentResult=undefined}setValue(){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"Cannot set amount for AmountFinal."`)}setDecimalValue(){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"Cannot set amount decimal for AmountFinal."`)}setCurrency(){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"Cannot set currency for AmountFinal."`)}toString(){return`${this.constructor.name}(`+`value=${this.value}`+`, currency=${this.currency}`+`, exponent=${this.exponent}`+`, adjustmentResult=${this.adjustmentResult}`+`)`}};Object.defineProperty(timapi.AmountFinal,"name",{value:"timapi.AmountFinal"});timapi.Brand=class{constructor(){this.name=undefined;this.dccAvailable=undefined;this.paymentProtocol=undefined;this.acqId=undefined;this.lastInitDate=undefined;this.applications=undefined;this.currencies=undefined}toString(){return`${this.constructor.name}(`+`name=${this.name}`+`, dccAvailable=${this.dccAvailable}`+`, paymentProtocol=${this.paymentProtocol}`+`, acqId=${this.acqId}`+`, lastInitDate=${this.lastInitDate}`+`, applications=${timapi._TimApiHelpers.arrayAsDebugString(this.applications)}`+`, currencies=${timapi._TimApiHelpers.arrayAsDebugString(this.currencies)}`+`)`}};Object.defineProperty(timapi.Brand,"name",{value:"timapi.Brand"});timapi.TransactionData=class{constructor(copyTrxData=undefined){this.dccAllowed=undefined;this.trxOriginalDate=undefined;this.ecrSeqCounter=undefined;this.partialApprovalAllowed=undefined;this.transRef=undefined;this.transSeq=undefined;this.cardRef=undefined;this.acqId=undefined;this.acqTransRef=undefined;this.trmTransRef=undefined;this.tipAllowed=undefined;this.phoneAuthCode=undefined;this.language=undefined;this.multiCurrencyFlag=undefined;this.ngvMode=undefined;this.ngvClearingDelay=undefined;this.cvc2=undefined;this.appExpirationDate=undefined;this.sixTrxRefNum=undefined;this.installmentAllowed=undefined;this.deferredAuthInd=undefined;this.subTransactions=[];if(copyTrxData!==undefined){this.dccAllowed=copyTrxData.dccAllowed;this.trxOriginalDate=copyTrxData.trxOriginalDate;this.ecrSeqCounter=copyTrxData.ecrSeqCounter;this.partialApprovalAllowed=copyTrxData.partialApprovalAllowed;this.transRef=copyTrxData.transRef;this.transSeq=copyTrxData.transSeq;this.cardRef=copyTrxData.cardRef;this.acqId=copyTrxData.acqId;this.acqTransRef=copyTrxData.acqTransRef;this.trmTransRef=copyTrxData.trmTransRef;this.tipAllowed=copyTrxData.tipAllowed;this.phoneAuthCode=copyTrxData.phoneAuthCode;this.language=copyTrxData.language;this.multiCurrencyFlag=copyTrxData.multiCurrencyFlag;this.ngvMode=copyTrxData.ngvMode;this.ngvClearingDelay=copyTrxData.ngvClearingDelay;this.cvc2=copyTrxData.cvc2;this.appExpirationDate=copyTrxData.appExpirationDate;this.sixTrxRefNum=copyTrxData.sixTrxRefNum;this.installmentAllowed=copyTrxData.installmentAllowed;this.deferredAuthInd=copyTrxData.deferredAuthInd;this.subTransactions=copyTrxData.subTransactions.slice()}}setAcqTransRef(acqId,trxRefNo){if(acqId<0||trxRefNo<0){throw new Error("Illegal Argument: acqId and trxRefNo may not be negative.")}this.acqTransRef=`014${acqId}${trxRefNo}`}toString(){return`${this.constructor.name}(`+`dccAllowed=${this.dccAllowed}`+`, trxOriginalDate=${this.trxOriginalDate}`+`, ecrSeqCounter=${this.ecrSeqCounter}`+`, partialApprovalAllowed=${this.partialApprovalAllowed}`+`, transRef=${this.transRef}`+`, transSeq=${this.transSeq}`+`, cardRef=${this.cardRef}`+`, acqId=${this.acqId}`+`, acqTransRef=${this.acqTransRef}`+`, trmTransRef=${this.trmTransRef}`+`, tipAllowed=${this.tipAllowed}`+`, phoneAuthCode=${this.phoneAuthCode}`+`, language=${this.language}`+`, multiCurrencyFlag=${this.multiCurrencyFlag}`+`, ngvMode=${this.ngvMode}`+`, ngvClearingDelay=${this.ngvClearingDelay}`+`, cvc2=${this.cvc2}`+`, appExpirationDate=${this.appExpirationDate}`+`, sixTrxRefNum=${this.sixTrxRefNum}`+`, installmentAllowed=${this.installmentAllowed}`+`, deferredAuthInd=${this.deferredAuthInd}`+`, subTransactions=${this.subTransactions}`+`)`}static copy(transactionData){var copy=new timapi.TransactionData;copy.dccAllowed=transactionData.dccAllowed;copy.trxOriginalDate=timapi.TimeDate.copy(transactionData.trxOriginalDate);copy.ecrSeqCounter=transactionData.ecrSeqCounter;copy.partialApprovalAllowed=transactionData.partialApprovalAllowed;copy.transRef=transactionData.transRef;copy.transSeq=transactionData.transSeq;copy.cardRef=transactionData.cardRef;copy.acqId=transactionData.acqId;copy.acqTransRef=transactionData.acqTransRef;copy.trmTransRef=transactionData.trmTransRef;copy.tipAllowed=transactionData.tipAllowed;copy.phoneAuthCode=transactionData.phoneAuthCode;copy.language=transactionData.language;copy.multiCurrencyFlag=transactionData.multiCurrencyFlag;copy.ngvMode=transactionData.ngvMode;copy.ngvClearingDelay=transactionData.ngvClearingDelay;copy.cvc2=transactionData.cvc2;copy.appExpirationDate=transactionData.appExpirationDate;copy.sixTrxRefNum=transactionData.sixTrxRefNum;copy.installmentAllowed=transactionData.installmentAllowed;copy.deferredAuthInd=transactionData.deferredAuthInd;copy.subTransactions=transactionData.subTransactions.slice();return copy}};Object.defineProperty(timapi.TransactionData,"name",{value:"timapi.TransactionData"});timapi.Counters=class{constructor(){this.counterType=undefined;this.seqCounter=undefined;this.counters=undefined}toString(){return`${this.constructor.name}(`+`counterType=${this.counterType}`+`, seqCounter=${this.seqCounter}`+`, counters=${timapi._TimApiHelpers.arrayAsDebugString(this.counters)}`+`)`}};Object.defineProperty(timapi.Counters,"name",{value:"timapi.Counters"});timapi.Color=class{constructor(r,g,b){if(r<0||r>255){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"R-value for a color must be within 0 ... 255"`)}this.r=r;if(g<0||g>255){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"G-value for a color must be within 0 ... 255"`)}this.g=g;if(b<0||b>255){throw new timapi.TimException(timapi.constants.ResultCode.invalidArgument,`"B-value for a color must be within 0 ... 255"`)}this.b=b}toString(){return`${this.constructor.name}(`+`r=${this.r}`+`, g=${this.g}`+`, b=${this.b}`+`)`}static copy(color){return new timapi.Color(color.r,color.g,color.b)}};Object.defineProperty(timapi.Color,"name",{value:"timapi.Color"});timapi.EcrInfo=class{constructor(){this.type=undefined;this.name=undefined;this.manufacturerName=undefined;this.version=undefined;this.serialNumber=undefined;this.architecture=undefined;this.integratorSolution=undefined;this.remoteIp=undefined}toString(){return`${this.constructor.name}(`+`type=${this.type}`+`, name=${this.name}`+`, manufacturerName=${this.manufacturerName}`+`, version=${this.version}`+`, serialNumber=${this.serialNumber}`+`, architecture=${this.architecture}`+`, integratorSolution=${this.integratorSolution}`+`, remoteIp=${this.remoteIp}`+`)`}};Object.defineProperty(timapi.EcrInfo,"name",{value:"timapi.EcrInfo"});timapi.TerminalSettings=class{constructor(){this.connectionIPString="";this.connectionIPPort=80;this.protocolType=timapi.constants.ProtocolType.sixml;this.integratorId="0";this.guides=new Set;this.guides.add(timapi.constants.Guides.retail);this.manufacturerFlags=0;this.fetchBrands=false;this.autoCommit=undefined;this.autoShiftManagement=true;this.autoShutterManagement=true;this.cardInsertionTimeout=60;this.cardRemovalTimeout=60;this.commitTimeout=60;this.proceedTimeout=60;this.dcc=true;this.partialApproval=false;this.allowClosedCardInsert=false;this.tipAllowed=true;this.fastNtfMode=false;this.requestRepetition=0;this.enableKeepAlive=true;this.persistentState=false}static copy(settings){var copy=new timapi.TerminalSettings;copy.connectionIPString=settings.connectionIPString;copy.connectionIPPort=settings.connectionIPPort;copy.protocolType=settings.protocolType;copy.integratorId=settings.integratorId;copy.guides=new Set(settings.guides);copy.manufacturerFlags=settings.manufacturerFlags;copy.fetchBrands=settings.fetchBrands;copy.autoCommit=settings.autoCommit;copy.autoShiftManagement=settings.autoShiftManagement;copy.autoShutterManagement=settings.autoShutterManagement;copy.cardInsertionTimeout=settings.cardInsertionTimeout;copy.cardRemovalTimeout=settings.cardRemovalTimeout;copy.commitTimeout=settings.commitTimeout;copy.dcc=settings.dcc;copy.partialApproval=settings.partialApproval;copy.proceedTimeout=settings.proceedTimeout;copy.allowClosedCardInsert=settings.allowClosedCardInsert;copy.tipAllowed=settings.tipAllowed;copy.fastNtfMode=settings.fastNtfMode;copy.requestRepetition=settings.requestRepetition;copy.enableKeepAlive=settings.enableKeepAlive;copy.persistentState=settings.persistentState;return copy}toString(){return`${this.constructor.name}(`+`connectionIPString=${this.connectionIPString}`+`, connectionIPPort=${this.connectionIPPort}`+`, protocolType=${this.protocolType}`+`, integratorId=${this.integratorId}`+`, guides=${timapi._TimApiHelpers.setAsDebugString(this.guides)}`+`, manufacturerFlags=${this.manufacturerFlags}`+`, fetchBrands=${this.fetchBrands}`+`, autoCommit=${this.autoCommit}`+`, autoShiftManagement=${this.autoShiftManagement}`+`, autoShutterManagement=${this.autoShutterManagement}`+`, cardInsertionTimeout=${this.cardInsertionTimeout}`+`, cardRemovalTimeout=${this.cardRemovalTimeout}`+`, commitTimeout=${this.commitTimeout}`+`, dcc=${this.dcc}`+`, partialApproval=${this.partialApproval}`+`, proceedTimeout=${this.proceedTimeout}`+`, allowClosedCardInsert=${this.allowClosedCardInsert}`+`, tipAllowed=${this.tipAllowed}`+`, fastNtfMode=${this.fastNtfMode}`+`, requestRepetition=${this.requestRepetition}`+`, enableKeepAlive=${this.enableKeepAlive}`+`, persistentState=${this.persistentState}`+")"}};Object.defineProperty(timapi.TerminalSettings,"name",{value:"timapi.TerminalSettings"});timapi.DefaultReceiptFormatter=class{formatReceipt(terminal,receiptItems){return[]}};Object.defineProperty(timapi.DefaultReceiptFormatter,"name",{value:"timapi.DefaultReceiptFormatter"});timapi._TimApiHelpers.TAInteger=class extends timapi._TimApiHelpers.TAObject{constructor(value){super(value)}static create(value){if(value===undefined){throw new Error("Content is undefined")}let f=timapi._TimApiHelpers.prepareInteger();let wa_integer=new timapi._TimApiHelpers.TAInteger;wa_integer._value=value;try{let split=timapi._TimApiHelpers.convert64BitTo32Bit(value);timapi.TimException.assert(f.create(wa_integer.p(),split.low,split.high,split.sign))}catch(err){wa_integer.dispose();throw err}return wa_integer}replace(value){let newInteger=timapi._TimApiHelpers.TAInteger.create(value);try{this.assign(newInteger)}finally{newInteger.dispose()}return this.v()}replaceOrUndef(value){if(value){return this.replace(value)}this.releaseIfValid();return this.v()}};_useNodeJSW3CWebSocket=false;if(typeof WebSocket==="undefined"){WebSocket=require("websocket").w3cwebsocket;_useNodeJSW3CWebSocket=true}timapi._TimApiHelpers.CommWebSocket=class{constructor(ip,port,slot){let t=timapi._TimApiHelpers;if(!t.CommWebSocket._functions){t.CommWebSocket._functions={onOpen:t.getFunction2("TAWAOnWebSocketOpen",["number"]),onClosed:t.getFunction2("TAWAOnWebSocketClosed",["number"]),onError:t.getFunction2("TAWAOnWebSocketError",["number"]),onMessage:t.getFunction2("TAWAOnWebSocketMessage",["number","array","number"])}}this._slot=slot;this._url=`ws://${ip}:${port}/SIXml`;this._protocol="SIXml";this._state=timapi._TimApiHelpers.CommWebSocket._states.connecting;this._createSocket()}sendMessage(message){this._socket.send(message)}close(){this._socket.onopen=undefined;this._socket.onmessage=undefined;this._socket.onclose=undefined;this._socket.onerror=undefined;this._state=timapi._TimApiHelpers.CommWebSocket._states.closing;this._slot=undefined;this._socket.close()}_onSocketOpen(event){if(this._slot===undefined){return}this._state=timapi._TimApiHelpers.CommWebSocket._states.connected;timapi._TimApiHelpers.CommWebSocket._functions.onOpen(this._slot)}_onSocketClosed(event){if(this._slot===undefined){return}if(this._state===timapi._TimApiHelpers.CommWebSocket._states.connecting){if(this._protocol){this._socket.onopen=undefined;this._socket.onmessage=undefined;this._socket.onclose=undefined;this._socket.onerror=undefined;this._socket.close();this._socket=undefined;this._protocol=undefined;let myself=this;setTimeout(function(){myself._createSocket()},100)}else{timapi._TimApiHelpers.CommWebSocket._functions.onClosed(this._slot)}}else if(this._state==timapi._TimApiHelpers.CommWebSocket._states.disconnecting){}else{timapi._TimApiHelpers.CommWebSocket._functions.onClosed(this._slot)}}_onSocketError(event){if(this._slot===undefined){return}}_onSocketMessage(event){if(this._slot===undefined){return}let data=new Uint8Array(event.data);timapi._TimApiHelpers.CommWebSocket._functions.onMessage(this._slot,data,data.length)}_createSocket(){if(_useNodeJSW3CWebSocket){let origin="http://localhost";let headers=null;let requestOptions=null;let clientConfig=null;this._socket=new WebSocket(this._url,this._protocol,origin,headers,requestOptions,clientConfig)}else{this._socket=new WebSocket(this._url,this._protocol)}this._socket.binaryType="arraybuffer";this._socket.onopen=this._onSocketOpen.bind(this);this._socket.onmessage=this._onSocketMessage.bind(this);this._socket.onclose=this._onSocketClosed.bind(this)}static createSocket(slot,ip,port){if(slot in timapi._TimApiHelpers.CommWebSocket._sockets){throw new Error("Internal error")}timapi._TimApiHelpers.CommWebSocket._sockets[slot]=new timapi._TimApiHelpers.CommWebSocket(ip,port,slot)}static sendMessage(slot,message){let socket=timapi._TimApiHelpers.CommWebSocket._sockets[slot];if(socket===undefined){throw new Error("Internal error")}socket.sendMessage(message)}static closeSocket(slot){let socket=timapi._TimApiHelpers.CommWebSocket._sockets[slot];if(socket===undefined){return}delete timapi._TimApiHelpers.CommWebSocket._sockets[slot];socket._slot=undefined;socket._state=timapi._TimApiHelpers.CommWebSocket._states.closing;socket.close()}};timapi._TimApiHelpers.CommWebSocket._sockets={};timapi._TimApiHelpers.CommWebSocket._functions=undefined;timapi._TimApiHelpers.CommWebSocket._states=Object.freeze({connecting:1,connected:2,closing:3});timapi._TimApiHelpers.TAString=class extends timapi._TimApiHelpers.TAObject{constructor(value){super(value)}static prepareString(){let f=timapi._TimApiHelpers.TAString._functions;if(!f){let g=timapi._TimApiHelpers.getFunction;f=timapi._TimApiHelpers.TAString._functions={create:g("ta_string_create",["string","number"]),create_from_binary:g("ta_string_create",["number","number"]),get_pointer:g("ta_string_get_pointer",["number"]),get_length:g("ta_string_get_length",["number"])}}return f}static create(content){if(content===undefined){throw new timapi.TimException(timapi.constants.ResultCode.systemError,"Content is undefined")}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.TAString.prepareString();let string;try{string=new timapi._TimApiHelpers.TAString;string._content=content;h.assert(f.create(string.p(),content,Module.lengthBytesUTF8(content)))}catch(err){if(string)string.dispose();throw err}return string}static createFromUint8Array(uint8Array){if(uint8Array===undefined){throw new timapi.TimException(timapi.constants.ResultCode.systemError,"Content is undefined")}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.TAString.prepareString();let string;let mem_c_string;try{string=new timapi._TimApiHelpers.TAString;mem_c_string=Module._malloc(uint8Array.byteLength);for(let i=0;i<uint8Array.byteLength;i++){Module.setValue(mem_c_string+i,uint8Array[i])}h.assert(f.create_from_binary(string.p(),mem_c_string,uint8Array.byteLength))}catch(err){string.dispose();throw err}finally{if(mem_c_string)Module._free(mem_c_string)}return string}static contentOf(string){let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.TAString.prepareString();let p=new timapi._TimApiHelpers.TAPointer;let s;try{h.assert(f.get_length(string.v(),p.p()));let len=p.v();h.assert(f.get_pointer(string.v(),p.p()));s=UTF8ToString(p.v(),len)}finally{p.dispose()}return s}static contentOfIfValid(string){if(string.isValid()){return timapi._TimApiHelpers.TAString.contentOf(string)}return undefined}static contentOfUint8Array(string){let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.TAString.prepareString();let p=new timapi._TimApiHelpers.TAPointer;let s;try{h.assert(f.get_length(string.v(),p.p()));let len=p.v();s=new Uint8Array(len);h.assert(f.get_pointer(string.v(),p.p()));for(let i=0;i<len;i++){s[i]=Module.getValue(p.v()+i)}}finally{p.dispose()}return s}static contentOfUint8ArrayIfValid(string){if(string.isValid()){return timapi._TimApiHelpers.TAString.contentOfUint8Array(string)}return undefined}static unwrapString(c_string){if(c_string===0){return undefined}let h=timapi._TimApiHelpers.prepareHelpers();let f=timapi._TimApiHelpers.TAString.prepareString();let string;let p;try{p=new timapi._TimApiHelpers.TAPointer;h.assert(f.get_length(c_string,p.p()));let len=p.v();h.assert(f.get_pointer(c_string,p.p()));string=UTF8ToString(p.v(),len)}finally{if(p)p.dispose()}return string}replace(content){let newString=timapi._TimApiHelpers.TAString.create(content);try{this.assign(newString)}finally{newString.dispose()}return this.value()}replaceUint8Array(content){let newString=timapi._TimApiHelpers.TAString.createFromUint8Array(content);try{this.assign(newString)}finally{newString.dispose()}return this.value()}replaceOrUndef(content){if(content){return this.replace(content)}this.releaseIfValid();return this.value()}content(){if(!this.isValid()){this._content=undefined;return undefined}if(!this._content){let pointer=new timapi._TimApiHelpers.TAPointer;let assert=timapi.TimException.assert;let f=timapi._TimApiHelpers.TAString.prepareString();try{assert(f.get_length(this.value(),pointer.pointer()));let len=pointer.value();assert(f.get_pointer(this.value(),pointer.pointer()));this._content=UTF8ToString(pointer.value(),len)}finally{pointer.dispose()}}return this._content}};timapi._TimApiHelpers.TAString._functions=undefined;timapi._TimApiHelpers.TAPointer=class{constructor(value){this._pointer=Module._malloc(4);Module.setValue(this._pointer,value||0,"i32")}pointer(){return this._pointer}p(){return this._pointer}value(){return Module.getValue(this._pointer,"i32")}v(){return Module.getValue(this._pointer,"i32")}setObject(value){Module.setValue(this._pointer,value,"i32")}isValid(){return this.value()!=0}dispose(){Module._free(this._pointer)}};timapi._TimApiHelpers.TimerWrapper=class{constructor(slot,repeat,timeoutMS){this._slot=slot;this._timeout=undefined;this._interval=undefined;let myself=this;if(repeat){this._interval=setInterval(function(){myself._onTimeout()},timeoutMS)}else{this._timeout=setTimeout(function(){myself._onTimeout()},timeoutMS)}}_onTimeout(){if(this._slot===undefined){return}if(!timapi._TimApiHelpers.TimerWrapper._TAWAOnTimeout){timapi._TimApiHelpers.TimerWrapper._TAWAOnTimeout=timapi._TimApiHelpers.getFunction2("TAWAOnTimeout",["number"])}let slot=this._slot;if(this._timeout){delete timapi._TimApiHelpers.TimerWrapper._timers[this._slot];this._slot=undefined}timapi._TimApiHelpers.TimerWrapper._TAWAOnTimeout(slot)}static startTimeout(slot,repeat,timeoutMS){timapi._TimApiHelpers.TimerWrapper.cancelTimeout(slot);timapi._TimApiHelpers.TimerWrapper._timers[slot]=new timapi._TimApiHelpers.TimerWrapper(slot,repeat,timeoutMS)}static cancelTimeout(slot){let timer=timapi._TimApiHelpers.TimerWrapper._timers[slot];if(timer===undefined){return}timer._slot=undefined;delete timapi._TimApiHelpers.TimerWrapper._timers[slot];if(timer._timeout){clearTimeout(timer._timeout);timer._timeout=undefined}else if(timer._interval){clearInterval(timer._interval);timer._interval=undefined}}};timapi._TimApiHelpers.TimerWrapper._timers={};timapi._TimApiHelpers.TimerWrapper._TAWAOnTimeout=undefined;timapi.constants.Cvm=new timapi.constants.Enumeration(["pin","pinSignature","signature","onDevice","noCvm"]);timapi.constants.BrandMode=new timapi.constants.Enumeration(["all","selected","off"]);timapi.constants.ProtocolType=new timapi.constants.Enumeration(["sixml"]);timapi.constants.VasInfoListType=new timapi.constants.Enumeration(["wolMatch","wolProgram"]);timapi.constants.ConnectionMode=new timapi.constants.Enumeration(["broadcast","onFixIp"]);timapi.constants.HardwareType=new timapi.constants.Enumeration(["terminal","eftApplication","pinPad","contactReader","contactlessReader","magStripeReader","wifiAdapter","lanAdapter","bluetoothAdapter","mobileAdapter"]);timapi.constants.CustomerDataType=new timapi.constants.Enumeration(["customerId","loyaltyBrandId","furtherInfo"]);timapi.constants.PaymentProtocol=new timapi.constants.Enumeration(["ep2","ev","vm","v3cxml"]);timapi.constants.PrintFlag=new timapi.constants.Enumeration(["suppressHeader","suppressSignature","suppressEcrInfo","suppressEftInfo","suppressFooter"]);timapi.constants.Currency=new timapi.constants.Enumeration([["AED",Object.freeze({code:"AED",name:"UAE Dirham",exponent:2,_wav:0,toString:function(){return this.code}})],["AFN",Object.freeze({code:"AFN",name:"Afghani",exponent:2,_wav:1,toString:function(){return this.code}})],["ALL",Object.freeze({code:"ALL",name:"Lek",exponent:2,_wav:2,toString:function(){return this.code}})],["AMD",Object.freeze({code:"AMD",name:"Armenian Dram",exponent:2,_wav:3,toString:function(){return this.code}})],["ANG",Object.freeze({code:"ANG",name:"Netherlands Antillean Guilder",exponent:2,_wav:4,toString:function(){return this.code}})],["AOA",Object.freeze({code:"AOA",name:"Kwanza",exponent:2,_wav:5,toString:function(){return this.code}})],["ARS",Object.freeze({code:"ARS",name:"Argentine Peso",exponent:2,_wav:6,toString:function(){return this.code}})],["AUD",Object.freeze({code:"AUD",name:"Australian Dollar",exponent:2,_wav:7,toString:function(){return this.code}})],["AWG",Object.freeze({code:"AWG",name:"Aruban Florin",exponent:2,_wav:8,toString:function(){return this.code}})],["AZN",Object.freeze({code:"AZN",name:"Azerbaijanian Manat",exponent:2,_wav:9,toString:function(){return this.code}})],["BAM",Object.freeze({code:"BAM",name:"Convertible Mark",exponent:2,_wav:10,toString:function(){return this.code}})],["BBD",Object.freeze({code:"BBD",name:"Barbados Dollar",exponent:2,_wav:11,toString:function(){return this.code}})],["BDT",Object.freeze({code:"BDT",name:"Taka",exponent:2,_wav:12,toString:function(){return this.code}})],["BGN",Object.freeze({code:"BGN",name:"Bulgarian Lev",exponent:2,_wav:13,toString:function(){return this.code}})],["BHD",Object.freeze({code:"BHD",name:"Bahraini Dinar",exponent:3,_wav:14,toString:function(){return this.code}})],["BIF",Object.freeze({code:"BIF",name:"Burundi Franc",exponent:0,_wav:15,toString:function(){return this.code}})],["BMD",Object.freeze({code:"BMD",name:"Bermudian Dollar",exponent:2,_wav:16,toString:function(){return this.code}})],["BND",Object.freeze({code:"BND",name:"Brunei Dollar",exponent:2,_wav:17,toString:function(){return this.code}})],["BOB",Object.freeze({code:"BOB",name:"Boliviano",exponent:2,_wav:18,toString:function(){return this.code}})],["BOV",Object.freeze({code:"BOV",name:"Mvdol",exponent:2,_wav:19,toString:function(){return this.code}})],["BRL",Object.freeze({code:"BRL",name:"Brazilian Real",exponent:2,_wav:20,toString:function(){return this.code}})],["BSD",Object.freeze({code:"BSD",name:"Bahamian Dollar",exponent:2,_wav:21,toString:function(){return this.code}})],["BTN",Object.freeze({code:"BTN",name:"Ngultrum",exponent:2,_wav:22,toString:function(){return this.code}})],["BWP",Object.freeze({code:"BWP",name:"Pula",exponent:2,_wav:23,toString:function(){return this.code}})],["BYN",Object.freeze({code:"BYN",name:"Belarusian Ruble",exponent:2,_wav:24,toString:function(){return this.code}})],["BYR",Object.freeze({code:"BYR",name:"Belarusian Ruble",exponent:0,_wav:25,toString:function(){return this.code}})],["BZD",Object.freeze({code:"BZD",name:"Belize Dollar",exponent:2,_wav:26,toString:function(){return this.code}})],["CAD",Object.freeze({code:"CAD",name:"Canadian Dollar",exponent:2,_wav:27,toString:function(){return this.code}})],["CDF",Object.freeze({code:"CDF",name:"Congolese Franc",exponent:2,_wav:28,toString:function(){return this.code}})],["CHE",Object.freeze({code:"CHE",name:"WIR Euro",exponent:2,_wav:29,toString:function(){return this.code}})],["CHF",Object.freeze({code:"CHF",name:"Swiss Franc",exponent:2,_wav:30,toString:function(){return this.code}})],["CHW",Object.freeze({code:"CHW",name:"WIR Franc",exponent:2,_wav:31,toString:function(){return this.code}})],["CLF",Object.freeze({code:"CLF",name:"Unidad de Fomento",exponent:4,_wav:32,toString:function(){return this.code}})],["CLP",Object.freeze({code:"CLP",name:"Chilean Peso",exponent:0,_wav:33,toString:function(){return this.code}})],["CNY",Object.freeze({code:"CNY",name:"Yuan Renminbi",exponent:2,_wav:34,toString:function(){return this.code}})],["COP",Object.freeze({code:"COP",name:"Colombian Peso",exponent:2,_wav:35,toString:function(){return this.code}})],["COU",Object.freeze({code:"COU",name:"Unidad de Valor Real",exponent:2,_wav:36,toString:function(){return this.code}})],["CRC",Object.freeze({code:"CRC",name:"Costa Rican Colon",exponent:2,_wav:37,toString:function(){return this.code}})],["CUC",Object.freeze({code:"CUC",name:"Peso Convertible",exponent:2,_wav:38,toString:function(){return this.code}})],["CUP",Object.freeze({code:"CUP",name:"Cuban Peso",exponent:2,_wav:39,toString:function(){return this.code}})],["CVE",Object.freeze({code:"CVE",name:"Cabo Verde Escudo",exponent:2,_wav:40,toString:function(){return this.code}})],["CZK",Object.freeze({code:"CZK",name:"Czech Koruna",exponent:2,_wav:41,toString:function(){return this.code}})],["DJF",Object.freeze({code:"DJF",name:"Djibouti Franc",exponent:0,_wav:42,toString:function(){return this.code}})],["DKK",Object.freeze({code:"DKK",name:"Danish Krone",exponent:2,_wav:43,toString:function(){return this.code}})],["DOP",Object.freeze({code:"DOP",name:"Dominican Peso",exponent:2,_wav:44,toString:function(){return this.code}})],["DZD",Object.freeze({code:"DZD",name:"Algerian Dinar",exponent:2,_wav:45,toString:function(){return this.code}})],["EGP",Object.freeze({code:"EGP",name:"Egyptian Pound",exponent:2,_wav:46,toString:function(){return this.code}})],["ERN",Object.freeze({code:"ERN",name:"Nakfa",exponent:2,_wav:47,toString:function(){return this.code}})],["ETB",Object.freeze({code:"ETB",name:"Ethiopian Birr",exponent:2,_wav:48,toString:function(){return this.code}})],["EUR",Object.freeze({code:"EUR",name:"Euro",exponent:2,_wav:49,toString:function(){return this.code}})],["FJD",Object.freeze({code:"FJD",name:"Fiji Dollar",exponent:2,_wav:50,toString:function(){return this.code}})],["FKP",Object.freeze({code:"FKP",name:"Falkland Islands Pound",exponent:2,_wav:51,toString:function(){return this.code}})],["GBP",Object.freeze({code:"GBP",name:"Pound Sterling",exponent:2,_wav:52,toString:function(){return this.code}})],["GEL",Object.freeze({code:"GEL",name:"Lari",exponent:2,_wav:53,toString:function(){return this.code}})],["GHS",Object.freeze({code:"GHS",name:"Ghana Cedi",exponent:2,_wav:54,toString:function(){return this.code}})],["GIP",Object.freeze({code:"GIP",name:"Gibraltar Pound",exponent:2,_wav:55,toString:function(){return this.code}})],["GMD",Object.freeze({code:"GMD",name:"Dalasi",exponent:2,_wav:56,toString:function(){return this.code}})],["GNF",Object.freeze({code:"GNF",name:"Guinea Franc",exponent:0,_wav:57,toString:function(){return this.code}})],["GTQ",Object.freeze({code:"GTQ",name:"Quetzal",exponent:2,_wav:58,toString:function(){return this.code}})],["GYD",Object.freeze({code:"GYD",name:"Guyana Dollar",exponent:2,_wav:59,toString:function(){return this.code}})],["HKD",Object.freeze({code:"HKD",name:"Hong Kong Dollar",exponent:2,_wav:60,toString:function(){return this.code}})],["HNL",Object.freeze({code:"HNL",name:"Lempira",exponent:2,_wav:61,toString:function(){return this.code}})],["HRK",Object.freeze({code:"HRK",name:"Kuna",exponent:2,_wav:62,toString:function(){return this.code}})],["HTG",Object.freeze({code:"HTG",name:"Gourde",exponent:2,_wav:63,toString:function(){return this.code}})],["HUF",Object.freeze({code:"HUF",name:"Forint",exponent:2,_wav:64,toString:function(){return this.code}})],["IDR",Object.freeze({code:"IDR",name:"Rupiah",exponent:2,_wav:65,toString:function(){return this.code}})],["ILS",Object.freeze({code:"ILS",name:"New Israeli Sheqel",exponent:2,_wav:66,toString:function(){return this.code}})],["INR",Object.freeze({code:"INR",name:"Indian Rupee",exponent:2,_wav:67,toString:function(){return this.code}})],["IQD",Object.freeze({code:"IQD",name:"Iraqi Dinar",exponent:3,_wav:68,toString:function(){return this.code}})],["IRR",Object.freeze({code:"IRR",name:"Iranian Rial",exponent:2,_wav:69,toString:function(){return this.code}})],["ISK",Object.freeze({code:"ISK",name:"Iceland Krona",exponent:0,_wav:70,toString:function(){return this.code}})],["JMD",Object.freeze({code:"JMD",name:"Jamaican Dollar",exponent:2,_wav:71,toString:function(){return this.code}})],["JOD",Object.freeze({code:"JOD",name:"Jordanian Dinar",exponent:3,_wav:72,toString:function(){return this.code}})],["JPY",Object.freeze({code:"JPY",name:"Yen",exponent:0,_wav:73,toString:function(){return this.code}})],["KES",Object.freeze({code:"KES",name:"Kenyan Shilling",exponent:2,_wav:74,toString:function(){return this.code}})],["KGS",Object.freeze({code:"KGS",name:"Som",exponent:2,_wav:75,toString:function(){return this.code}})],["KHR",Object.freeze({code:"KHR",name:"Riel",exponent:2,_wav:76,toString:function(){return this.code}})],["KMF",Object.freeze({code:"KMF",name:"Comoro Franc",exponent:0,_wav:77,toString:function(){return this.code}})],["KPW",Object.freeze({code:"KPW",name:"North Korean Won",exponent:2,_wav:78,toString:function(){return this.code}})],["KRW",Object.freeze({code:"KRW",name:"Won",exponent:0,_wav:79,toString:function(){return this.code}})],["KWD",Object.freeze({code:"KWD",name:"Kuwaiti Dinar",exponent:3,_wav:80,toString:function(){return this.code}})],["KYD",Object.freeze({code:"KYD",name:"Cayman Islands Dollar",exponent:2,_wav:81,toString:function(){return this.code}})],["KZT",Object.freeze({code:"KZT",name:"Tenge",exponent:2,_wav:82,toString:function(){return this.code}})],["LAK",Object.freeze({code:"LAK",name:"Kip",exponent:2,_wav:83,toString:function(){return this.code}})],["LBP",Object.freeze({code:"LBP",name:"Lebanese Pound",exponent:2,_wav:84,toString:function(){return this.code}})],["LKR",Object.freeze({code:"LKR",name:"Sri Lanka Rupee",exponent:2,_wav:85,toString:function(){return this.code}})],["LRD",Object.freeze({code:"LRD",name:"Liberian Dollar",exponent:2,_wav:86,toString:function(){return this.code}})],["LSL",Object.freeze({code:"LSL",name:"Loti",exponent:2,_wav:87,toString:function(){return this.code}})],["LYD",Object.freeze({code:"LYD",name:"Libyan Dinar",exponent:3,_wav:88,toString:function(){return this.code}})],["MAD",Object.freeze({code:"MAD",name:"Moroccan Dirham",exponent:2,_wav:89,toString:function(){return this.code}})],["MDL",Object.freeze({code:"MDL",name:"Moldovan Leu",exponent:2,_wav:90,toString:function(){return this.code}})],["MGA",Object.freeze({code:"MGA",name:"Malagasy Ariary",exponent:2,_wav:91,toString:function(){return this.code}})],["MKD",Object.freeze({code:"MKD",name:"Denar",exponent:2,_wav:92,toString:function(){return this.code}})],["MMK",Object.freeze({code:"MMK",name:"Kyat",exponent:2,_wav:93,toString:function(){return this.code}})],["MNT",Object.freeze({code:"MNT",name:"Tugrik",exponent:2,_wav:94,toString:function(){return this.code}})],["MOP",Object.freeze({code:"MOP",name:"Pataca",exponent:2,_wav:95,toString:function(){return this.code}})],["MRO",Object.freeze({code:"MRO",name:"Ouguiya",exponent:2,_wav:96,toString:function(){return this.code}})],["MUR",Object.freeze({code:"MUR",name:"Mauritius Rupee",exponent:2,_wav:97,toString:function(){return this.code}})],["MVR",Object.freeze({code:"MVR",name:"Rufiyaa",exponent:2,_wav:98,toString:function(){return this.code}})],["MWK",Object.freeze({code:"MWK",name:"Malawi Kwacha",exponent:2,_wav:99,toString:function(){return this.code}})],["MXN",Object.freeze({code:"MXN",name:"Mexican Peso",exponent:2,_wav:100,toString:function(){return this.code}})],["MXV",Object.freeze({code:"MXV",name:"Mexican Unidad de Inversion (UDI)",exponent:2,_wav:101,toString:function(){return this.code}})],["MYR",Object.freeze({code:"MYR",name:"Malaysian Ringgit",exponent:2,_wav:102,toString:function(){return this.code}})],["MZN",Object.freeze({code:"MZN",name:"Mozambique Metical",exponent:2,_wav:103,toString:function(){return this.code}})],["NAD",Object.freeze({code:"NAD",name:"Namibia Dollar",exponent:2,_wav:104,toString:function(){return this.code}})],["NGN",Object.freeze({code:"NGN",name:"Naira",exponent:2,_wav:105,toString:function(){return this.code}})],["NIO",Object.freeze({code:"NIO",name:"Cordoba Oro",exponent:2,_wav:106,toString:function(){return this.code}})],["NOK",Object.freeze({code:"NOK",name:"Norwegian Krone",exponent:2,_wav:107,toString:function(){return this.code}})],["NPR",Object.freeze({code:"NPR",name:"Nepalese Rupee",exponent:2,_wav:108,toString:function(){return this.code}})],["NZD",Object.freeze({code:"NZD",name:"New Zealand Dollar",exponent:2,_wav:109,toString:function(){return this.code}})],["OMR",Object.freeze({code:"OMR",name:"Rial Omani",exponent:3,_wav:110,toString:function(){return this.code}})],["PAB",Object.freeze({code:"PAB",name:"Balboa",exponent:2,_wav:111,toString:function(){return this.code}})],["PEN",Object.freeze({code:"PEN",name:"Sol",exponent:2,_wav:112,toString:function(){return this.code}})],["PGK",Object.freeze({code:"PGK",name:"Kina",exponent:2,_wav:113,toString:function(){return this.code}})],["PHP",Object.freeze({code:"PHP",name:"Philippine Peso",exponent:2,_wav:114,toString:function(){return this.code}})],["PKR",Object.freeze({code:"PKR",name:"Pakistan Rupee",exponent:2,_wav:115,toString:function(){return this.code}})],["PLN",Object.freeze({code:"PLN",name:"Zloty",exponent:2,_wav:116,toString:function(){return this.code}})],["PTS",Object.freeze({code:"PTS",name:"VM Loyalty",exponent:0,_wav:117,toString:function(){return this.code}})],["PYG",Object.freeze({code:"PYG",name:"Guarani",exponent:0,_wav:118,toString:function(){return this.code}})],["QAR",Object.freeze({code:"QAR",name:"Qatari Rial",exponent:2,_wav:119,toString:function(){return this.code}})],["RON",Object.freeze({code:"RON",name:"Romanian Leu",exponent:2,_wav:120,toString:function(){return this.code}})],["RSD",Object.freeze({code:"RSD",name:"Serbian Dinar",exponent:2,_wav:121,toString:function(){return this.code}})],["RUB",Object.freeze({code:"RUB",name:"Russian Ruble",exponent:2,_wav:122,toString:function(){return this.code}})],["RWF",Object.freeze({code:"RWF",name:"Rwanda Franc",exponent:0,_wav:123,toString:function(){return this.code}})],["SAR",Object.freeze({code:"SAR",name:"Saudi Riyal",exponent:2,_wav:124,toString:function(){return this.code}})],["SBD",Object.freeze({code:"SBD",name:"Solomon Islands Dollar",exponent:2,_wav:125,toString:function(){return this.code}})],["SCR",Object.freeze({code:"SCR",name:"Seychelles Rupee",exponent:2,_wav:126,toString:function(){return this.code}})],["SDG",Object.freeze({code:"SDG",name:"Sudanese Pound",exponent:2,_wav:127,toString:function(){return this.code}})],["SEK",Object.freeze({code:"SEK",name:"Swedish Krona",exponent:2,_wav:128,toString:function(){return this.code}})],["SGD",Object.freeze({code:"SGD",name:"Singapore Dollar",exponent:2,_wav:129,toString:function(){return this.code}})],["SHP",Object.freeze({code:"SHP",name:"Saint Helena Pound",exponent:2,_wav:130,toString:function(){return this.code}})],["SLL",Object.freeze({code:"SLL",name:"Leone",exponent:2,_wav:131,toString:function(){return this.code}})],["SOS",Object.freeze({code:"SOS",name:"Somali Shilling",exponent:2,_wav:132,toString:function(){return this.code}})],["SRD",Object.freeze({code:"SRD",name:"Surinam Dollar",exponent:2,_wav:133,toString:function(){return this.code}})],["SSP",Object.freeze({code:"SSP",name:"South Sudanese Pound",exponent:2,_wav:134,toString:function(){return this.code}})],["STD",Object.freeze({code:"STD",name:"Dobra",exponent:2,_wav:135,toString:function(){return this.code}})],["SVC",Object.freeze({code:"SVC",name:"El Salvador Colon",exponent:2,_wav:136,toString:function(){return this.code}})],["SYP",Object.freeze({code:"SYP",name:"Syrian Pound",exponent:2,_wav:137,toString:function(){return this.code}})],["SZL",Object.freeze({code:"SZL",name:"Lilangeni",exponent:2,_wav:138,toString:function(){return this.code}})],["THB",Object.freeze({code:"THB",name:"Baht",exponent:2,_wav:139,toString:function(){return this.code}})],["TJS",Object.freeze({code:"TJS",name:"Somoni",exponent:2,_wav:140,toString:function(){return this.code}})],["TMT",Object.freeze({code:"TMT",name:"Turkmenistan New Manat",exponent:2,_wav:141,toString:function(){return this.code}})],["TND",Object.freeze({code:"TND",name:"Tunisian Dinar",exponent:3,_wav:142,toString:function(){return this.code}})],["TOP",Object.freeze({code:"TOP",name:"Pa’anga",exponent:2,_wav:143,toString:function(){return this.code}})],["TRY",Object.freeze({code:"TRY",name:"Turkish Lira",exponent:2,_wav:144,toString:function(){return this.code}})],["TTD",Object.freeze({code:"TTD",name:"Trinidad and Tobago Dollar",exponent:2,_wav:145,toString:function(){return this.code}})],["TWD",Object.freeze({code:"TWD",name:"New Taiwan Dollar",exponent:2,_wav:146,toString:function(){return this.code}})],["TZS",Object.freeze({code:"TZS",name:"Tanzanian Shilling",exponent:2,_wav:147,toString:function(){return this.code}})],["UAH",Object.freeze({code:"UAH",name:"Hryvnia",exponent:2,_wav:148,toString:function(){return this.code}})],["UGX",Object.freeze({code:"UGX",name:"Uganda Shilling",exponent:0,_wav:149,toString:function(){return this.code}})],["USD",Object.freeze({code:"USD",name:"US Dollar",exponent:2,_wav:150,toString:function(){return this.code}})],["USN",Object.freeze({code:"USN",name:"US Dollar (Next day)",exponent:2,_wav:151,toString:function(){return this.code}})],["UYI",Object.freeze({code:"UYI",name:"Uruguay Peso en Unidades Indexadas (URUIURUI)",exponent:0,_wav:152,toString:function(){return this.code}})],["UYU",Object.freeze({code:"UYU",name:"Peso Uruguayo",exponent:2,_wav:153,toString:function(){return this.code}})],["UZS",Object.freeze({code:"UZS",name:"Uzbekistan Sum",exponent:2,_wav:154,toString:function(){return this.code}})],["VEF",Object.freeze({code:"VEF",name:"Bolívar",exponent:2,_wav:155,toString:function(){return this.code}})],["VND",Object.freeze({code:"VND",name:"Dong",exponent:0,_wav:156,toString:function(){return this.code}})],["VUV",Object.freeze({code:"VUV",name:"Vatu",exponent:0,_wav:157,toString:function(){return this.code}})],["WST",Object.freeze({code:"WST",name:"Tala",exponent:2,_wav:158,toString:function(){return this.code}})],["XAF",Object.freeze({code:"XAF",name:"CFA Franc BEAC",exponent:0,_wav:159,toString:function(){return this.code}})],["XAG",Object.freeze({code:"XAG",name:"Silver",exponent:0,_wav:160,toString:function(){return this.code}})],["XAU",Object.freeze({code:"XAU",name:"Gold",exponent:0,_wav:161,toString:function(){return this.code}})],["XBA",Object.freeze({code:"XBA",name:"Bond Markets Unit European Composite Unit (EURCO)",exponent:0,_wav:162,toString:function(){return this.code}})],["XBB",Object.freeze({code:"XBB",name:"Bond Markets Unit European Monetary Unit (E.M.U.-6)",exponent:0,_wav:163,toString:function(){return this.code}})],["XBC",Object.freeze({code:"XBC",name:"Bond Markets Unit European Unit of Account 9 (E.U.A.-9)",exponent:0,_wav:164,toString:function(){return this.code}})],["XBD",Object.freeze({code:"XBD",name:"Bond Markets Unit European Unit of Account 17 (E.U.A.-17)",exponent:0,_wav:165,toString:function(){return this.code}})],["XCD",Object.freeze({code:"XCD",name:"East Caribbean Dollar",exponent:2,_wav:166,toString:function(){return this.code}})],["XDR",Object.freeze({code:"XDR",name:"SDR (Special Drawing Right)",exponent:0,_wav:167,toString:function(){return this.code}})],["XOF",Object.freeze({code:"XOF",name:"CFA Franc BCEAO",exponent:0,_wav:168,toString:function(){return this.code}})],["XPD",Object.freeze({code:"XPD",name:"Palladium",exponent:0,_wav:169,toString:function(){return this.code}})],["XPF",Object.freeze({code:"XPF",name:"CFP Franc",exponent:0,_wav:170,toString:function(){return this.code}})],["XPT",Object.freeze({code:"XPT",name:"Platinum",exponent:0,_wav:171,toString:function(){return this.code}})],["XSU",Object.freeze({code:"XSU",name:"Sucre",exponent:0,_wav:172,toString:function(){return this.code}})],["XTS",Object.freeze({code:"XTS",name:"Codes specifically reserved for testing purposes",exponent:0,_wav:173,toString:function(){return this.code}})],["XUA",Object.freeze({code:"XUA",name:"ADB Unit of Account",exponent:0,_wav:174,toString:function(){return this.code}})],["XXX",Object.freeze({code:"XXX",name:"The codes assigned for transactions where no currency is involved",exponent:0,_wav:175,toString:function(){return this.code}})],["YER",Object.freeze({code:"YER",name:"Yemeni Rial",exponent:2,_wav:176,toString:function(){return this.code}})],["ZAR",Object.freeze({code:"ZAR",name:"Rand",exponent:2,_wav:177,toString:function(){return this.code}})],["ZMW",Object.freeze({code:"ZMW",name:"Zambian Kwacha",exponent:2,_wav:178,toString:function(){return this.code}})],["ZWL",Object.freeze({code:"ZWL",name:"Zimbabwe Dollar",exponent:2,_wav:179,toString:function(){return this.code}})]]);timapi.constants.ReceiptItemType=new timapi.constants.Enumeration(["actId","accPer","acqId","aid","amount","amountDcc","amountOther","amountReservation","amountSaldo","amountTip","authCode","authReslt","authRespC","authRespTextC","brandName","currency","currencyDcc","dccDisclaimer","disclaimer","exponent","exponentDcc","markupDcc","markupExponentDcc","cardNumberPrintableMerchant","cardNumberPrintableCardholder","rateDcc","rateExponentDcc","timeStampDate","timeStampTime","trmId","trxRefNum","trxSeqCnt","posEntryMode","cardExpiryDate","cardNumberEnc","ecrSeqCounter","panReceiptDol","panReceiptDolIndex","preAuthorizationExpDate","tenderName","numberOfInstallments","installmentDisclaimer","originalAid","originalCardNumberPrintable","originalBrandName","originalCardCountryCode","originalTenderName","originalTransRef","amountInstallmentFee","amountInstallmentTotal","amountInstallmentFirst","amountInstallmentOne","interestInstallment","markupDccRegulated","markupExponentDccRegulated","rateDccRegulated","rateExponentDccRegulated","keyPanReceiptIndex"]);timapi.constants.RequestType=new timapi.constants.Enumeration(["connect","activate","applicationInformation","balance","changeSettings","commit","counterRequest","deactivate","dccRates","hardwareInformation","initTransaction","login","logout","reboot","reconciliation","receiptRequest","reconfig","rollback","systemInformation","softwareUpdate","transaction","closeReader","openReader","ejectCard","openMaintenanceWindow","closeMaintenanceWindow","activateServiceMenu","openDialogMode","closeDialogMode","showSignatureCapture","showDialog","sendCardCommand","printOnTerminal","loyaltyData","startCheckout","finishCheckout","provideLoyaltyBasket","provideVasResult","balanceInquiry","requestAlias","deviceMaintenance"]);timapi.constants.MerchantOptionType=new timapi.constants.Enumeration(["additionalMerchantData","multiAccountIndex","multiContractIndex","merchantTid","clerkIdentifier","fuelDispenserNumber","posdnumber","receiptNumber","shiftNumber"]);timapi.constants.FinancialTransactions=new timapi.constants.Enumeration(["purchase","credit","reversal","preAuthorization","cashAdvance","commit","rollback","giro","combined","authorizeCredit","authorizeDeposit","finalizePurchase","proceed","purchaseForcedAcceptance","purchaseWithCashback","purchasePhoneAuthorized","loadVoucher","collectPoints","purchaseReservation","purchaseReservationPhoneAuthorized","amtAdjustment","purchasePhoneOrdered","purchaseMailOrdered","activateCard","load","unload","funding","refunding","debtRecovery"]);timapi.constants.CouponRejectionReason=new timapi.constants.Enumeration(["alreadyRedeemed","articleDelisted","campaignExpired","campaignCancelled","other"]);timapi.constants.ResourceParameterType=new timapi.constants.Enumeration(["codeCheckType","codeCheckKeyId","codeCheckData","regularDataQueryType","regularData","petrolCardData1","petrolCardData2","petrolCardData3","activeReaders","exponent"]);timapi.constants.ManagementStatus=new timapi.constants.Enumeration(["closed","open","dialog"]);timapi.constants.RemoteFunctions=new timapi.constants.Enumeration(["screenshot","deviceMaintenance"]);timapi.constants.ResourceId=new timapi.constants.Enumeration(["enterMileage","enterCarNumber","enterDriverCode","enterFleetId","selectPump","selectWashingStation","selectProgram","selectProduct","loadCard","readCode","checkCode","enterAdditionalInformation","enterCostCenter","enterEmployeeNumber","enterLicensePlate","enterProjectNumber","petrolPrintReceipt","petrolRemoveCard","petrolOutOfOrder","petrolInvalidEntry","petrolRefuelAtPump","petrolSeeOtherScreen","petrolPleaseRefuel","petrolPleaseRefuelForAmt","petrolInsertCard","petrolPleaseWait","petrolLastCodeTry","petrolCodeTriesLeft","petrolNoCodeTriesLeft","petrolCodeIncorrect","petrolLastTankcodeTry","petrolTankcodeTriesLeft","petrolNoTankcodeTriesLeft","petrolTankcodeIncorrect","petrolCardInverted","petrolCardOutOfOrder","petrolCardNotAuthorised","petrolCardUnknown","petrolCardExpired","petrolCardBlocked","petrolCardRefused","petrolCardRefusedByHost","petrolCardWithoutPaymentFunction","petrolCardOperationAborted","petrolCardProductNotAllowed","petrolCardProductNotAvailable","petrolCardAmountTooLow","petrolCardTransctionStillInProgress","petrolCardLimitReached","petrolCardPersonalisationCard","petrolCardDualCardSystem","petrolCardInsertSecondCard","petrolCardDualCardSystemInsertSecondCard","petrolPumpNotAvailable","petrolPumpNoPumpFree","petrolPumpStoppedRefueling","petrolPumpRefuelingInProgress","petrolPumpProductNotAvailable","petrolPumpRefuelingAborted","petrolPumpPressOk","petrolWelcomeClosed","petrolWelcome","petrolWelcomeShopOpen","petrolBanknoteInsertBanknote","petrolBanknoteCreditAmount","petrolBanknoteCreditAmountInsertBanknote","petrolBanknoteNoCredit","petrolBanknoteOrCards","petrolBanknoteOnly","petrolBanknoteCardsOnly","petrolBanknoteCreditAmountPumpPressOk","petrolPaymentInShop","petrolPaymentInShopPressBtn","petrolPaymentAtPumpPressBtn","petrolPaymenSelectShopPump","petrolPaymenSeeOtherDisplay","petrolPrintReceiptNotAvailable","petrolPrintPressKeyForSafeReceipt","petrolPrintOutOfOrder","petrolPrintReceiptInPrint","petrolVas","petrolVasAmountCheckingScancode","petrolVasWaitCheckingScancode","petrolVasVoucherUsedUp","petrolVasVoucherInvalidBlocked","petrolVasVoucherExpired","petrolVasVoucherCannotBeEvaluated","petrolVasCumulusScanned","petrolVasDiscountBonScanned","petrolVasEnterScancode","petrolVasDiscountVoucherNotActive","petrolVasClubsmartScanned","petrolVasGiftcardAlreadyActive","petrolVasGiftcardNotPossible","petrolMaintenanceServiceRequired","petrolMaintenanceTechnicalProblem","petrolMaintenanceDoorOpen","petrolMaintenanceCommFailure","petrolMaintenanceHostOutOfOrder","postNoRechargeCard","postShowPhoneNumberWithAmount","postEnterPhoneOrPrepaidNumber","postPhoneNumberTooLong","postPhoneNumberTooShort","postEnterPhoneNumber","postPleaseWait","postProcessingDeclined","postProcessingOk","postRegisterCard","postWelcomeCard","postShowPhoneNumber","postInterAccountTransfer","postInterAccountTransferConfirmation","postDisbursementFromAccount","postDepositOwnAccount","postEmptyDialog","postPacketAcknowledgement","postPinEntry","bankingInsertCard","bankingPinCheck","bankingShowSaldo","bankingConfirmAmount","bankingRemoveCard","bankingWelcome","commonRegular","commonPleaseWait","commonDataSaved","commonCardInitialized","commonCardDeinitialized","commonRemoveCard","migrosGkkpinCheck"]);timapi.constants.SettingType=new timapi.constants.Enumeration(["displayBrightness","displayContrast","keypadTones","alertTones","language","powerManagementMode"]);timapi.constants.NgvMode=new timapi.constants.Enumeration(["mandatory","allowedWithFallback","notAllowed"]);timapi.constants.UpdateStatus=new timapi.constants.Enumeration(["upToDate","runningNoReboot","runningReboot"]);timapi.constants.Theme=new timapi.constants.Enumeration(["six","swissPost"]);timapi.constants.ProtocolLevel=new timapi.constants.Enumeration(["sixml2_2"]);timapi.constants.ReceiptType=new timapi.constants.Enumeration(["activateCard","adjustReservation","balanceInquiry","cashAdvance","cancelReservation","credit","confirmReservation","finalizePurchase","load","preAuthorization","purchase","purchaseForcedAcceptance","purchaseMailOrdered","purchasePhoneAuthorized","purchasePhoneOrdered","purchaseReservation","purchaseWithCashback","reservation","reversal"]);timapi.constants.ResultCode=new timapi.constants.Enumeration(["ok","apiCancelEcr","apiInvalidAnswer","apiDisabledFeature","apiFunctionDisallowed","apiPersistencyProblem","apiConnectFailServer","apiConnectFailTerminal","apiConnectionLostServer","apiConnectionLostTerminal","ethernetDisconnected","rs232Disconnected","apiTimeoutServer","apiTimeoutTerminal","serverInvalidAnswer","serverInvalidRequest","serverDisabledFeature","serverPersistencyProblem","serverConnectFailTerminal","serverConnectionLostTerminal","serverTimeoutTerminal","ccrUnavailable","mcrUnavailable","nfcUnavailable","displayUnavailable","pinPadUnavailable","rs232Unavailable","rs232NotConfigured","swInstallationFailed","swVersionNotSuitable","swAuthenticationFailed","cardReaderErrorCcr","cardReaderErrorMcr","cardReaderErrorNfc","cardErrorCcr","cardErrorMcr","cardErrorNfc","cardReadError","cardReadTimeout","cardInsertionTimeout","cardReaderKeysLost","cardReaderSecurityError","cardTimeout","cardNotReadable","cardInvalidData","cardFunctionNotFound","cardFunctionNotAllowed","cardUnexpectedlyPresentInReader","pinPadSecurityError","pinPadTampered","pinPadKeysLost","cardholderStop","cardholderTimeout","cardRemoved","timTimeoutEcr","timConnectFailPaymentHost","timConnectionLostPaymentHost","timTimeoutAnswerRs232","timCommunicationFailure","timConfigFailure","timInitFailure","sixmlGeneralError","sixmlInvalidRequest","sixmlWrongCashier","sixmlWrongEcrId","sixmlUnknownReferenceNumber","sixmlWrongState","busyOtherController","busyMaintenance","requestPending","sixmlUnsupportedRequest","trxNoCommonApplications","trxLimitExceeded","trxNoCommonCvm","declinedCvmFailed","trxReferral","trxInvalidAuthResponse","declinedGeneric","declinedSaldoTooLow","declinedWrongPin","declinedCardBlocked","declinedSecurityIssue","declinedUsageControl","declinedDoubleTransaction","declinedGenericFirstAc","declinedGenericSecondAc","trxCommitTimeout","trxRollbackImpossible","cashbackAmountTooLow","cashbackAmountTooHigh","basketDeclined","noTrxInGroupExceeded","unsupportedCharactersInMessage","loyaltyCheckInPending","declinedCardError","declinedCardExpired","declinedTrxInvalid","declinedTryLater","declinedTryAnotherInterface","declinedInvalidMerchant","declinedRestrictionDeclined","declinedWrongCurrency","declinedAutoreversalPending","declinedWrongCardNumber","declinedWrongCardExpiryDate","declinedRetryTemporaryUnavailable","declinedServiceNotAllowed","declinedCardholderInformationIssue","declinedReferralWrongAuthCode","declinedReferralWrongAmount","declinedReferralOtherReason","declinedCaptureCardGeneric","declinedCaptureCardInfoToClient","declinedCaptureCardOrderToClient","declinedCaptureCardTimeoutRemovingCard","declinedNotSupported","valueOutOfRangeInThisContext","voucherTypeNotAvailable","transactionMismatch"]);timapi.constants.TransactionType=new timapi.constants.Enumeration(["purchase","credit","reversal","preAuthorization","finalizePurchase","debtRecovery","cashAdvance","purchaseForcedAcceptance","purchaseWithCashback","purchasePhoneAuthorized","purchasePhoneOrdered","purchaseMailOrdered","accountVerification","giro","combined","authorizeCredit","authorizeDeposit","reservation","adjustReservation","cancelReservation","purchaseReservation","purchaseReservationPhoneAuthorized","loadVoucher","collectPoints","funding","refunding","load","unload","activateCard"]);timapi.constants.LoyaltyFunctionType=new timapi.constants.Enumeration(["init","update","deinit","query"]);timapi.constants.PrintFormat=new timapi.constants.Enumeration(["noPrint","normal","onDevice","fieldsOnly"]);timapi.constants.CardReader=new timapi.constants.Enumeration(["icc","cl","ms"]);timapi.constants.ResponseType=new timapi.constants.Enumeration(["positive","negative","cardRemoval","timeout"]);timapi.constants.VasInfoType=new timapi.constants.Enumeration(["wolaltId","woltrxMatch","woltrxId","woltrxIdentTime","woluserAlias","wolcardAlias","wolagrDataToEcrindicator","wolagrReqAddVasdataIndicator","wolagrResDataToHstIndicator","wolamtVasprg","wolvasprgCtnr","wolvasprgId","wolvasprgName","wolvasprgMtchId","wolvasspecId","woldataToEcrind","wolreqAddVasdataInd","woladdVasdataComMd","wolresDataToHstInd","woladdVasdata","wolsupportVer"]);timapi.constants.NonFinancialTransactions=new timapi.constants.Enumeration(["cancel","balanceInquiry","clientIdentification","initTransaction","holdCommit","reservation","adjustReservation","cancelReservation","loyaltyData","startCheckout","finishCheckout","provideLoyaltyBasket","provideVasResult","accountVerification"]);timapi.constants.CurrencyType=new timapi.constants.Enumeration(["local","foreign","dcc"]);timapi.constants.PosEntryMode=new timapi.constants.Enumeration(["unspecified","manual","magStripeIncomplete","barCode","ocr","icc","referenceBased","bluetoothLowEnergy","qrcOnTerminal","qrcOnMobile","tokenBasedEcommerce","magStripe","magStripeFallback","magStripeFallbackAgain","magStripeFallbackIccFail","emergencyDataEntry","ecommerce","ctlessIcc","ctlessMagStrige"]);timapi.constants.StatusFunctions=new timapi.constants.Enumeration(["featureRequest","terminalStatus","systemInformation","applicationInformation","hardwareInformation","keepAlive","licenseChanged"]);timapi.constants.CardProductType=new timapi.constants.Enumeration(["debit","credit","commercial","prepaid"]);timapi.constants.SleepModeStatus=new timapi.constants.Enumeration(["enteringSleep","wakingUp"]);timapi.constants.SecurityStatus=new timapi.constants.Enumeration(["disabled","active","tampered"]);timapi.constants.AdjustmentResult=new timapi.constants.Enumeration(["ok","failRequestToLate","failAmountToHigh"]);timapi.constants.AdminFunctions=new timapi.constants.Enumeration(["login","logout","reconfig","reboot","softwareUpdate","activate","deactivate","counterRequest","reconciliation","balance","openDialogMode","closeDialogMode","transmitLog","startReaderCleaning","dccRates","changeSettings","receiptRequest","closeReader","openReader","ejectCard","openMaintenanceWindow","closeMaintenanceWindow","activateServiceMenu"]);timapi.constants.KernelType=new timapi.constants.Enumeration(["kernelPure","emvContact","entryPoint","kernel1","kernel2","kernel3","kernel4","kernel5","kernel6","kernel7"]);timapi.constants.CardReaderStatus=new timapi.constants.Enumeration(["cardReaderClosed","cardManuallyEntered","cardSwiped","cardNotRemoved","cardPresented","cardReaderEmpty","cardInserted","cardEjected"]);timapi.constants.FunctionHint=new timapi.constants.Enumeration(["purchase","credit","reversal","preAuthorization","finalizePurchase","cashAdvance","purchaseForcedAcceptance","purchaseWithCashback","purchasePhoneAuthorized","purchasePhoneOrdered","purchaseMailOrdered","giro","combined","authorizeCredit","authorizeDeposit","reservation","loadVoucher","collectPoints"]);timapi.constants.BrandBarBrand=new timapi.constants.Enumeration(["alipay","amex","aralRoutex","aurora","austroCard","bancomat","bcmc","boncardPay","boncardPoints","bonus","bonuscard","bpRoutex","brunschwig","businessCard","cash","co2neutralCard","conforama","coopEkz","coopMobile","coopProntoCard","cosyCard","cup","diners","dinerClub","diplomatkarte","dkv","eniRoutex","eshellPrepaid","esso","euroShell","fnac","freiUndFlott","giftc","giftCard","groupCard","ipRoutex","iq","jcb","jelmoliPaycard","jet","jgeschenkkarte","jubinCardPetrol","lebara","loeb","logPay","lunchCheckCard","lycaMobile","maestro","maestroCh","masterCard","mbudgetMc","mcard","mediaMarkt","mergerCard","migrolcard","migros","moveri","mscompanyCard","myOne","novofleet","omvRoutex","paycard","paycardF","paycardApp","paySysGiftcard","pharmacard","phonecard","postCard","powerCard","reka","rekaLunch","rekaRail","salt","sbbkarte","shellMca","staedtekarte","statoilRoutex","sunrise","supercardPlus","supercardVisa","swissBonusCard","swisscom","twint","utaFullService","utaSelectCard","valuemaster","visa","visaElectron","vorteilsCard","vpay","weChat","wirCard","yallo"]);timapi.constants.MaintenanceType=new timapi.constants.Enumeration(["cleanReaders","calibrateTouchScreen","transmitTeld"]);timapi.constants.ImageFileFormat=new timapi.constants.Enumeration(["jpeg","png","bmp"]);timapi.constants.Guides=new timapi.constants.Enumeration(["retail","unattended","advancedRetail","banking","petrol","dialog","remote","gastro","hospitality","valueAddedServices","austrianUseCases","certification"]);timapi.constants.ConnectionStatus=new timapi.constants.Enumeration(["disconnected","loggedOut","loggedIn"]);timapi.constants.TransactionReason=new timapi.constants.Enumeration(["installment","recurring","unscheduled"]);timapi.constants.DialogFunctions=new timapi.constants.Enumeration(["showSignatureCapture","showDialog","sendCardCommand","printOnTerminal"]);timapi.constants.Reason=new timapi.constants.Enumeration(["ok","corr","stop","cardReader","autoConfirm","timeout","codeOk","codeNok","pinOk","pinNok","f1","f2","otherKey"]);timapi.constants.TransactionStatus=new timapi.constants.Enumeration(["busy","idle","waitForCard","readingCard","applicationSelection","waitForProceed","dccSelection","enterTip","pinEntry","signatureCapture","processing","waitForCommit"]);timapi.constants.ProcessingDisposition=new timapi.constants.Enumeration(["onEcr","onEft"]);timapi.constants.ReceiptRequestType=new timapi.constants.Enumeration(["reprint","list"]);timapi.constants.MerchantAction=new timapi.constants.Enumeration(["signature","none"]);timapi.constants.CounterType=new timapi.constants.Enumeration(["shift","balance"]);timapi.constants.Recipient=new timapi.constants.Enumeration(["merchant","cardholder","both"]);timapi.constants.EcrInfoType=new timapi.constants.Enumeration(["os","ecrApplication","eftApi","eftModule"]);
