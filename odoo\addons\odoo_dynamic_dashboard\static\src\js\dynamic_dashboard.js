/** @odoo-module **/

import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { Component, onWillStart, onMounted, useState } from "@odoo/owl";

class DynamicDashboard extends Component {
    setup() {
        this.orm = useService("orm");
        this.rpc = useService("rpc");
        this.actionService = useService("action");

        this.state = useState({
            dashboard: null,
            blocks: [],
            loading: true,
            error: null,
            theme: null,
        });

        onWillStart(async () => {
            await this.loadDashboard();
        });

        onMounted(() => {
            if (this.state.dashboard && this.state.dashboard.refresh_interval > 0) {
                this.refreshInterval = setInterval(() => {
                    this.refreshData();
                }, this.state.dashboard.refresh_interval * 1000);
            }
        });
    }

    async loadDashboard() {
        try {
            this.state.loading = true;
            const dashboardId = this.props.action.params.dashboard_id;

            const dashboard = await this.orm.read(
                "dashboard.dashboard",
                [dashboardId],
                ["name", "description", "layout", "theme", "refresh_interval"]
            );

            this.state.dashboard = dashboard[0];

            if (this.state.dashboard.theme) {
                const theme = await this.orm.read(
                    "dashboard.theme",
                    [this.state.dashboard.theme[0]],
                    ["background_color", "text_color", "tile_background_color", "tile_text_color", "accent_color", "is_dark"]
                );
                this.state.theme = theme[0];
                this.applyTheme();
            }

            const blocks = await this.orm.search_read(
                "dashboard.block",
                [["dashboard_id", "=", dashboardId]],
                ["name", "type", "width", "height", "data_x", "data_y", "background_color", "text_color",
                 "model_name", "domain", "operation", "measured_field_id",
                 "chart_type", "chart_data_model", "chart_data_domain", "chart_data_groupby", "chart_data_measure", "chart_data_limit"]
            );

            this.state.blocks = blocks;

            // Load data for each block
            for (const block of this.state.blocks) {
                await this.loadBlockData(block);
            }

            this.state.loading = false;
        } catch (error) {
            this.state.error = error.message || "Failed to load dashboard";
            this.state.loading = false;
        }
    }

    async loadBlockData(block) {
        try {
            if (block.type === 'kpi') {
                block.value = await this.getKpiValue(block);
            } else if (block.type === 'chart') {
                block.chartData = await this.getChartData(block);
            }
        } catch (error) {
            block.error = error.message || "Failed to load block data";
        }
    }

    async getKpiValue(block) {
        if (!block.model_name) {
            return 0;
        }

        const domain = block.domain ? JSON.parse(block.domain) : [];

        if (block.operation === 'count') {
            const count = await this.orm.search_count(block.model_name, domain);
            return count;
        } else {
            const field = block.measured_field_id ? block.measured_field_id[0] : false;
            if (!field) {
                return 0;
            }

            const fieldName = block.measured_field_id[1];

            const result = await this.rpc("/web/dataset/call_kw", {
                model: block.model_name,
                method: "read_group",
                args: [domain, [fieldName], []],
                kwargs: {
                    lazy: false,
                },
            });

            if (result.length === 0) {
                return 0;
            }

            const value = result[0][fieldName];

            switch (block.operation) {
                case 'sum':
                    return value || 0;
                case 'avg':
                    return value || 0;
                case 'min':
                    return value || 0;
                case 'max':
                    return value || 0;
                default:
                    return value || 0;
            }
        }
    }

    async getChartData(block) {
        if (!block.chart_data_model || !block.chart_data_groupby) {
            return null;
        }

        const domain = block.chart_data_domain ? JSON.parse(block.chart_data_domain) : [];
        const groupBy = block.chart_data_groupby;
        const measure = block.chart_data_measure ? block.chart_data_measure[1] : false;
        const limit = block.chart_data_limit || 10;

        let fields = [groupBy];
        if (measure) {
            fields.push(measure);
        }

        const result = await this.rpc("/web/dataset/call_kw", {
            model: block.chart_data_model,
            method: "read_group",
            args: [domain, fields, [groupBy]],
            kwargs: {
                lazy: false,
                limit: limit,
            },
        });

        const labels = result.map(r => r[groupBy] instanceof Array ? r[groupBy][1] : r[groupBy]);
        const data = measure ? result.map(r => r[measure]) : result.map(r => r.__count);

        return {
            labels: labels,
            datasets: [
                {
                    label: measure || "Count",
                    data: data,
                    backgroundColor: this.getChartColors(block.chart_type, labels.length),
                    borderColor: block.chart_type === 'line' ? this.state.theme?.accent_color || '#7c7bad' : undefined,
                    borderWidth: 1,
                },
            ],
        };
    }

    getChartColors(chartType, count) {
        const baseColors = [
            '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
            '#1abc9c', '#d35400', '#34495e', '#16a085', '#c0392b'
        ];

        if (chartType === 'line') {
            return this.state.theme?.accent_color || '#7c7bad';
        }

        // For pie, doughnut, etc. return array of colors
        const colors = [];
        for (let i = 0; i < count; i++) {
            colors.push(baseColors[i % baseColors.length]);
        }

        return colors;
    }

    applyTheme() {
        if (!this.state.theme) {
            return;
        }

        const theme = this.state.theme;
        const root = document.documentElement;

        root.style.setProperty('--dashboard-bg-color', theme.background_color);
        root.style.setProperty('--dashboard-text-color', theme.text_color);
        root.style.setProperty('--dashboard-tile-bg-color', theme.tile_background_color);
        root.style.setProperty('--dashboard-tile-text-color', theme.tile_text_color);
        root.style.setProperty('--dashboard-accent-color', theme.accent_color);

        if (theme.is_dark) {
            document.body.classList.add('o_dashboard_dark_mode');
        } else {
            document.body.classList.remove('o_dashboard_dark_mode');
        }
    }

    async refreshData() {
        for (const block of this.state.blocks) {
            await this.loadBlockData(block);
        }
    }

    formatValue(value) {
        if (typeof value !== 'number') {
            return value;
        }

        if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
        } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K';
        }

        return value.toString();
    }

    // Template event handlers
    onClickAdd(ev) {
        console.log('Add button clicked');
    }

    onClickAddItem(ev) {
        const type = ev.target.dataset.type;
        const chartType = ev.target.dataset.chart_type;
        console.log('Add item:', type, chartType);
    }

    editLayout(ev) {
        console.log('Edit layout clicked');
    }

    saveLayout(ev) {
        console.log('Save layout clicked');
    }

    dateFilter(ev) {
        console.log('Date filter changed:', ev.target.value);
    }

    onChangeTheme(ev) {
        console.log('Theme changed:', ev.target.value);
    }

    printPdf(ev) {
        console.log('Print PDF clicked');
    }

    sendMail(ev) {
        console.log('Send mail clicked');
    }

    clearSearch(ev) {
        const searchInput = document.getElementById('search-input-chart');
        if (searchInput) {
            searchInput.value = '';
        }
    }

    clickSearch(ev) {
        const searchInput = document.getElementById('search-input-chart');
        const searchValue = searchInput ? searchInput.value : '';
        console.log('Search clicked:', searchValue);
    }
}

DynamicDashboard.template = 'odoo_dynamic_dashboard.Dashboard';
DynamicDashboard.props = {
    action: Object,
};

registry.category("actions").add("odoo_dynamic_dashboard.dashboard", DynamicDashboard);


