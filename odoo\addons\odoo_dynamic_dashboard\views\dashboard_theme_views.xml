<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Dashboard Theme Form View -->
    <record id="view_dashboard_theme_form" model="ir.ui.view">
        <field name="name">dashboard.theme.form</field>
        <field name="model">dashboard.theme</field>
        <field name="arch" type="xml">
            <form string="Dashboard Theme">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Theme Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="background_color" widget="color"/>
                            <field name="text_color" widget="color"/>
                            <field name="is_dark"/>
                        </group>
                        <group>
                            <field name="tile_background_color" widget="color"/>
                            <field name="tile_text_color" widget="color"/>
                            <field name="accent_color" widget="color"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Dashboard Theme Tree View -->
    <record id="view_dashboard_theme_tree" model="ir.ui.view">
        <field name="name">dashboard.theme.tree</field>
        <field name="model">dashboard.theme</field>
        <field name="arch" type="xml">
            <tree string="Dashboard Themes">
                <field name="name"/>
                <field name="is_dark"/>
            </tree>
        </field>
    </record>

    <!-- Dashboard Theme Action -->
    <record id="action_dashboard_theme" model="ir.actions.act_window">
        <field name="name">Dashboard Themes</field>
        <field name="res_model">dashboard.theme</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first dashboard theme
            </p>
            <p>
                Dashboard themes allow you to customize the look and feel of your dashboards.
            </p>
        </field>
    </record>
</odoo>

