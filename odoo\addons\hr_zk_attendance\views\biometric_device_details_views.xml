<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--Biometric device configuration list view-->
    <record id="biometric_device_details_view_list" model="ir.ui.view">
        <field name="name">biometric.device.details.view.list</field>
        <field name="model">biometric.device.details</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="device_ip"/>
                <field name="port_number"/>
            </list>
        </field>
    </record>
    <!--Biometric device configuration form view-->
    <record id="biometric_device_details_view_form" model="ir.ui.view">
        <field name="name">biometric.device.details.view.form</field>
        <field name="model">biometric.device.details</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_set_timezone" string=" Set Time"
                            type="object" class="oe_highlight"/>
                    <button name="action_import_employees"
                            string="Import Employees"
                            type="object" class="oe_highlight"/>
                    <button name="action_download_attendance"
                            string="Download Data"
                            type="object" class="oe_highlight"/>
                    <button name="action_clear_attendance" string="Clear Data"
                            type="object" class="oe_highlight"
                            confirm="Are you sure you want to clear all
                            attendance records from the Device and Odoo?"/>
                    <button name="action_restart_device" string="Restart"
                            type="object" class="oe_highlight"
                            confirm="Are you sure you want Restart the Biometric
                            Device?"/>
                </header>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="device_ip"/>
                        <field name="port_number"/>
                        <field name="address_id"/>
                    </group>
                    <button name="action_test_connection"
                            type="object" class="btn btn-secondary">
                        <i class="fa fa-fw o_button_icon fa-television"/>
                        Test Connection
                    </button>
                </sheet>
            </form>
        </field>
    </record>
    <!--	Action for the biometric device-->
    <record id="biometric_device_details_action" model="ir.actions.act_window">
        <field name="name">Biometric Device</field>
        <field name="res_model">biometric.device.details</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>
