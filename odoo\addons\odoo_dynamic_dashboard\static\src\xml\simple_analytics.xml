<?xml version="1.0" encoding="UTF-8"?>
<templates id="simple_analytics_templates" xml:space="preserve">
    <t t-name="SimpleAnalyticsDashboard" owl="1">
        <div class="simple-analytics-dashboard p-4">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-0">
                        <t t-esc="state.title"/>
                    </h2>
                    <p class="text-muted mt-1">Real-time analytics and insights</p>
                </div>
                <div>
                    <button class="btn btn-primary" t-on-click="refreshData">
                        <i class="fa fa-refresh mr-1"></i>
                        Refresh
                    </button>
                </div>
            </div>

            <!-- Loading State -->
            <div t-if="state.loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-3 text-muted">Loading dashboard data...</p>
            </div>

            <!-- Error State -->
            <div t-if="state.error" class="alert alert-danger" role="alert">
                <h4 class="alert-heading">Error Loading Dashboard</h4>
                <p><t t-esc="state.error"/></p>
                <button class="btn btn-outline-danger" t-on-click="refreshData">
                    <i class="fa fa-redo mr-1"></i>
                    Try Again
                </button>
            </div>

            <!-- Dashboard Content -->
            <div t-if="!state.loading and !state.error" class="dashboard-content">
                <!-- KPI Widgets -->
                <div class="row mb-4">
                    <t t-foreach="state.widgets" t-as="widget" t-key="widget_index">
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div t-attf-class="card border-left-{{widget.color}} shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div t-attf-class="text-xs font-weight-bold text-{{widget.color}} text-uppercase mb-1">
                                                <t t-esc="widget.title"/>
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <t t-esc="widget.value"/>
                                            </div>
                                            <div class="text-xs text-muted mt-1">
                                                <span t-attf-class="text-{{widget.trend and widget.trend.startsWith('+') ? 'success' : 'danger'}}">
                                                    <t t-esc="widget.trend"/>
                                                </span>
                                                <t t-esc="widget.subtitle"/>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i t-attf-class="fas {{widget.icon}} fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>

                <!-- Charts Placeholder -->
                <div class="row">
                    <t t-foreach="state.charts" t-as="chart" t-key="chart_index">
                        <div t-attf-class="{{chart.size or 'col-lg-6'}} mb-4">
                            <div class="card shadow h-100">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <t t-esc="chart.title"/>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center py-5">
                                        <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                                        <p class="text-muted">Chart will be rendered here</p>
                                        <small class="text-muted">Type: <t t-esc="chart.type"/></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </div>

            <!-- Empty State -->
            <div t-if="!state.loading and !state.error and !state.widgets.length" class="text-center py-5">
                <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No Dashboard Data Available</h5>
                <p class="text-muted">Please check your configuration or try refreshing the page.</p>
                <button class="btn btn-primary" t-on-click="refreshData">
                    <i class="fas fa-sync-alt mr-1"></i>
                    Refresh Data
                </button>
            </div>
        </div>
    </t>
</templates>
