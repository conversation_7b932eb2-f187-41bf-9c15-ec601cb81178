<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="AnalyticsDashboard" owl="1">
        <div class="analytics-dashboard-container">
            <!-- Dashboard Header -->
            <div class="dashboard-header mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i t-if="state.dashboardType === 'overview'" class="fas fa-chart-area text-primary mr-2"></i>
                            <i t-if="state.dashboardType === 'sales'" class="fas fa-dollar-sign text-success mr-2"></i>
                            <i t-if="state.dashboardType === 'production'" class="fas fa-industry text-info mr-2"></i>
                            <i t-if="state.dashboardType === 'stock'" class="fas fa-boxes text-warning mr-2"></i>
                            <i t-if="state.dashboardType === 'maintenance'" class="fas fa-wrench text-danger mr-2"></i>
                            <i t-if="state.dashboardType === 'inventory'" class="fas fa-clipboard-list text-secondary mr-2"></i>
                            <i t-if="state.dashboardType === 'workshop'" class="fas fa-hammer text-dark mr-2"></i>
                            <i t-if="state.dashboardType === 'manufacturing'" class="fas fa-cogs text-primary mr-2"></i>
                            <t t-esc="state.dashboardTitle"/>
                        </h1>
                        <p class="text-muted mt-2">Real-time analytics and insights</p>
                    </div>
                    <div class="col-md-4 text-right">
                        <button class="btn btn-primary btn-sm mr-2" t-on-click="refreshData">
                            <i class="fas fa-sync-alt mr-1"></i>
                            Refresh
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" t-on-click="exportData">
                            <i class="fas fa-download mr-1"></i>
                            Export
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div t-if="state.loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-3 text-muted">Loading dashboard data...</p>
            </div>

            <!-- Dashboard Content -->
            <div t-if="!state.loading and state.dashboardData" class="dashboard-content">
                <!-- KPI Widgets Row -->
                <div class="row dashboard-row mb-4">
                    <t t-foreach="state.dashboardData.widgets" t-as="widget" t-key="widget_index">
                        <div class="col-xl-3 col-md-6 mb-3 widget_card">
                            <div t-attf-class="card border-left-{{widget.color}} shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center h-100">
                                        <div class="col mr-2">
                                            <div t-attf-class="text-xs font-weight-bold text-{{widget.color}} text-uppercase mb-1">
                                                <t t-esc="widget.title"/>
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <t t-esc="widget.value"/>
                                            </div>
                                            <div class="text-xs text-muted mt-1">
                                                <span t-attf-class="text-{{widget.trend and widget.trend.startsWith('+') ? 'success' : 'danger'}}">
                                                    <t t-esc="widget.trend"/>
                                                </span>
                                                <t t-esc="widget.subtitle"/>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i t-attf-class="{{widget.icon}} fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>

                <!-- Charts Row -->
                <div class="row dashboard-row">
                    <t t-foreach="state.dashboardData.charts" t-as="chart" t-key="chart_index">
                        <div t-attf-class="{{chart.size or 'col-lg-6'}} mb-4 chart_card">
                            <div class="card shadow h-100">
                                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <t t-esc="chart.title"/>
                                    </h6>
                                    <div class="dropdown no-arrow">
                                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                                            <div class="dropdown-header">Chart Actions:</div>
                                            <a class="dropdown-item" href="#" t-on-click="() => this.exportChart(chart)">Export Chart</a>
                                            <a class="dropdown-item" href="#" t-on-click="() => this.refreshChart(chart)">Refresh Data</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas t-attf-id="chart_{{chart_index}}" t-ref="chart_{{chart_index}}"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </div>

            <!-- Error State -->
            <div t-if="state.error" class="alert alert-danger" role="alert">
                <h4 class="alert-heading">Error Loading Dashboard</h4>
                <p><t t-esc="state.error"/></p>
                <hr>
                <p class="mb-0">
                    <button class="btn btn-outline-danger" t-on-click="refreshData">
                        <i class="fas fa-redo mr-1"></i>
                        Try Again
                    </button>
                </p>
            </div>

            <!-- Empty State -->
            <div t-if="!state.loading and !state.dashboardData and !state.error" class="text-center py-5">
                <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No Dashboard Data Available</h5>
                <p class="text-muted">Please check your configuration or try refreshing the page.</p>
                <button class="btn btn-primary" t-on-click="refreshData">
                    <i class="fas fa-sync-alt mr-1"></i>
                    Refresh Data
                </button>
            </div>
        </div>
    </t>
</templates>
