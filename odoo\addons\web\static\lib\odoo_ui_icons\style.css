@font-face {
  font-family: 'odoo_ui_icons';
  src: url('fonts/odoo_ui_icons.woff2') format('woff2'), url('fonts/odoo_ui_icons.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.oi {
  display: inline-block;
  font-family: 'odoo_ui_icons';
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.oi-view-pivot:before { content: '\e800'; }
.oi-text-break:before { content: '\e801'; }
.oi-text-inline:before { content: '\e802'; }
.oi-voip:before { content: '\e803'; }
.oi-odoo:before { content: '\e806'; }
.oi-search:before { content: '\e808'; }
.oi-group:before { content: '\e80a'; }
.oi-settings-adjust:before { content: '\e80c'; }
.oi-apps:before { content: '\e80d'; }
.oi-panel-right:before { content: '\e810'; }
.oi-launch:before { content: '\e812'; }
.oi-studio:before { content: '\e813'; }
.oi-view-kanban:before { content: '\e814'; }
.oi-text-wrap:before { content: '\e815'; }
.oi-view-cohort:before { content: '\e816'; }
.oi-view-list:before { content: '\e817'; }
.oi-gif-picker:before { content: '\e82e'; }
.oi-chevron-down:before { content: '\e839'; }
.oi-chevron-left:before { content: '\e83a'; }
.oi-chevron-right:before { content: '\e83b'; }
.oi-chevron-up:before { content: '\e83c'; }
.oi-arrows-h:before { content: '\e83d'; }
.oi-arrows-v:before { content: '\e83e'; }
.oi-arrow-down-left:before { content: '\e83f'; }
.oi-arrow-down-right:before { content: '\e840'; }
.oi-arrow-down:before { content: '\e841'; }
.oi-arrow-left:before { content: '\e842'; }
.oi-arrow-right:before { content: '\e843'; }
.oi-arrow-up-left:before { content: '\e844'; }
.oi-arrow-up-right:before { content: '\e845'; }
.oi-arrow-up:before { content: '\e846'; }
.oi-draggable:before { content: '\e847'; }
.oi-view:before { content: '\e861'; }
.oi-archive:before { content: '\e862'; }
.oi-unarchive:before { content: '\e863'; }
.oi-text-effect:before { content: '\e827'; }
.oi-smile-add:before { content: '\e84e'; }
.oi-close:before { content: '\e852'; }
.oi-food-delivery:before { content: '\e82a'; }

/* RTL adaptations. */
/* Flip directional icons by 180 degree. */
/* ---------------------------------------------------------------------------- */
.o_rtl .oi-chevron-left,
.o_rtl .oi-chevron-right,
.o_rtl .oi-arrow-down-left,
.o_rtl .oi-arrow-down-right,
.o_rtl .oi-arrow-left,
.o_rtl .oi-arrow-right,
.o_rtl .oi-arrow-up-left,
.o_rtl .oi-arrow-up-right {
  transform: rotate(180deg);
}
