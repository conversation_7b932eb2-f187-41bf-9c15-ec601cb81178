<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Dashboard Security Group -->
        <record id="group_dashboard_user" model="res.groups">
            <field name="name">Dashboard User</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
        
        <record id="group_dashboard_manager" model="res.groups">
            <field name="name">Dashboard Manager</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('group_dashboard_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>
</odoo>
