#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix OdooDynamicDashboard action tag references
Run this script in your Odoo shell to fix the immediate issue

Usage:
1. Start Odoo shell: python odoo-bin shell -d your_database_name
2. Copy and paste this script content
"""

# Fix existing dashboard actions with wrong tag
old_actions = env['ir.actions.client'].search([
    ('tag', '=', 'OdooDynamicDashboard')
])

print(f"Found {len(old_actions)} actions with old tag 'OdooDynamicDashboard'")

if old_actions:
    # Update them to use the correct tag
    old_actions.write({
        'tag': 'odoo_dynamic_dashboard.dashboard'
    })
    
    print(f"✅ Successfully updated {len(old_actions)} actions to use 'odoo_dynamic_dashboard.dashboard'")
    
    # Commit the changes
    env.cr.commit()
    print("✅ Changes committed to database")
else:
    print("ℹ️  No actions found with old tag - nothing to fix")

# Verify the fix
new_actions = env['ir.actions.client'].search([
    ('tag', '=', 'odoo_dynamic_dashboard.dashboard')
])

print(f"✅ Total actions now using correct tag: {len(new_actions)}")

# Check if any old tags remain
remaining_old = env['ir.actions.client'].search([
    ('tag', '=', 'OdooDynamicDashboard')
])

if remaining_old:
    print(f"⚠️  Warning: {len(remaining_old)} actions still use old tag")
else:
    print("✅ All actions now use the correct tag")

print("\n🎉 Fix completed! You can now restart your Odoo server and test the dashboard.")
