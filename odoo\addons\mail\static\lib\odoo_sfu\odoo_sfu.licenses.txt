Name: mediasoup-client
Version: 3.7.0
License: ISC
Private: false
Description: mediasoup client side JavaScript library
Repository: https://github.com/versatica/mediasoup-client.git
Homepage: https://mediasoup.org
Contributors:
  <PERSON><PERSON><PERSON> <<EMAIL>> (https://inakibaz.me)
  <PERSON> <<EMAIL>> (https://github.com/jmillan)
License Copyright:
===

ISC License

Copyright © 2015, <PERSON><PERSON><PERSON> <<EMAIL>>

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted, provided that the above
copyright notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.


---

Name: debug
Version: 4.3.4
License: MIT
Private: false
Description: Lightweight debugging utility for Node.js and the browser
Repository: git://github.com/debug-js/debug.git
Author: Josh Junon <<EMAIL>>
Contributors:
  TJ Holowaychuk <<EMAIL>>
  Nathan Rajlich <<EMAIL>> (http://n8.io)
  Andrew Rhyne <<EMAIL>>
License Copyright:
===

(The MIT License)

Copyright (c) 2014-2017 TJ Holowaychuk <<EMAIL>>
Copyright (c) 2018-2021 Josh Junon

Permission is hereby granted, free of charge, to any person obtaining a copy of this software
and associated documentation files (the 'Software'), to deal in the Software without restriction,
including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
portions of the Software.

THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.



---

Name: ms
Version: 2.1.2
License: MIT
Private: false
Description: Tiny millisecond conversion utility
Repository: undefined
License Copyright:
===

The MIT License (MIT)

Copyright (c) 2016 Zeit, Inc.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---

Name: ua-parser-js
Version: 1.0.37
License: MIT
Private: false
Description: Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data. Supports browser & node.js environment
Repository: https://github.com/faisalman/ua-parser-js.git
Homepage: https://github.com/faisalman/ua-parser-js
Author: Faisal Salman <<EMAIL>> (http://faisalman.com)
Contributors:
  Aamir Poonawalla <<EMAIL>>
  Admas <<EMAIL>>
  algenon <<EMAIL>>
  Alvin Portillo <<EMAIL>>
  Amumu <<EMAIL>>
  Andrea Vaghi <<EMAIL>>
  Anton Zhiyanov <<EMAIL>>
  Arturo Mejia <<EMAIL>>
  Arun Rama Reddy <<EMAIL>>
  Austin Pray <<EMAIL>>
  Bendeguz <<EMAIL>>
  Benjamin Bertrand <<EMAIL>>
  Benjamin Urban <<EMAIL>>
  boneyao <<EMAIL>>
  Carl C Von Lewin <<EMAIL>>
  CESAR RAMOS <<EMAIL>>
  Chad Killingsworth <<EMAIL>>
  Christopher De Cairos <<EMAIL>>
  Cyrille David <<EMAIL>>
  Dario Vladovic <<EMAIL>>
  David Annez <<EMAIL>>
  Davit Barbakadze <<EMAIL>>
  ddivernois <<EMAIL>>
  Deliaz <<EMAIL>>
  Demis Palma <<EMAIL>>
  dhoko <<EMAIL>>
  dianhe <<EMAIL>>
  dineshks1 <dineshks1@<EMAIL>>
  Dmitry Tyschenko <<EMAIL>>
  Douglas Li <<EMAIL>>
  Dumitru Uzun <<EMAIL>>
  Dustin <<EMAIL>>
  Elchin Valiyev <<EMAIL>>
  Emil Hesslow <<EMAIL>>
  Eric Redon <<EMAIL>>
  Eric Schrenker <<EMAIL>>
  Erik Hesselink <<EMAIL>>
  Fabian Becker <<EMAIL>>
  Faisal Salman <<EMAIL>>
  Frédéric Camblor <<EMAIL>>
  Frederik Ring <<EMAIL>>
  Gerald Host <<EMAIL>>
  Germán M. Bravo <<EMAIL>>
  Grigory Dmitrenko <<EMAIL>>
  gulpin <<EMAIL>>
  Hendrik Helwich <<EMAIL>>
  Hermann Ebert <<EMAIL>>
  hr6r <<EMAIL>>
  Igor Topal <<EMAIL>>
  Ildar Kamalov <<EMAIL>>
  insanehong <<EMAIL>>
  jackpoll <<EMAIL>>
  Jake Mc <<EMAIL>>
  JBYoshi <<EMAIL>>
  Joey Parrish <<EMAIL>>
  John Tantalo <<EMAIL>>
  John Yanarella <<EMAIL>>
  Jon Buckley <<EMAIL>>
  Josh Goldberg <<EMAIL>>
  Junki-Ishida <<EMAIL>>
  Kendall Buchanan <<EMAIL>>
  Lee Treveil <<EMAIL>>
  leonardo <<EMAIL>>
  Levente Balogh <<EMAIL>>
  Liam Quinn <<EMAIL>>
  Lithin <<EMAIL>>
  ll-syber <<EMAIL>>
  Loris Guignard <<EMAIL>>
  Lukas Drgon <<EMAIL>>
  Lukas Eipert <<EMAIL>>
  Malash <<EMAIL>>
  Martynas <<EMAIL>>
  Masahiko Sato <<EMAIL>>
  Matt Brophy <<EMAIL>>
  Matthew Origer <<EMAIL>>
  Maximilian Haupt <<EMAIL>>
  Max Maurer <<EMAIL>>
  Max Nordlund <<EMAIL>>
  Michael Hess <<EMAIL>>
  MimyyK <<EMAIL>>
  naoh <<EMAIL>>
  Nicholas Ionata <<EMAIL>>
  Nikhil Motiani <<EMAIL>>
  Nik Rolls <<EMAIL>>
  nionata <<EMAIL>>
  niris <<EMAIL>>
  Nobuo Okada <<EMAIL>>
  o.drapeza <<EMAIL>>
  otakuSiD <<EMAIL>>
  patrick-nurt <<EMAIL>>
  Pavel Studeny <<EMAIL>>
  Peter Dave Hello <<EMAIL>>
  philippsimon <<EMAIL>>
  Pieter Hendrickx <<EMAIL>>
  Piper Chester <<EMAIL>>
  Queen Vinyl Darkscratch <<EMAIL>>
  Raine Makelainen <<EMAIL>>
  Raman Savaryn <<EMAIL>>
  Robert Tod <<EMAIL>>
  roman.savarin <<EMAIL>>
  Ron Korland <<EMAIL>>
  Ross Noble <<EMAIL>>
  ruicong <<EMAIL>>
  Sandro Sonntag <<EMAIL>>
  sgautrea <<EMAIL>>
  Shane Gautreau <<EMAIL>>
  Shane Thacker <<EMAIL>>
  Shreedhar <<EMAIL>>
  Simon Eisenmann <<EMAIL>>
  Simon Lang <<EMAIL>>
  Stiekel <<EMAIL>>
  sUP <<EMAIL>>
  Sylvain Gizard <<EMAIL>>
  szchenghuang <<EMAIL>>
  Tanguy Krotoff <<EMAIL>>
  Tony Tomarchio <<EMAIL>>
  Ulrich Schmidt <<EMAIL>>
  Vadim Kurachevsky <<EMAIL>>
  Yılmaz <<EMAIL>>
  yuanyang <<EMAIL>>
  Yun Young-jin <<EMAIL>>
  Zach Bjornson <<EMAIL>>
License Copyright:
===

MIT License

Copyright (c) 2012-2023 Faisal Salman <<<EMAIL>>>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---

Name: events
Version: 3.3.0
License: MIT
Private: false
Description: Node's event emitter for all engines.
Repository: git://github.com/Gozala/events.git
Author: Irakli Gozalishvili <<EMAIL>> (http://jeditoolkit.com)
License Copyright:
===

MIT

Copyright Joyent, Inc. and other Node contributors.

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to permit
persons to whom the Software is furnished to do so, subject to the
following conditions:

The above copyright notice and this permission notice shall be included
in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
USE OR OTHER DEALINGS IN THE SOFTWARE.


---

Name: h264-profile-level-id
Version: 1.0.2
License: ISC
Private: false
Description: Utility to process H264 profile-level-id values
Repository: https://github.com/ibc/h264-profile-level-id.git
Author: Iñaki Baz Castillo <<EMAIL>> (https://inakibaz.me)
License Copyright:
===

ISC License

Copyright © 2019, Iñaki Baz Castillo <<EMAIL>>

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted, provided that the above
copyright notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.


---

Name: awaitqueue
Version: 3.0.2
License: ISC
Private: false
Description: JavaScript utility to enqueue asynchronous tasks and run them sequentially one after another
Repository: https://github.com/versatica/awaitqueue.git
Author: Iñaki Baz Castillo <<EMAIL>> (https://inakibaz.me)
License Copyright:
===

ISC License

Copyright © 2019, Iñaki Baz Castillo <<EMAIL>>

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted, provided that the above
copyright notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.


---

Name: queue-microtask
Version: 1.2.3
License: MIT
Private: false
Description: fast, tiny `queueMicrotask` shim for modern engines
Repository: git://github.com/feross/queue-microtask.git
Homepage: https://github.com/feross/queue-microtask
Author: Feross Aboukhadijeh <<EMAIL>> (https://feross.org)
License Copyright:
===

The MIT License (MIT)

Copyright (c) Feross Aboukhadijeh

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.


---

Name: sdp-transform
Version: 2.14.1
License: MIT
Private: false
Description: A simple parser/writer for the Session Description Protocol
Repository: clux/sdp-transform
Author: Eirik Albrigtsen <<EMAIL>>
License Copyright:
===

(The MIT License)

Copyright (c) 2013 Eirik Albrigtsen

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
'Software'), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.