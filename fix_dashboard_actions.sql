-- SQL script to fix OdooDynamicDashboard action tag references
-- Run this in your SOFABI_2025 database to fix the immediate issue

-- First, let's see what we have
SELECT id, name, tag
FROM ir_actions_client
WHERE tag LIKE '%Dashboard%' OR tag LIKE '%dashboard%';

-- Update all ir.actions.client records that use the old 'OdooDynamicDashboard' tag
UPDATE ir_actions_client
SET tag = 'odoo_dynamic_dashboard.dashboard'
WHERE tag = 'OdooDynamicDashboard';

-- Check the results
SELECT id, name, tag
FROM ir_actions_client
WHERE tag = 'odoo_dynamic_dashboard.dashboard';

-- Also check if there are any remaining old tags
SELECT id, name, tag
FROM ir_actions_client
WHERE tag = 'OdooDynamicDashboard';

-- Show all dashboard-related actions
SELECT id, name, tag, create_date, write_date
FROM ir_actions_client
WHERE tag LIKE '%dashboard%' OR name LIKE '%Dashboard%'
ORDER BY write_date DESC;
