
:root {
  /* Colors */
  --green: #00C689;
  --blue: #3DA5F4;
  --red: #F1536E;
  --yellow: #FDA006;
  /*Fonts*/
  --primary-font: 'Roboto', sans-serif;
}

html .o_web_client > .o_action_manager {
  overflow: auto;
}

.bg-green {
  background-color: var(--green);
}

.bg-blue {
  background-color: var(--blue);
}

.bg-red {
  background-color: var(--red);
}

.bg-yellow {
  background-color: var(--yellow);
}

.text-color-yellow {
  color: var(--yellow);
}

.text-color-green {
  color: var(--green);
}

.text-color-blue {
  color: var(--blue);
}

.text-color-red {
  color: var(--red);
}

.text-color-yellow {
  color: var(--yellow);
}

.tile-container__icon-container {
  border-radius: 50%;
  width: 4.75rem;
  height: 4.75rem;
  font-size: 28px;
  margin-left: 15px;
}

.tile-container__status-container {
  margin-left: 2em;
}

.status-container__title {
  font-family: var(--primary-font);
  font-weight: 500;
  font-size: 1.5rem;
  line-height: 1.5rem;
}

.status-container__figures {
  font-family: var(--primary-font);
}

.status-container__figures > h3 {
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 1.813rem;
}

.tile-container__setting-icon {
  top: 0.638rem;
}

// Styles généraux du tableau de bord
.dynamic-dashboard {
    font-family: 'Nunito', sans-serif;
    background-color: #f8f9fc;
    min-height: 100vh;
    
    .dashboard-container {
        padding: 1.5rem;
    }
    
    // Styles pour les tuiles KPI
    .resize-drag.tile {
        transition: all 0.3s ease;
        overflow: hidden;
        position: absolute;
        
        &:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        }
        
        .btn-light-hover {
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            
            &:hover {
                color: white;
                background: rgba(255, 255, 255, 0.1);
            }
        }
        
        .progress {
            background-color: rgba(255, 255, 255, 0.2);
            
            .progress-bar {
                background-color: rgba(255, 255, 255, 0.8);
            }
        }
    }
    
    // Styles pour les graphiques
    .chart-tile {
        transition: all 0.3s ease;
        position: absolute;
        
        &:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        }
    }
    
    // Styles pour l'en-tête du tableau de bord
    .dashboard-header {
        h1 {
            font-weight: 700;
            color: #4e73df;
        }
    }
    
    // Styles pour les filtres
    .dashboard-filters {
        background: white;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.03);
    }
    
    // Styles pour les boutons
    .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
        
        &:hover {
            background-color: #2e59d9;
            border-color: #2653d4;
        }
    }
    
    .btn-outline-primary {
        color: #4e73df;
        border-color: #4e73df;
        
        &:hover {
            background-color: #4e73df;
            border-color: #4e73df;
        }
    }
}

// Thème sombre
.dark-theme {
    background-color: #1a1a1a;
    color: #e0e0e0;
    
    .dashboard-container {
        background-color: #1a1a1a;
    }
    
    .dashboard-header h1 {
        color: #4e73df;
    }
    
    .dashboard-filters {
        background-color: #2a2a2a;
        color: #e0e0e0;
    }
    
    .resize-drag.tile {
        background: linear-gradient(135deg, #2c3e50 0%, #1a2530 100%) !important;
    }
    
    .chart-tile {
        background-color: #2a2a2a !important;
        color: #e0e0e0;
    }
    
    .btn-light {
        background-color: #3a3a3a;
        border-color: #3a3a3a;
        color: #e0e0e0;
        
        &:hover {
            background-color: #4a4a4a;
            border-color: #4a4a4a;
            color: #ffffff;
        }
    }
    
    // Styles spécifiques pour les graphiques en mode sombre
    canvas {
        filter: brightness(0.9);
    }
}

// Styles responsifs
@media (max-width: 768px) {
    .dynamic-dashboard {
        .dashboard-header {
            flex-direction: column;
            align-items: flex-start !important;
            
            > div:last-child {
                margin-top: 1rem;
                width: 100%;
                
                .btn {
                    width: 100%;
                    margin-bottom: 0.5rem;
                }
            }
        }
        
        .dashboard-filters .row {
            flex-direction: column;
            
            .col-md-4 {
                margin-top: 1rem;
            }
            
            .btn-group {
                width: 100%;
                
                .btn {
                    flex: 1;
                }
            }
        }
    }
}
.btn-align-items{
    width: 134px;
    font-size: small;
    border-radius: revert;
    height: 33px;
}
#edit_layout{
    background-color: #0c8444;
}
#save_layout{
    background-color: #b53c5d;
    width: 134px;
    height: 33px;
    font-size: small;
    border-radius: revert;
}
#search-button{
    width: 69px;
    margin-left: 5px;
}
#search-input-chart{
    width: 206px;
    height: 34px;
    border: 1px solid black;
}
.search-clear{
    margin-left: -80px;
}
label input{
    appearance: none;
}
.mode{
    padding-left: 7px;
    font-family: 'odoo_ui_icons';
    display: none;
}
.view-mode-icon{
    font-size: x-large;
}




