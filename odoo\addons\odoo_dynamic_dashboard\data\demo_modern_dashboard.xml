<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Modern Dashboard Demo Data -->
        
        <!-- Demo Dashboard -->
        <record id="demo_modern_dashboard" model="dashboard.dashboard">
            <field name="name">Dashboard Analytics Moderne</field>
            <field name="description">Tableau de bord interactif avec design moderne et animations</field>
            <field name="layout">grid</field>
            <field name="refresh_interval">300</field>
            <field name="active" eval="True"/>
        </record>

        <!-- Demo KPI Blocks -->
        <record id="demo_kpi_sales" model="dashboard.block">
            <field name="name">Ventes Totales</field>
            <field name="dashboard_id" ref="demo_modern_dashboard"/>
            <field name="type">kpi</field>
            <field name="width">300</field>
            <field name="height">160</field>
            <field name="data_x">0</field>
            <field name="data_y">0</field>
            <field name="background_color">#667eea</field>
            <field name="text_color">#ffffff</field>
            <field name="model_name">sale.order</field>
            <field name="domain">[['state', 'in', ['sale', 'done']]]</field>
            <field name="operation">sum</field>
        </record>

        <record id="demo_kpi_orders" model="dashboard.block">
            <field name="name">Commandes</field>
            <field name="dashboard_id" ref="demo_modern_dashboard"/>
            <field name="type">kpi</field>
            <field name="width">300</field>
            <field name="height">160</field>
            <field name="data_x">320</field>
            <field name="data_y">0</field>
            <field name="background_color">#f093fb</field>
            <field name="text_color">#ffffff</field>
            <field name="model_name">sale.order</field>
            <field name="domain">[['state', '!=', 'cancel']]</field>
            <field name="operation">count</field>
        </record>

        <record id="demo_kpi_customers" model="dashboard.block">
            <field name="name">Clients Actifs</field>
            <field name="dashboard_id" ref="demo_modern_dashboard"/>
            <field name="type">kpi</field>
            <field name="width">300</field>
            <field name="height">160</field>
            <field name="data_x">640</field>
            <field name="data_y">0</field>
            <field name="background_color">#4facfe</field>
            <field name="text_color">#ffffff</field>
            <field name="model_name">res.partner</field>
            <field name="domain">[['is_company', '=', False], ['customer_rank', '>', 0]]</field>
            <field name="operation">count</field>
        </record>

        <record id="demo_kpi_products" model="dashboard.block">
            <field name="name">Produits</field>
            <field name="dashboard_id" ref="demo_modern_dashboard"/>
            <field name="type">kpi</field>
            <field name="width">300</field>
            <field name="height">160</field>
            <field name="data_x">960</field>
            <field name="data_y">0</field>
            <field name="background_color">#a8edea</field>
            <field name="text_color">#2c3e50</field>
            <field name="model_name">product.product</field>
            <field name="domain">[['sale_ok', '=', True]]</field>
            <field name="operation">count</field>
        </record>

        <!-- Demo Chart Blocks -->
        <record id="demo_chart_sales_evolution" model="dashboard.block">
            <field name="name">Évolution des Ventes</field>
            <field name="dashboard_id" ref="demo_modern_dashboard"/>
            <field name="type">chart</field>
            <field name="chart_type">line</field>
            <field name="width">600</field>
            <field name="height">300</field>
            <field name="data_x">0</field>
            <field name="data_y">200</field>
            <field name="background_color">#ffffff</field>
            <field name="text_color">#2c3e50</field>
            <field name="model_name">sale.order</field>
            <field name="domain">[['state', 'in', ['sale', 'done']]]</field>
            <field name="chart_data_groupby">date_order:month</field>
            <field name="operation">sum</field>
        </record>

        <record id="demo_chart_top_products" model="dashboard.block">
            <field name="name">Top Produits</field>
            <field name="dashboard_id" ref="demo_modern_dashboard"/>
            <field name="type">chart</field>
            <field name="chart_type">bar</field>
            <field name="width">600</field>
            <field name="height">300</field>
            <field name="data_x">620</field>
            <field name="data_y">200</field>
            <field name="background_color">#ffffff</field>
            <field name="text_color">#2c3e50</field>
            <field name="model_name">sale.order.line</field>
            <field name="domain">[['order_id.state', 'in', ['sale', 'done']]]</field>
            <field name="chart_data_groupby">product_id</field>
            <field name="operation">sum</field>
            <field name="chart_data_limit">10</field>
        </record>

        <record id="demo_chart_sales_by_team" model="dashboard.block">
            <field name="name">Ventes par Équipe</field>
            <field name="dashboard_id" ref="demo_modern_dashboard"/>
            <field name="type">chart</field>
            <field name="chart_type">doughnut</field>
            <field name="width">600</field>
            <field name="height">300</field>
            <field name="data_x">0</field>
            <field name="data_y">520</field>
            <field name="background_color">#ffffff</field>
            <field name="text_color">#2c3e50</field>
            <field name="model_name">sale.order</field>
            <field name="domain">[['state', 'in', ['sale', 'done']]]</field>
            <field name="chart_data_groupby">team_id</field>
            <field name="operation">sum</field>
        </record>

        <record id="demo_chart_monthly_comparison" model="dashboard.block">
            <field name="name">Comparaison Mensuelle</field>
            <field name="dashboard_id" ref="demo_modern_dashboard"/>
            <field name="type">chart</field>
            <field name="chart_type">radar</field>
            <field name="width">600</field>
            <field name="height">300</field>
            <field name="data_x">620</field>
            <field name="data_y">520</field>
            <field name="background_color">#ffffff</field>
            <field name="text_color">#2c3e50</field>
            <field name="model_name">sale.order</field>
            <field name="domain">[['state', 'in', ['sale', 'done']]]</field>
            <field name="chart_data_groupby">date_order:month</field>
            <field name="operation">count</field>
            <field name="chart_data_limit">6</field>
        </record>

        <!-- Modern Theme -->
        <record id="modern_theme" model="dashboard.theme">
            <field name="name">Thème Moderne</field>
            <field name="color_x">#667eea</field>
            <field name="color_y">#764ba2</field>
            <field name="color_z">#f093fb</field>
            <field name="style">background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);</field>
            <field name="body">&lt;div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; color: white; text-align: center;"&gt;
                &lt;h3&gt;Thème Moderne&lt;/h3&gt;
                &lt;p&gt;Design moderne avec dégradés et animations&lt;/p&gt;
            &lt;/div&gt;</field>
        </record>

        <!-- Advanced Theme -->
        <record id="advanced_theme" model="dashboard.theme">
            <field name="name">Thème Avancé</field>
            <field name="color_x">#4facfe</field>
            <field name="color_y">#00f2fe</field>
            <field name="color_z">#a8edea</field>
            <field name="style">background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);</field>
            <field name="body">&lt;div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 20px; border-radius: 10px; color: white; text-align: center;"&gt;
                &lt;h3&gt;Thème Avancé&lt;/h3&gt;
                &lt;p&gt;Design avancé pour analytics professionnels&lt;/p&gt;
            &lt;/div&gt;</field>
        </record>

        <!-- Sunset Theme -->
        <record id="sunset_theme" model="dashboard.theme">
            <field name="name">Thème Sunset</field>
            <field name="color_x">#ff9a9e</field>
            <field name="color_y">#fecfef</field>
            <field name="color_z">#ffecd2</field>
            <field name="style">background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);</field>
            <field name="body">&lt;div style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); padding: 20px; border-radius: 10px; color: white; text-align: center;"&gt;
                &lt;h3&gt;Thème Sunset&lt;/h3&gt;
                &lt;p&gt;Couleurs chaudes et apaisantes&lt;/p&gt;
            &lt;/div&gt;</field>
        </record>

    </data>
</odoo>
