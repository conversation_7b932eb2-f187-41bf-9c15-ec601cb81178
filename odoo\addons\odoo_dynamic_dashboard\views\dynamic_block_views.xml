<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Dashboard Block Form View -->
    <record id="view_dashboard_block_form" model="ir.ui.view">
        <field name="name">dashboard.block.form</field>
        <field name="model">dashboard.block</field>
        <field name="arch" type="xml">
            <form string="Dashboard Block">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Block Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="dashboard_id"/>
                            <field name="type"/>
                        </group>
                        <group>
                            <field name="width"/>
                            <field name="height"/>
                            <field name="data_x"/>
                            <field name="data_y"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Data Configuration" name="data_config">
                            <group attrs="{'invisible': [('type', '!=', 'kpi')]}">
                                <group string="KPI Configuration">
                                    <field name="model_name"/>
                                    <field name="domain"/>
                                    <field name="operation"/>
                                    <field name="measured_field_id" 
                                           attrs="{'invisible': [('operation', '=', 'count')], 'required': [('operation', '!=', 'count')]}"
                                           domain="[('model_id.model', '=', model_name), ('ttype', 'in', ['integer', 'float', 'monetary'])]"/>
                                </group>
                            </group>
                            <group attrs="{'invisible': [('type', '!=', 'chart')]}">
                                <group string="Chart Configuration">
                                    <field name="chart_type"/>
                                    <field name="chart_data_model"/>
                                    <field name="chart_data_domain"/>
                                    <field name="chart_data_groupby"/>
                                    <field name="chart_data_measure" 
                                           domain="[('model_id.model', '=', chart_data_model), ('ttype', 'in', ['integer', 'float', 'monetary'])]"/>
                                    <field name="chart_data_limit"/>
                                </group>
                            </group>
                        </page>
                        <page string="Style" name="style">
                            <group>
                                <field name="background_color" widget="color"/>
                                <field name="text_color" widget="color"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Dashboard Block Tree View -->
    <record id="view_dashboard_block_tree" model="ir.ui.view">
        <field name="name">dashboard.block.tree</field>
        <field name="model">dashboard.block</field>
        <field name="arch" type="xml">
            <tree string="Dashboard Blocks">
                <field name="name"/>
                <field name="dashboard_id"/>
                <field name="type"/>
                <field name="model_name"/>
            </tree>
        </field>
    </record>

    <!-- Dashboard Block Action -->
    <record id="action_dashboard_block" model="ir.actions.act_window">
        <field name="name">Dashboard Blocks</field>
        <field name="res_model">dashboard.block</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first dashboard block
            </p>
            <p>
                Dashboard blocks are the building blocks of your dashboard.
            </p>
        </field>
    </record>
    <!--Menu Item for the model Dashboard Blocks-->
    <menuitem name="Dashboard Blocks" id="dashboard_block_menu"
              parent="odoo_dynamic_dashboard.menu_dashboard"
              sequence="5" action="action_dashboard_block"/>
</odoo>

