# -*- coding: utf-8 -*-
################################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>).
#    Author: B<PERSON>gyadev KP (<EMAIL>)
#
#    This program is free software: you can modify
#    it under the terms of the GNU Affero General Public License (AGPL) as
#    published by the Free Software Foundation, either version 3 of the
#    License, or (at your option) any later version.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU Affero General Public License for more details.
#
#    You should have received a copy of the GNU Affero General Public License
#    along with this program.  If not, see <https://www.gnu.org/licenses/>.
#
################################################################################
{
    'name': "Advanced Dynamic Dashboard Odoo18",
    'version': '********.2',
    'category': 'Productivity',
    'summary': """Odoo Dynamic Dashboard with OWL""",
    'description': """Configurable OWL-based Dashboard for Odoo 18""",
    'author': 'Cybrosys Techno Solutions',
    'website': "https://www.cybrosys.com",
    'license': "AGPL-3",
    'depends': ['web'],
    'data': [
        'security/dashboard_security.xml',
        'security/ir.model.access.csv',
        'security/ir_rule.xml',
        'data/dashboard_theme_data.xml',
        'data/demo_modern_dashboard.xml',
        'views/dashboard_views.xml',
        'views/dynamic_block_views.xml',
        'views/dashboard_menu_views.xml',
        'views/dashboard_theme_views.xml',
        'wizard/dashboard_mail_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            # Modern Fonts
            'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',

            # Libraries
            'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js',
            'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js',
            'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css',

            # Styles
            'odoo_dynamic_dashboard/static/src/css/dashboard.css',
            'odoo_dynamic_dashboard/static/src/css/modern_charts.css',
            'odoo_dynamic_dashboard/static/src/scss/**/*.scss',

            # Scripts
            'odoo_dynamic_dashboard/static/src/js/dynamic_dashboard.js',
            'odoo_dynamic_dashboard/static/src/js/dynamic_dashboard_chart.js',
            'odoo_dynamic_dashboard/static/src/js/dynamic_dashboard_tile.js',
            'odoo_dynamic_dashboard/static/src/js/interact_min.js',
            'odoo_dynamic_dashboard/static/src/js/modern_dashboard.js',

            # Templates
            'odoo_dynamic_dashboard/static/src/xml/dynamic_dashboard_template.xml',
            'odoo_dynamic_dashboard/static/src/xml/modern_kpi_cards.xml',
        ],
    },
    'images': ['static/description/banner.jpg'],
    'license': "AGPL-3",
    'installable': True,
    'auto_install': False,
    'application': True,
}
