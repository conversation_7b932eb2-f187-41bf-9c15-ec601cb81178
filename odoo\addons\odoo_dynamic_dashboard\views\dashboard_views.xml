<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Dashboard Form View -->
    <record id="view_dashboard_form" model="ir.ui.view">
        <field name="name">dashboard.dashboard.form</field>
        <field name="model">dashboard.dashboard</field>
        <field name="arch" type="xml">
            <form string="Dashboard">
                <header>
                    <button name="action_view_dashboard" string="View Dashboard" type="object" class="oe_highlight"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Dashboard Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="description"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="layout"/>
                            <field name="theme"/>
                            <field name="refresh_interval"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Blocks" name="blocks">
                            <field name="block_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="type"/>
                                    <field name="model_name"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Dashboard Tree View -->
    <record id="view_dashboard_tree" model="ir.ui.view">
        <field name="name">dashboard.dashboard.tree</field>
        <field name="model">dashboard.dashboard</field>
        <field name="arch" type="xml">
            <tree string="Dashboards">
                <field name="name"/>
                <field name="description"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Dashboard Action -->
    <record id="action_dashboard" model="ir.actions.act_window">
        <field name="name">Dashboards</field>
        <field name="res_model">dashboard.dashboard</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first dashboard
            </p>
            <p>
                Dashboards allow you to organize and visualize your data in a meaningful way.
            </p>
        </field>
    </record>

    <!-- Dashboard Client Action -->
    <record id="action_dashboard_client" model="ir.actions.client">
        <field name="name">Dynamic Dashboard</field>
        <field name="tag">odoo_dynamic_dashboard.dashboard</field>
        <field name="params">{}</field>
    </record>
</odoo>

