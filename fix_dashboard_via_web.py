"""
<PERSON><PERSON>t to fix OdooDynamicDashboard action tag via Odoo web interface
You can run this in the Odoo web interface under Settings > Technical > Server Actions
"""

# Create a new Server Action with this code:

# Find all client actions with old tag
old_actions = env['ir.actions.client'].search([
    ('tag', '=', 'OdooDynamicDashboard')
])

# Log what we found
_logger.info(f"Found {len(old_actions)} actions with old tag 'OdooDynamicDashboard'")

if old_actions:
    # Show details of what we're about to fix
    for action in old_actions:
        _logger.info(f"Action ID {action.id}: {action.name} - Tag: {action.tag}")
    
    # Update them to use the correct tag
    old_actions.write({
        'tag': 'odoo_dynamic_dashboard.dashboard'
    })
    
    _logger.info(f"✅ Successfully updated {len(old_actions)} actions to use 'odoo_dynamic_dashboard.dashboard'")
    
    # Return a message to the user
    return {
        'type': 'ir.actions.client',
        'tag': 'display_notification',
        'params': {
            'title': 'Dashboard Actions Fixed!',
            'message': f'Successfully updated {len(old_actions)} dashboard actions. You can now access your dashboards.',
            'type': 'success',
            'sticky': False,
        }
    }
else:
    _logger.info("ℹ️  No actions found with old tag - nothing to fix")
    return {
        'type': 'ir.actions.client',
        'tag': 'display_notification',
        'params': {
            'title': 'No Fix Needed',
            'message': 'No dashboard actions found with old tag. The issue might be elsewhere.',
            'type': 'info',
            'sticky': False,
        }
    }
