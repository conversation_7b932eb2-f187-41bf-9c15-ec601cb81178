[options]
# Database settings
db_host = localhost
db_port = 5432
db_user = odoo18
db_password = odoo18
db_name = odoo18_db

# Server settings
xmlrpc_port = 8069
longpolling_port = 8072

# Addons path
addons_path = odoo/addons,odoo/odoo/addons

# Admin password (change this!)
admin_passwd = admin

# Log settings
log_level = info
log_handler = :INFO

# Performance settings
workers = 0
max_cron_threads = 2

# Security settings
list_db = True

# File upload settings
data_dir = filestore

# Session settings
session_timeout = 3600

# Email settings (optional)
# email_from = <EMAIL>
# smtp_server = localhost
# smtp_port = 587
# smtp_user = 
# smtp_password = 
# smtp_ssl = False

# Development settings (remove in production)
dev_mode = reload,qweb,werkzeug,xml

# Additional options for Dynamic Dashboard
# Enable developer mode for easier debugging
# server_wide_modules = base,web

# Timezone (adjust to your location)
# timezone = UTC
