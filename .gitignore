# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Odoo specific
*.log
*.pid
filestore/
sessions/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Compiled files
*.pyc
*.pyo

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# Pytest
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.venv

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Large files that shouldn't be in Git
*.zip
*.tar.gz
*.rar
*.7z

# Odoo logs and sessions
odoo.log*
sessions/
filestore/

# Configuration files with sensitive data (keep odoo.conf but be careful)
# odoo.conf  # Uncomment if contains sensitive data
